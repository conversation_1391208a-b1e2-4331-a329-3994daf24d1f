name: juno_plus
description: A new Flutter project.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 0.0.1+24

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.22.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  design_system:
    path: ../design_system
  authentication:
    path: ../authentication
  bluetooth:
    path: ../bluetooth
  help_center:
    path: ../help_center
  notifications:
    path: ../notifications
  account_management:
    path: ../account_management
  remote:
    path: ../remote
  cloud_functions: ^6.0.1
  cupertino_icons: ^1.0.6
  syncfusion_flutter_gauges: ^25.2.5
  flutter_screenutil:
  date_picker_plus: 
  flutter_svg:
  lottie:
#  get: ^5.0.0-release-candidate-5
  get_it:
  auto_route: ^10.0.1
  another_xlider: ^3.0.2
  awesome_dialog:
  intl:
  fl_chart: ^0.68.0
  flutter_launcher_icons:
  carousel_slider_x: ^6.0.2
  flutter_local_notifications: ^19.0.0
  firebase_messaging: ^16.0.1
  doso: ^1.1.1
  image_picker:
  cached_network_image:
  firebase_app_check: ^0.4.0+1
  shared_preferences: ^2.0.6
  firebase_core: ^4.1.0
  skeletonizer: ^1.3.0
  chewie: ^1.8.1
  video_player: ^2.9.2
  timeago:
  fluttertoast: ^8.2.6
  #https://pub.dev/packages/upgrader
  upgrader: ^11.3.0
  connectivity_plus:
  calendar_date_picker2: ^1.1.5
  table_calendar: ^3.1.2
  flutter_hooks: ^0.21.1-pre.4
  dart_style: ^3.0.1
  timezone:
  showcaseview: ^3.0.0
#  firebase_remote_config: 5.4.0
#  firebase_remote_config_platform_interface: ^1.5.2
  package_info_plus: ^8.1.1
  xml: 6.5.0
  #remove_after_use:
  flutter_blue_plus:
  google_sign_in_ios: ^6.1.0
  rive:
  flutter_slidable: ^4.0.0
  timeline_tile: ^2.0.0
  smooth_page_indicator: ^1.2.1
  url_launcher: ^6.3.1
  share_plus: ^10.1.2
  font_awesome_flutter: ^10.8.0
  crypto: ^3.0.3

dev_dependencies:
  integration_test:
    sdk: flutter
  flutter_test:
    sdk: flutter
  change_app_package_name: ^1.1.0
  build_runner: ^2.4.15
  auto_route_generator: ^10.0.1
  injectable_generator:
  patrol: 3.14.1

dependency_overrides:
  watcher: 1.1.0
  flutter_lints: ^5.0.0
  cloud_firestore: ^6.0.1
  xml: 6.5.0
  patrol: 3.14.1
  # Add these overrides to fix AppCheckCore version conflicts
  firebase_app_check: ^0.4.0+1

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/logo/logo.png"
  min_sdk_android: 21

patrol:
  app_name: juno_plus
  flavor: Dev
  android:
    package_name: com.junotechno.juno_plus.dev
  ios:
    bundle_id: com.junotechno.juno_plus.dev


flutter:
  uses-material-design: true
  assets:
    - assets/welcome_page/
    - assets/logo/
    - assets/social_icons/
    - assets/remote/
    - assets/home/
    - assets/firmware/
    - assets/settings/
    - assets/rive/
    - assets/product_showcase/
    - assets/onboarding/