import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:account_management/application/period_reminder_settings_bloc/period_reminder_settings_bloc.dart';
import 'package:account_management/domain/model/period_reminder_settings.dart';
import '../period_reminder_settings_dialog.dart';
import 'settings_section.dart';

class RemindersSection extends StatelessWidget {
  const RemindersSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SettingsSection(
      title: 'Reminders',
      children: [
        // Period Reminder
        BlocBuilder<PeriodReminderSettingsBloc, PeriodReminderSettingsState>(
          builder: (context, reminderState) {
            final settings = reminderState.maybeWhen(
              loaded: (settings) => settings,
              saved: (settings) => settings,
              orElse: () => PeriodReminderSettings.empty(),
            );

            return _buildReminderItem(
              context: context,
              icon: 'assets/settings/period.svg',
              title: 'Period Reminder',
              subtitle: settings.isPeriodReminderEnabled
                  ? '${settings.periodReminderDaysBefore} ${settings.periodReminderDaysBefore == 1 ? 'day' : 'days'} before'
                  : 'Tap to configure',
              isEnabled: settings.isPeriodReminderEnabled,
              onTap: () async {
                final result = await showDialog<PeriodReminderSettings>(
                  context: context,
                  builder: (context) => PeriodReminderSettingsDialog(
                    initialSettings: settings,
                    reminderType: 'period',
                  ),
                );

                if (result != null) {
                  context.read<PeriodReminderSettingsBloc>().add(
                        PeriodReminderSettingsEvent.togglePeriodReminder(
                            result.isPeriodReminderEnabled),
                      );
                  context.read<PeriodReminderSettingsBloc>().add(
                        PeriodReminderSettingsEvent.updatePeriodReminderDays(
                            result.periodReminderDaysBefore),
                      );
                  context.read<PeriodReminderSettingsBloc>().add(
                        const PeriodReminderSettingsEvent.saveSettings(),
                      );
                }
              },
              onToggle: (value) {
                context.read<PeriodReminderSettingsBloc>().add(
                      PeriodReminderSettingsEvent.togglePeriodReminder(value),
                    );
                context.read<PeriodReminderSettingsBloc>().add(
                      const PeriodReminderSettingsEvent.saveSettings(),
                    );
              },
            );
          },
        ),
        
        const SizedBox(height: 10),
        const Divider(thickness: 1),
        
        // Ovulation Reminder
        BlocBuilder<PeriodReminderSettingsBloc, PeriodReminderSettingsState>(
          builder: (context, reminderState) {
            final settings = reminderState.maybeWhen(
              loaded: (settings) => settings,
              saved: (settings) => settings,
              orElse: () => PeriodReminderSettings.empty(),
            );

            return _buildReminderItem(
              context: context,
              icon: 'assets/settings/ovulation.svg',
              title: 'Ovulation Reminder',
              subtitle: settings.isOvulationReminderEnabled
                  ? '${settings.ovulationReminderDaysBefore} ${settings.ovulationReminderDaysBefore == 1 ? 'day' : 'days'} before'
                  : 'Tap to configure',
              isEnabled: settings.isOvulationReminderEnabled,
              onTap: () async {
                final result = await showDialog<PeriodReminderSettings>(
                  context: context,
                  builder: (context) => PeriodReminderSettingsDialog(
                    initialSettings: settings,
                    reminderType: 'ovulation',
                  ),
                );

                if (result != null) {
                  context.read<PeriodReminderSettingsBloc>().add(
                        PeriodReminderSettingsEvent.toggleOvulationReminder(
                            result.isOvulationReminderEnabled),
                      );
                  context.read<PeriodReminderSettingsBloc>().add(
                        PeriodReminderSettingsEvent.updateOvulationReminderDays(
                            result.ovulationReminderDaysBefore),
                      );
                  context.read<PeriodReminderSettingsBloc>().add(
                        const PeriodReminderSettingsEvent.saveSettings(),
                      );
                }
              },
              onToggle: (value) {
                context.read<PeriodReminderSettingsBloc>().add(
                      PeriodReminderSettingsEvent.toggleOvulationReminder(value),
                    );
                context.read<PeriodReminderSettingsBloc>().add(
                      const PeriodReminderSettingsEvent.saveSettings(),
                    );
              },
            );
          },
        ),
        
        const SizedBox(height: 10),
        const Divider(thickness: 1),
        
        // Battery Reminder
        _buildBatteryItem(context),
      ],
    );
  }

  Widget _buildReminderItem({
    required BuildContext context,
    required String icon,
    required String title,
    required String subtitle,
    required bool isEnabled,
    required VoidCallback onTap,
    required void Function(bool) onToggle,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.only(left: 10, right: 4),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 30,
              height: 30,
              child: SvgPicture.asset(
                icon,
                width: 30,
                height: 30,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(width: 20),
            Container(
              width: .35.sw,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            fontSize: 18,
                            color: const Color(0xff26204a),
                            fontWeight: FontWeight.w400,
                          ),
                    ),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontSize: 16,
                            color: const Color(0xff26204a),
                            fontWeight: FontWeight.w100,
                          ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              width: .20.sw,
              child: Switch(
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                value: isEnabled,
                onChanged: onToggle,
                thumbIcon: WidgetStateProperty.resolveWith((Set<WidgetState> states) {
                  if (states.contains(WidgetState.selected)) {
                    return const Icon(Icons.check, color:  AppTheme.primaryColor);
                  }
                  return null;
                }),
                thumbColor: WidgetStateProperty.resolveWith((Set<WidgetState> states) {
                  return Colors.white;
                }),
                activeColor: AppTheme.primaryColor,
                activeTrackColor: AppTheme.primaryColor,
                trackOutlineWidth: WidgetStateProperty.resolveWith((Set<WidgetState> states) {
                  return 10;
                }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBatteryItem(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // TODO: Implement battery reminder functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Battery reminder feature coming soon!'),
            backgroundColor:  AppTheme.primaryColor,
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.only(left: 10, right: 4),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            SizedBox(
              width: 30,
              height: 30,
              child: SvgPicture.asset(
                'assets/remote/battery_icon.svg',
                width: 30,
                height: 30,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(width: 25),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Battery',
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontSize: 18,
                          color: const Color(0xff26204a),
                          fontWeight: FontWeight.w400,
                        ),
                  ),
                  Text(
                    'Tap to configure',
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          fontSize: 16,
                          color: const Color(0xff26204a),
                          fontWeight: FontWeight.w100,
                        ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 20,
              color:  AppTheme.primaryColor,
            ),
          ],
        ),
      ),
    );
  }
}
