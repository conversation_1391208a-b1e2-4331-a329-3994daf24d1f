import 'package:account_management/application/update_health_data_bloc/update_health_data_bloc.dart';
import 'package:date_picker_plus/date_picker_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:design_system/design/theme.dart';
import 'package:google_fonts/google_fonts.dart';
import '../onboarding/onboarding_slider.dart';

class UpdateCycleLengthDialog extends StatefulWidget {
    final int cycleLength ;
   UpdateCycleLengthDialog({required this.cycleLength, Key? key}) : super(key: key);

  @override
  State<UpdateCycleLengthDialog> createState() => _UpdateCycleLengthDialogState();
}

class _UpdateCycleLengthDialogState extends State<UpdateCycleLengthDialog> {
  int cycleLength = 28;
  @override
  void initState() {
    cycleLength = widget.cycleLength;
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UpdateHealthDataBloc, UpdateHealthDataState>(
      builder: (context, state) {
        return AlertDialog(
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppTheme.loginAppBarColor,
          title:  Center(
            child: Text('Your Cycle Length',style:Theme.of(context).textTheme.headlineMedium!.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 20
            ),),
          ),
           content:
           SizedBox(
             height: .8.sw,
             child: Card(
               margin: EdgeInsets.symmetric(vertical: 20, horizontal: 10),
               color: Colors.white,
               shape: RoundedRectangleBorder(
                 borderRadius: BorderRadius.circular(20),
               ),
               child: Padding(
                 padding: const EdgeInsets.all(15),
                 child: Column(
                   children: [
                     SizedBox(height: 5),
                     Text(
                       'Slide to Edit Your Cycle Length',
                       textAlign: TextAlign.center,
                       style: TextStyle(
                         color: Colors.black,
                         fontSize: 16,
                         fontWeight: FontWeight.w500,
                       ),
                     ),
                     SizedBox(
                       height: 5,
                     ),
                     SizedBox(
                       width: 330,
                       height: 90,
                       child: CustomIntSlider(
                         value: cycleLength,
                         min: 10,
                         max: 50,
                         colour: Color(0xFF9D8BCD),
                         onChanged: (val) {
                           setState(() => cycleLength = val);
                         },
                       ),
                     ),
                     SizedBox(
                       height: 5,
                     ),
                     Text(
                       'The average cycle length is between 21 - 40 days.',
                       textAlign: TextAlign.center,
                       style: TextStyle(
                         color: Colors.black,
                         fontSize: 15,
                       ),
                     ),
                     SizedBox(
                       height: 5,
                     ),
                   ],
                 ),
               ),
             ),
           ),
          actions: <Widget>[
            OutlinedButton(
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: Color(0xFF584294), width: 2),
                foregroundColor: const Color(0xFF584294),
                padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Cancel',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF584294),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 14,horizontal: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                elevation: 3,
              ),
              onPressed: () {
                BlocProvider.of<UpdateHealthDataBloc>(context).add(UpdateCycleLength(cycleLength));
                Fluttertoast.showToast(msg: 'Cycle length updated successfully');
                Navigator.of(context).pop();
              },
              child: Text(
                'Confirm',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  letterSpacing: 0.5,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}



class UpdatePeriodLengthDialog extends StatefulWidget {
   final int periodLength;
  UpdatePeriodLengthDialog({required this.periodLength, Key? key}) : super(key: key);

  @override
  State<UpdatePeriodLengthDialog> createState() => _UpdatePeriodLengthDialogState();
}


class _UpdatePeriodLengthDialogState extends State<UpdatePeriodLengthDialog> {
  int periodLength = 3;

  @override
  void initState() {
   periodLength = widget.periodLength;
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UpdateHealthDataBloc, UpdateHealthDataState>(
      builder: (context, state) {
        return AlertDialog(
          contentPadding: EdgeInsets.zero,
          backgroundColor: AppTheme.loginAppBarColor,
          title:  Center(
            child: Text('Your Period Length',style:Theme.of(context).textTheme.headlineMedium!.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 20
            ),),
          ),
           content: SizedBox(
             height: 250,
             child: Card(
               margin: EdgeInsets.symmetric(vertical: 20, horizontal: 10),
               color: Colors.white,
               shape: RoundedRectangleBorder(
                 borderRadius: BorderRadius.circular(20),
               ),
               child: Padding(
                 padding: const EdgeInsets.all(15),
                 child: Column(
                   children: [
                     SizedBox(height: 5),
                     Text(
                       'Slide to Edit Your Period Days',
                       textAlign: TextAlign.center,
                       style: TextStyle(
                         color: Colors.black,
                         fontSize: 16,
                         fontWeight: FontWeight.w500,
                       ),
                     ),
                     SizedBox(
                       height: 5,
                     ),
                     SizedBox(
                       width: 330,
                       height: 90,
                       child: CustomIntSlider(
                         value: periodLength,
                         min: 2,
                         max: 10,
                         colour: Color(0xFFF4BA4A),
                         onChanged: (val) {
                           setState(() => periodLength = val);
                         },
                       ),
                     ),
                     SizedBox(
                       height: 5,
                     ),
                     Text(
                       'The average period usually lasts 3-5 days.',
                       textAlign: TextAlign.center,
                       style: TextStyle(
                         color: Colors.black,
                         fontSize: 15,
                       ),
                     ),
                     SizedBox(
                       height: 5,
                     ),
                   ],
                 ),
               ),
             ),
           ),

          actions: <Widget>[
            OutlinedButton(
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: Color(0xFF584294), width: 2),
                foregroundColor: const Color(0xFF584294),
                padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Cancel',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF584294),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 14,horizontal: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                elevation: 3,
              ),
              onPressed: () {
                BlocProvider.of<UpdateHealthDataBloc>(context)
                    .add(UpdatePeriodLength(periodLength));
                Fluttertoast.showToast(msg: 'Period length updated successfully');
                Navigator.of(context).pop();
              },
              child: Text(
                'Confirm',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  letterSpacing: 0.5,
                ),
              ),
            ),

          ],
        );
      },
    );
  }
}


class UpdateOvulationDayDialog extends StatefulWidget {
  final DateTime ovulationDate;
  UpdateOvulationDayDialog({required this.ovulationDate, Key? key}) : super(key: key);

  @override
  State<UpdateOvulationDayDialog> createState() => _UpdateOvulationDayDialogState();
}

class _UpdateOvulationDayDialogState extends State<UpdateOvulationDayDialog> {
  DateTime ovulationDate = DateTime.now();

  @override
  void initState() {
    ovulationDate = widget.ovulationDate;
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UpdateHealthDataBloc, UpdateHealthDataState>(
      builder: (context, state) {
        return AlertDialog(
          backgroundColor: AppTheme.loginAppBarColor,
          title: Center(
            child: Text('Ovulation Date', style: Theme
                .of(context)
                .textTheme
                .headlineMedium!
                .copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 18
            ),),
          ),
          content:  SizedBox(
            width: .7.sw,
            height: 240,
            child: DatePicker(
              initialDate: ovulationDate,
              minDate: DateTime(1960, 10, 10),
              maxDate: DateTime.now(),
              padding: EdgeInsets.zero,
              currentDate: DateTime.now(),
              selectedDate: ovulationDate,
              onDateSelected: (date) {
                ovulationDate = date;
              },
              currentDateDecoration: const BoxDecoration(),
              currentDateTextStyle:
              GoogleFonts.roboto(color: const Color(0xff71456F)),
              daysOfTheWeekTextStyle: const TextStyle(
                color: Color(0xff71456F),
                fontWeight: FontWeight.bold,
                fontSize: 13,
              ),
              disabledCellsDecoration: const BoxDecoration(),
              disabledCellsTextStyle: const TextStyle(),
              enabledCellsDecoration: const BoxDecoration(),
              enabledCellsTextStyle:
              GoogleFonts.roboto(color: const Color(0xff71456F)),
              initialPickerType: PickerType.days,
              selectedCellDecoration: BoxDecoration(
                color: Theme
                    .of(context)
                    .primaryColor,
                shape: BoxShape.circle,
              ),
              selectedCellTextStyle: GoogleFonts.roboto(
                color: const Color(0xffFBF0D5),
                fontWeight: FontWeight.w400,
                fontSize: 12,
              ),
              leadingDateTextStyle:
              GoogleFonts.roboto(color: const Color(0xff71456F)),
              slidersColor: const Color(0xff71456F),
              highlightColor: const Color(0xff71456F),
              slidersSize: 20,
              splashColor: Theme
                  .of(context)
                  .primaryColor
                  .withOpacity(0.5),
              splashRadius: 0,
              centerLeadingDate: true,
            ),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                BlocProvider.of<UpdateHealthDataBloc>(context).add(
                    UpdateHealthDataEvent.updateOvulationDate(ovulationDate));
                Fluttertoast.showToast(
                    msg: 'Ovulation day updated successfully');
                Navigator.of(context).pop();
              },
              child: const Text('Confirm'),
            )

          ],
        );
      },
    );
  }
  }