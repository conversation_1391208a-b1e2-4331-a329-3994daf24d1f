import 'package:auto_route/auto_route.dart';
import 'package:bluetooth/bluetooth.dart';
import 'package:bluetooth/domain/failure/bluetooth_failure.dart';
import 'package:bluetooth/infrastructure/firmware_manager.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:remote/application/device_status_watcher_bloc/device_status_watcher_bloc.dart';
import 'package:remote/application/device_control_bloc/device_control_bloc.dart'
    as remote;

import '../../custom_widgets/curved_app_bar.dart';
import '../../custom_widgets/battery_indicator.dart';
import '../../helpers.dart';

import 'components/settings_section.dart';
import 'components/common_settings_tile.dart';

@RoutePage()
class DeviceSettingsPage extends StatefulWidget {
  const DeviceSettingsPage({Key? key}) : super(key: key);

  @override
  State<DeviceSettingsPage> createState() => _DeviceSettingsPageState();
}

class _DeviceSettingsPageState extends State<DeviceSettingsPage> {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<BluetoothServiceBloc>()
            ..add(const BluetoothServiceEvent.checkBluetooth()),
        ),
        BlocProvider(
          create: (context) => getIt<OtaUpdateBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<DeviceStatusWatcherBloc>()
            ..add(const DeviceStatusWatcherEvent.watchAllStarted()),
        ),
      ],
      child: BlocListener<remote.DeviceControlBloc, remote.DeviceControlState>(
        listener: (context, state) {
          if (state is remote.DeviceUnpaired) {
            print(
                '🎯 DeviceUnpaired state received - showing snackbar and navigating back');
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Device has been unpaired successfully'),
                backgroundColor: Colors.green,
              ),
            );
            // Trigger bluetooth service bloc to move to landing page state
            context.read<BluetoothServiceBloc>().add(const MoveToLandingPage());

            // Pop back to previous page immediately after showing snackbar
            WidgetsBinding.instance.addPostFrameCallback((_) {
              print('🎯 Attempting to pop back from device settings page');
              if (context.mounted) {
                print('🎯 Context is mounted, calling router.pop()');
                context.router.pop();
              } else {
                print('❌ Context is not mounted, cannot pop');
              }
            });
          }
          // Note: Removed ActionFailure handling to prevent showing "Failed to unpair"
          // for therapy state watching failures that occur on page load.
          // Users will know if unpair actually failed because the device will still be connected.
        },
        child: Scaffold(
          appBar: CurvedAppBar(
            appBarColor: AppTheme.primaryColor,
            logoColor: const Color(0xffFAF2DF),
            height: .35.sw,
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(25.0),
              child: Column(
                children: [
                  SizedBox(height: .35.sw),

                  // Device Information Section
                  _buildDeviceInfoSection(),

                  const SizedBox(height: 35),

                  // Firmware Information Section
                  _buildFirmwareInfoSection(),

                  const SizedBox(height: 35),

                  // Software Update Section
                  _buildSoftwareUpdateSection(),

                  const SizedBox(height: 35),

                  // Device Management Section
                  _buildDeviceManagementSection(),

                  SizedBox(height: .25.sw),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDeviceInfoSection() {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, bluetoothState) {
        if (bluetoothState is Connected) {
          final device = bluetoothState.device;
          return SettingsSection(
            title: 'Device Information',
            children: [
              // Device Name
              _buildDeviceInfoItem(
                icon: Icons.device_hub,
                title: 'Device Name',
                subtitle: device.platformName.isNotEmpty
                    ? device.platformName
                    : 'Juno Device',
              ),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),

              // Battery Level
              _buildBatteryInfoItem(),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),

              // Device ID
              _buildDeviceInfoItem(
                icon: Icons.fingerprint,
                title: 'Device ID',
                subtitle: device.remoteId.toString(),
              ),
            ],
          );
        } else {
          return SettingsSection(
            title: 'Device Information',
            children: [
              _buildDeviceInfoItem(
                icon: Icons.device_unknown,
                title: 'No Device Connected',
                subtitle: 'Connect a device to view information',
              ),
            ],
          );
        }
      },
    );
  }

  Widget _buildSoftwareUpdateSection() {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, bluetoothState) {
        if (bluetoothState is Connected) {
          final device = bluetoothState.device;
          return SettingsSection(
            title: 'Software Update',
            children: [
              BlocConsumer<OtaUpdateBloc, OtaUpdateState>(
                listener: (context, state) {
                  // Handle state changes without maybeWhen
                  // For now, we'll skip the listener functionality
                },
                builder: (context, otaState) {
                  // Show default check for updates since maybeWhen is not available
                  return _buildSoftwareUpdateItem(
                    title: 'Check for Updates',
                    subtitle: 'Tap to check for firmware updates',
                    onTap: () {
                      context.read<OtaUpdateBloc>().add(
                            OtaUpdateEvent.checkForUpdates(device),
                          );
                    },
                  );
                },
              ),
            ],
          );
        } else {
          return SettingsSection(
            title: 'Software Update',
            children: [
              _buildSoftwareUpdateItem(
                title: 'Device Not Connected',
                subtitle: 'Connect a device to check for updates',
              ),
            ],
          );
        }
      },
    );
  }

  Widget _buildDeviceManagementSection() {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, bluetoothState) {
        if (bluetoothState is Connected) {
          final device = bluetoothState.device;
          return SettingsSection(
            title: 'Device Management',
            children: [
              _buildDeviceManagementItem(
                iconPath: 'assets/settings/history.svg',
                title: 'Update History',
                subtitle: 'View previous firmware updates',
                onTap: () {
                  context.read<OtaUpdateBloc>().add(
                        const OtaUpdateEvent.loadUpdateHistory(),
                      );
                  _showUpdateHistoryDialog(context);
                },
              ),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),
              _buildDeviceManagementItem(
                iconPath: 'assets/settings/reset.svg',
                title: 'Reset Device',
                subtitle: 'Reset device to factory settings',
                onTap: () => _showResetDeviceDialog(context, device),
              ),
              const SizedBox(height: 10),
              const Divider(thickness: 1, height: 7),
              const SizedBox(height: 10),
              _buildUnpairDeviceItem(device),
            ],
          );
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildDeviceInfoItem({
    required IconData icon,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
    Color? iconColor,
    Widget? trailing,
  }) {
    return CommonSettingsTile(
      icon: Icon(icon, color: iconColor ?? AppTheme.primaryColor),
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      trailing: trailing ??
          (onTap != null
              ? const Icon(Icons.arrow_forward_ios, size: 16)
              : null),
    );
  }

  Widget _buildBatteryInfoItem() {
    return BlocBuilder<DeviceStatusWatcherBloc, DeviceStatusWatcherState>(
      builder: (context, deviceState) {
        final batteryLevel =
            deviceState.deviceInformation.batteryLevel?.batteryLevel ?? 0;
        final batteryPercentage = (batteryLevel / 100).clamp(0.0, 1.0);

        return _buildDeviceInfoItem(
          icon: Icons.battery_std,
          iconColor: _getBatteryColor(batteryLevel),
          title: 'Battery Level',
          subtitle: '$batteryLevel%',
          trailing: SizedBox(
            width: 60,
            height: 20,
            child: BatteryIndicator(
              value: batteryPercentage,
              barColor: _getBatteryColor(batteryLevel),
            ),
          ),
        );
      },
    );
  }

  Color _getBatteryColor(int batteryLevel) {
    if (batteryLevel > 50) return Colors.green;
    if (batteryLevel > 20) return Colors.orange;
    return Colors.red;
  }

  Widget _buildFirmwareInfoSection() {
    return BlocBuilder<BluetoothServiceBloc, BluetoothServiceState>(
      builder: (context, bluetoothState) {
        if (bluetoothState is Connected) {
          final device = bluetoothState.device;
          // Automatically trigger firmware info loading when connected
          WidgetsBinding.instance.addPostFrameCallback((_) {
            final otaBloc = context.read<OtaUpdateBloc>();
            if (otaBloc.state.runtimeType.toString() == 'Initial') {
              otaBloc.add(OtaUpdateEvent.checkDeviceFirmware(device));
            }
          });

          return SettingsSection(
            title: 'Firmware Information',
            children: [
              BlocBuilder<OtaUpdateBloc, OtaUpdateState>(
                builder: (context, otaState) {
                  // For now, show loading state since we can't use maybeWhen
                  return Column(
                    children: [
                      _buildDeviceInfoItem(
                        icon: Icons.memory,
                        title: 'Current Version',
                        subtitle: 'Loading...',
                      ),
                      const SizedBox(height: 10),
                      const Divider(thickness: 1, height: 7),
                      const SizedBox(height: 10),
                      _buildDeviceInfoItem(
                        icon: Icons.computer,
                        title: 'Hardware Version',
                        subtitle: 'Loading...',
                      ),
                      const SizedBox(height: 10),
                      const Divider(thickness: 1, height: 7),
                      const SizedBox(height: 10),
                      _buildDeviceInfoItem(
                        icon: Icons.system_update,
                        title: 'OTA Support',
                        subtitle: 'Loading...',
                      ),
                    ],
                  );
                },
              ),
            ],
          );
        } else {
          return SettingsSection(
            title: 'Firmware Information',
            children: [
              _buildDeviceInfoItem(
                icon: Icons.device_unknown,
                title: 'No Device Connected',
                subtitle: 'Connect a device to view firmware details',
              ),
            ],
          );
        }
      },
    );
  }

  Widget _buildSoftwareUpdateItem({
    required String title,
    required String subtitle,
    VoidCallback? onTap,
    bool isLoading = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: isLoading
                  ? const Center(
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    )
                  : const Icon(
                      Icons.system_update,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (onTap != null && !isLoading)
              const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpdateProgressItem(OtaProgress progress) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.system_update,
                color: AppTheme.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Text(
                  'Updating Firmware',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Text(
            progress.status,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 10),
          LinearProgressIndicator(
            value: progress.percentage / 100,
            backgroundColor: Colors.grey[300],
            valueColor:
                const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 8),
          Text(
            '${progress.percentage.toStringAsFixed(1)}% (${progress.bytesTransferred} / ${progress.totalBytes} bytes)',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnpairDeviceItem(BluetoothDevice device) {
    return BlocBuilder<remote.DeviceControlBloc, remote.DeviceControlState>(
      builder: (context, state) {
        final isUnpairing = state is remote.UnpairingDevice;

        return _buildDeviceManagementItem(
          iconPath: 'assets/settings/unpair.svg',
          title: 'Unpair Device',
          subtitle: isUnpairing
              ? 'Unpairing device...'
              : 'Remove device from saved devices',
          onTap: isUnpairing
              ? null
              : () => _showUnpairDeviceDialog(context, device),
        );
      },
    );
  }

  Widget _buildDeviceManagementItem({
    required String iconPath,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
  }) {
    return CommonSettingsTile(
      icon: Icon(Icons.settings, color: AppTheme.primaryColor),
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      trailing:
          onTap != null ? const Icon(Icons.arrow_forward_ios, size: 16) : null,
    );
  }

  String _getErrorMessage(BluetoothFailure failure) {
    // Simple error message since maybeWhen is not available
    return 'An error occurred during the update. Please try again.';
  }

  void _showUpdateDialog(BuildContext context, BluetoothDevice device,
      FirmwareInfo updateInfo, OtaUpdateBloc otaBloc) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Firmware Update Available'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Version: ${updateInfo.version}'),
            Text('Size: ${(updateInfo.size / 1024).toStringAsFixed(0)} KB'),
            Text('Build Date: ${updateInfo.buildDate}'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withOpacity(0.3)),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.security, color: Colors.orange, size: 20),
                      SizedBox(width: 8),
                      Text(
                        'Security Notice',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    'This device uses application protection for cybersecurity compliance. '
                    'The update process will perform a full chip erase to clear protection flags. '
                    'Ensure your device is fully charged (>50%) before proceeding.',
                    style: TextStyle(fontSize: 13),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This update will improve device performance and add new features. '
              'Stay within Bluetooth range during the entire update process.',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              try {
                print('🔍 [UI DEBUG] Update button clicked');

                // Load the real DFU application package
                print('🔍 [UI DEBUG] Loading firmware package...');
                final firmwareData =
                    await FirmwareManager.loadFirmwarePackage();
                print(
                    '✅ [UI DEBUG] Firmware loaded: ${firmwareData.length} bytes');

                print('🔍 [UI DEBUG] Starting OTA update via bloc...');
                otaBloc.add(
                  OtaUpdateEvent.startOtaUpdate(device, firmwareData),
                );
                print('✅ [UI DEBUG] OTA update event added to bloc');
              } catch (e) {
                print('❌ [UI DEBUG] Error in update button: $e');
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to load firmware: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _showUpdateHistoryDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update History'),
        content: BlocBuilder<OtaUpdateBloc, OtaUpdateState>(
          builder: (context, state) {
            // Show loading indicator since maybeWhen is not available
            return const Center(child: CircularProgressIndicator());
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showUnpairDeviceDialog(BuildContext context, BluetoothDevice device) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Unpair Device'),
          content: Text(
            'Are you sure you want to unpair "${device.platformName.isNotEmpty ? device.platformName : 'Juno Device'}"? This will remove the device from your saved devices and disconnect it.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                print(
                    '🎯 Unpair button pressed - closing dialog and triggering unpair event');
                Navigator.of(context).pop();
                print('🎯 Adding unpair event to DeviceControlBloc');
                context.read<remote.DeviceControlBloc>().add(
                      const remote.DeviceControlEvent.unpairDevice(),
                    );
                print('🎯 Unpair event added to DeviceControlBloc');
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Unpair'),
            ),
          ],
        );
      },
    );
  }

  void _showResetDeviceDialog(BuildContext context, BluetoothDevice device) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Device'),
        content: const Text(
          'This will reset your device to factory settings. All custom settings will be lost. '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // In a real implementation, send reset command to device
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content:
                      Text('Device reset functionality not implemented yet'),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Reset', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
