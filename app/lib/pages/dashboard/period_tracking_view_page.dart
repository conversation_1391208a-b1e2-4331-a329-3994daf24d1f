import 'dart:io';

import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:account_management/application/symptom_tracking_bloc/symptom_tracking_bloc.dart';
import 'package:account_management/application/symptom_watcher_bloc/symptom_watcher_bloc.dart';
import 'package:account_management/domain/model/symptom_model.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import '../../custom_widgets/dotted_border_widget.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../helpers.dart';
import 'package:intl/intl.dart';
import 'package:design_system/design_system.dart';
import 'daily_symptom_page.dart';

@RoutePage()
class PeriodTrackingViewPage extends StatelessWidget {
  const PeriodTrackingViewPage({super.key});

  @override
  Widget build(BuildContext context) {
    final currentYear = DateTime.now().year;
    print('📱 PeriodTrackingViewPage: Building with year $currentYear');

    return BlocProvider<PeriodTrackingWatcherBloc>(
      create: (context) {
        print('📱 PeriodTrackingViewPage: Creating PeriodTrackingWatcherBloc');
        final bloc = getIt<PeriodTrackingWatcherBloc>();
        print(
            '📱 PeriodTrackingViewPage: Adding watchYearStarted event for year $currentYear');
        bloc.add(PeriodTrackingWatcherEvent.watchYearStarted(currentYear));
        return bloc;
      },
      child: PeriodTrackingViewScaffold(),
    );
  }
}

class PeriodTrackingViewScaffold extends StatefulWidget {
  const PeriodTrackingViewScaffold({super.key});

  @override
  State<PeriodTrackingViewScaffold> createState() =>
      _PeriodTrackingViewScaffoldState();
}

// Optimized date state cache for performance
class DateState {
  final bool isTodayDate;
  final bool isFutureDate;
  final bool isSelectedPeriodDate;
  final bool isFirstPeriodDate;
  final bool isLastPeriodDate;
  final bool isInPeriodWindow;
  final bool isOvulationDate;
  final bool isFirstOvulationDate;
  final bool isLastOvulationDate;
  final bool isInOvulationWindow;
  final bool isFilledGapDate; // New field for filled gap dates

  const DateState({
    this.isTodayDate = false,
    this.isFutureDate = false,
    this.isSelectedPeriodDate = false,
    this.isFirstPeriodDate = false,
    this.isLastPeriodDate = false,
    this.isInPeriodWindow = false,
    this.isOvulationDate = false,
    this.isFirstOvulationDate = false,
    this.isLastOvulationDate = false,
    this.isInOvulationWindow = false,
    this.isFilledGapDate = false,
  });
}

class _PeriodTrackingViewScaffoldState
    extends State<PeriodTrackingViewScaffold> {
  List<DateTime> _months = [];
  int _selectedYear = DateTime.now().year;
  late ScrollController _scrollController;

  // Cache for period cycles to avoid recalculation
  List<List<DateTime>>? _cachedPeriodCycles;
  Set<DateTime>? _cachedPeriodDates;

  @override
  void initState() {
    super.initState();
    _months = _getMonthsToDisplay(_selectedYear);
    _scrollController = ScrollController();

    // Auto-scroll to today's month after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToTodaysMonth();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToTodaysMonth() {
    final today = DateTime.now();
    if (today.year == _selectedYear) {
      // Find the index of today's month (0-based)
      final todayMonthIndex = today.month - 1;

      if (todayMonthIndex >= 0 && todayMonthIndex < _months.length) {
        // Check if scroll controller is attached to avoid assertion error
        if (_scrollController.hasClients) {
          // Calculate scroll position more accurately
          // Each month card is approximately 450 pixels including margins
          final cardHeight = 450.0;
          final scrollPosition = todayMonthIndex * cardHeight;

          // Add a delay to ensure the ListView is fully rendered
          Future.delayed(Duration(milliseconds: 200), () {
            if (_scrollController.hasClients && mounted) {
              // Ensure we don't scroll beyond the maximum scroll extent
              final maxScrollExtent =
                  _scrollController.position.maxScrollExtent;
              final targetPosition = scrollPosition > maxScrollExtent
                  ? maxScrollExtent
                  : scrollPosition;

              _scrollController.animateTo(
                targetPosition,
                duration: Duration(milliseconds: 1000),
                curve: Curves.easeInOut,
              );
            }
          });
        }
      }
    }
  }

  void _showSymptomBottomSheet(BuildContext context, DateTime selectedDate) {
    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: false,
      enableDrag: true,
      builder: (BuildContext bottomSheetContext) {
        return BlocProvider<SymptomTrackingBloc>(
          create: (context) => getIt<SymptomTrackingBloc>()
            ..add(SymptomTrackingEvent.loadSymptomData(date: selectedDate)),
          child: _SymptomBottomSheet(selectedDate: selectedDate),
        );
      },
    );
  }

  // Generate list of months to display for a specific year
  List<DateTime> _getMonthsToDisplay(int year) {
    List<DateTime> months = [];

    // Add all 12 months of the selected year
    for (int month = 1; month <= 12; month++) {
      months.add(DateTime(year, month, 1));
    }

    return months;
  }

  // Get available years (current year and past 5 years)
  List<int> _getAvailableYears() {
    final currentYear = DateTime.now().year;
    List<int> years = [];

    for (int i = 0; i < 6; i++) {
      years.add(currentYear - i);
    }

    return years;
  }

  // Show year selector bottom sheet
  void _showYearSelector(BuildContext context) {
    // Store the parent context that has access to the bloc
    final parentContext = context;

    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext bottomSheetContext) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.4,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(30.r),
              topRight: Radius.circular(30.r),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                spreadRadius: 0,
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 10.h,
              ),
              // Handle bar
              Container(
                margin: EdgeInsets.only(top: 12.h, left: 0.5.sw - 30.w),
                width: 60.w,
                height: 4.h,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
              // Title
              Padding(
                padding: EdgeInsets.all(20.w),
                child: Text(
                  'Select Year',
                  style: GoogleFonts.roboto(
                    fontSize: 35.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
              // Year list
              Expanded(
                child: ListView.builder(
                  itemCount: _getAvailableYears().length,
                  itemBuilder: (context, index) {
                    final year = _getAvailableYears()[index];
                    final isSelected = year == _selectedYear;

                    return GestureDetector(
                      onTap: () {
                        if (year != _selectedYear) {
                          setState(() {
                            _selectedYear = year;
                            _months = _getMonthsToDisplay(_selectedYear);
                          });
                          parentContext.read<PeriodTrackingWatcherBloc>().add(
                                PeriodTrackingWatcherEvent.watchYearStarted(
                                    year),
                              );
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            _scrollToTodaysMonth();
                          });
                        }
                        Navigator.pop(bottomSheetContext);
                      },
                      child: Container(
                        margin: EdgeInsets.symmetric(
                            horizontal: 20.w, vertical: 10.h),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? AppTheme.primaryColor.withOpacity(0.1)
                              : Colors.white,
                          borderRadius: BorderRadius.circular(25.r),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.08),
                              blurRadius: 12,
                              offset: Offset(0, 4),
                            ),
                          ],
                          border: Border.all(
                            color: isSelected
                                ? AppTheme.primaryColor
                                : Colors.grey[300]!,
                            width: isSelected ? 2 : 1,
                          ),
                        ),
                        child: ListTile(
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 24.w, vertical: 8.h),
                          title: Text(
                            year.toString(),
                            style: GoogleFonts.roboto(
                              fontSize: 30.sp,
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.w400,
                              color: isSelected
                                  ? AppTheme.primaryColor
                                  : Colors.black87,
                            ),
                          ),
                          trailing: isSelected
                              ? Icon(Icons.check, color: AppTheme.primaryColor)
                              : null,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Performance-optimized period cycle calculation with caching
  List<List<DateTime>> _getPeriodCycles(Set<DateTime> periodDates) {
    // Use cache if data hasn't changed
    if (_cachedPeriodDates != null &&
        _cachedPeriodCycles != null &&
        _cachedPeriodDates!.length == periodDates.length &&
        _cachedPeriodDates!.containsAll(periodDates)) {
      return _cachedPeriodCycles!;
    }

    // Calculate cycles only when data changes
    final cycles = _calculatePeriodCycles(periodDates);

    // Update cache
    _cachedPeriodDates = Set.from(periodDates);
    _cachedPeriodCycles = cycles;

    return cycles;
  }

  // Efficient cycle calculation - runs only when data changes
  List<List<DateTime>> _calculatePeriodCycles(Set<DateTime> periodDates) {
    if (periodDates.isEmpty) return [];

    // Sort dates chronologically (only once)
    final sortedDates = periodDates.toList()..sort();

    List<List<DateTime>> cycles = [];
    List<DateTime> currentCycle = [];

    for (int i = 0; i < sortedDates.length; i++) {
      final currentDate = sortedDates[i];

      if (currentCycle.isEmpty) {
        currentCycle.add(currentDate);
      } else {
        final lastDate = currentCycle.last;
        final daysDifference = currentDate.difference(lastDate).inDays;

        if (daysDifference <= 2) {
          // Consecutive day or single-day gap - add to current cycle
          currentCycle.add(currentDate);
        } else {
          // Gap of 2+ days found - end current cycle and start new one
          cycles.add(List.from(currentCycle));
          currentCycle = [currentDate];
        }
      }
    }

    // Add the last cycle
    if (currentCycle.isNotEmpty) {
      cycles.add(currentCycle);
    }

    return cycles;
  }

  // Generate visual period dates by filling single-day gaps for UI display only
  Set<DateTime> _generateVisualPeriodDates(Set<DateTime> actualPeriodDates) {
    if (actualPeriodDates.isEmpty) return actualPeriodDates;

    final visualDates = Set<DateTime>.from(actualPeriodDates);
    final sortedDates = actualPeriodDates.toList()..sort();

    // Fill single-day gaps (1-day gap = 2 days difference)
    for (int i = 0; i < sortedDates.length - 1; i++) {
      final currentDate = sortedDates[i];
      final nextDate = sortedDates[i + 1];
      final daysDifference = nextDate.difference(currentDate).inDays;

      // If there's exactly a 1-day gap (2 days difference), fill it
      if (daysDifference == 2) {
        final gapDate = currentDate.add(const Duration(days: 1));
        visualDates.add(gapDate);
      }
    }

    return visualDates;
  }

  // Fast date comparison - inline for performance
  bool _isSameDate(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  // Get ovulation windows (groups of consecutive ovulation dates)
  List<List<DateTime>> _getOvulationWindows(Set<DateTime> ovulationDates) {
    // Use cache if data hasn't changed
    if (_cachedPeriodDates != null &&
        _cachedPeriodCycles != null &&
        _cachedPeriodDates!.length == ovulationDates.length &&
        _cachedPeriodDates!.containsAll(ovulationDates)) {
      return _cachedPeriodCycles!;
    }

    // Calculate windows only when data changes
    final windows = _calculateOvulationWindows(ovulationDates);

    return windows;
  }

  // Calculate ovulation windows from ovulation dates
  List<List<DateTime>> _calculateOvulationWindows(
      Set<DateTime> ovulationDates) {
    if (ovulationDates.isEmpty) return [];

    // Sort dates chronologically
    final sortedDates = ovulationDates.toList()..sort();

    List<List<DateTime>> windows = [];
    List<DateTime> currentWindow = [];

    for (int i = 0; i < sortedDates.length; i++) {
      final currentDate = sortedDates[i];

      if (currentWindow.isEmpty) {
        currentWindow.add(currentDate);
      } else {
        final lastDate = currentWindow.last;
        final daysDifference = currentDate.difference(lastDate).inDays;

        if (daysDifference <= 1) {
          // Consecutive day - add to current window
          currentWindow.add(currentDate);
        } else {
          // Gap found - end current window and start new one
          windows.add(List.from(currentWindow));
          currentWindow = [currentDate];
        }
      }
    }

    // Add the last window
    if (currentWindow.isNotEmpty) {
      windows.add(currentWindow);
    }

    return windows;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CurvedAppBar(
        appBarColor: AppTheme.primaryColor,
        logoColor: const Color(0xffFAF2DF),
        height: .35.sw,
        subtitle: 'Insights',
        subtitleColor: const Color(0xffFAF2DF),
        topLeftIcon: Container(
          height: 40,
          width: 40,
          decoration: BoxDecoration(
            color: const Color(0xffFAF2DF),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back,
                color: AppTheme.primaryColor, size: 20),
            onPressed: () {
              context.router.pop();
            },
          ),
        ),
        topRightIcon: Builder(
          builder: (context) => GestureDetector(
            onTap: () => _showYearSelector(context),
            child: Container(
              height: .08.sw,
              padding: EdgeInsets.symmetric(vertical: 8.h),
              decoration: BoxDecoration(
                // Golden color
                borderRadius: BorderRadius.circular(50),
                border: Border.all(color: const Color(0xffFAF2DF), width: 2),
                boxShadow: [],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(width: 18.w),
                  Text(
                    _selectedYear.toString(),
                    style: GoogleFonts.roboto(
                      color: const Color(0xffFAF2DF),
                      fontSize: 30.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: const Color(0xffFAF2DF),
                    size: 23,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body: BlocBuilder<PeriodTrackingWatcherBloc, PeriodTrackingWatcherState>(
        builder: (context, state) {
          print(
              '📱 PeriodTrackingViewPage: BlocBuilder received state: ${state.runtimeType}');

          return state.maybeMap(
            loadSuccess: (dataState) {
              print(
                  '📱 PeriodTrackingViewPage: LoadSuccess state with ${dataState.yearData.keys.length} months');

              // Extract period and ovulation dates from the year data
              final periodDates = <DateTime>{};
              final ovulationDates = <DateTime>{};

              for (final monthEntry in dataState.yearData.entries) {
                print(
                    '📱 Processing month ${monthEntry.key} with ${monthEntry.value.keys.length} days');

                for (final dayEntry in monthEntry.value.entries) {
                  final periodModel = dayEntry.value;

                  if (periodModel.isPeriodDate == true &&
                      periodModel.date != null) {
                    periodDates.add(periodModel.date!);
                  }

                  if (periodModel.isOvulationDate == true &&
                      periodModel.date != null) {
                    ovulationDates.add(periodModel.date!);
                  }
                }
              }

              print(
                  '📱 PeriodTrackingViewPage: Extracted ${periodDates.length} period dates and ${ovulationDates.length} ovulation dates');

              // Add future predictions if available
              if (dataState.futurePredictions != null) {
                final futurePeriods = dataState.futurePredictions!['periods'];
                final futureOvulations =
                    dataState.futurePredictions!['ovulations'];

                if (futurePeriods != null) {
                  periodDates.addAll(futurePeriods);
                  print(
                      '📱 Added ${futurePeriods.length} predicted period dates from bloc');
                }

                if (futureOvulations != null) {
                  ovulationDates.addAll(futureOvulations);
                  print(
                      '📱 Added ${futureOvulations.length} predicted ovulation dates from bloc');
                }
              } else {
                // Predictions will be calculated asynchronously by the bloc
                print(
                    '📱 No predictions available yet, showing current data only');
              }

              return Stack(
                children: [
                  // Gradient background
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Color(0xffFBF0D5), // Light cream
                            Color(0xffF8EEFF), // Light purple
                          ],
                        ),
                      ),
                    ),
                  ),
                  // Main content
                  ListView.builder(
                    controller: _scrollController,
                    padding: EdgeInsets.only(
                      top: .05.sw + MediaQuery.of(context).padding.top,
                      left: 16.w,
                      right: 16.w,
                      bottom: 8.h,
                    ),
                    itemCount: _months.length,
                    itemBuilder: (context, index) {
                      return _buildOptimizedMonthCalendar(
                          _months[index], periodDates, ovulationDates);
                    },
                  ),
                ],
              );
            },
            orElse: () {
              print(
                  '📱 PeriodTrackingViewPage: OrElse case - showing loading indicator');
              return Padding(
                padding: EdgeInsets.only(
                  top: .35.sw + MediaQuery.of(context).padding.top,
                ),
                child: const Center(
                  child: CircularProgressIndicator(
                    color: AppTheme.primaryColor,
                  ),
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: Container(
        margin: EdgeInsets.only(bottom: 20.h),
        child: FloatingActionButton.extended(
          onPressed: () {
            print(
                'PeriodTrackingViewPage: Navigating to edit with year $_selectedYear');
            final route = PeriodTrackingEditRoute(initialYear: _selectedYear);
            print(
                'PeriodTrackingViewPage: Created route with initialYear: ${route.args?.initialYear}');
            context.router.push(route);
          },
          backgroundColor: AppTheme.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50),
          ),
          label: Text(
            'Edit',
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 30.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          icon: const Icon(
            Icons.edit,
            color: Colors.white,
            size: 23,
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  // Optimized month calendar builder using simple widgets
  Widget _buildOptimizedMonthCalendar(
      DateTime month, Set<DateTime> periodDates, Set<DateTime> ovulationDates) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      child: Container(
        margin: EdgeInsets.only(bottom: 16.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(32),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              spreadRadius: 2,
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 40.w, horizontal: 10.w),
          child: Column(
            children: [
              // Month header
              Text(
                DateFormat('MMMM yyyy').format(month),
                style: GoogleFonts.inter(
                  fontSize: 34.sp,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
              SizedBox(height: 12.h),
              Divider(),
              SizedBox(height: 16.h),
              // Days of week header
              _buildDaysOfWeekHeader(),
              SizedBox(height: 8.h),
              // Calendar grid
              _buildCalendarGrid(month, periodDates, ovulationDates),
            ],
          ),
        ),
      ),
    );
  }

  // Days of week header
  Widget _buildDaysOfWeekHeader() {
    final daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return Row(
      children: daysOfWeek
          .map((day) => Expanded(
                child: Center(
                  child: Text(
                    day,
                    style: GoogleFonts.roboto(
                      fontSize: 25.sp,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ))
          .toList(),
    );
  }

  // Optimized calendar grid using simple Row/Column widgets
  Widget _buildCalendarGrid(
      DateTime month, Set<DateTime> periodDates, Set<DateTime> ovulationDates) {
    final firstDayOfMonth = DateTime(month.year, month.month, 1);
    final lastDayOfMonth = DateTime(month.year, month.month + 1, 0);
    final daysInMonth = lastDayOfMonth.day;
    final firstWeekday = firstDayOfMonth.weekday % 7; // Sunday = 0

    List<Widget> weeks = [];
    List<Widget> currentWeek = [];

    // Add empty cells for days before the first day of the month
    for (int i = 0; i < firstWeekday; i++) {
      currentWeek.add(Expanded(child: Container()));
    }

    // Add days of the month
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(month.year, month.month, day);
      currentWeek.add(Expanded(
          child: _buildOptimizedDayCell(date, periodDates, ovulationDates)));

      // If we've filled a week (7 days), start a new week
      if (currentWeek.length == 7) {
        weeks.add(Row(children: List.from(currentWeek)));
        currentWeek.clear();
      }
    }

    // Fill remaining cells in the last week
    while (currentWeek.length < 7) {
      currentWeek.add(Expanded(child: Container()));
    }
    if (currentWeek.isNotEmpty) {
      weeks.add(Row(children: List.from(currentWeek)));
    }

    return Column(children: weeks);
  }

  // Optimized day cell builder using period dates with cycle detection
  Widget _buildOptimizedDayCell(
      DateTime date, Set<DateTime> periodDates, Set<DateTime> ovulationDates) {
    final dateNormalized = DateTime(date.year, date.month, date.day);
    final today = DateTime.now();
    final todayNormalized = DateTime(today.year, today.month, today.day);

    // Generate visual period dates by filling single-day gaps for display
    final visualPeriodDates = _generateVisualPeriodDates(periodDates);

    // Check if this date is a visual period date (includes filled gaps)
    final isPeriodDate = visualPeriodDates.any((periodDate) =>
        periodDate.year == date.year &&
        periodDate.month == date.month &&
        periodDate.day == date.day);

    // Check if this date is an actual period date (from user data, not filled gap)
    final isActualPeriodDate = periodDates.any((periodDate) =>
        periodDate.year == date.year &&
        periodDate.month == date.month &&
        periodDate.day == date.day);

    // Check if this date is an ovulation date
    final isOvulationDate = ovulationDates.any((ovulationDate) =>
        ovulationDate.year == date.year &&
        ovulationDate.month == date.month &&
        ovulationDate.day == date.day);

    final isToday = dateNormalized.isAtSameMomentAs(todayNormalized);
    final isFutureDate = dateNormalized.isAfter(todayNormalized);

    // Calculate first/last period dates only if this is a period date
    bool isFirstPeriodDate = false;
    bool isLastPeriodDate = false;

    if (isPeriodDate) {
      // Use visual period dates for cycle calculation (includes filled gaps)
      final periodCycles = _getPeriodCycles(visualPeriodDates);

      // Fast lookup - check each cycle for this date
      for (final cycle in periodCycles) {
        if (cycle.any((d) => _isSameDate(d, date))) {
          isFirstPeriodDate = _isSameDate(cycle.first, date);
          isLastPeriodDate = _isSameDate(cycle.last, date);
          break; // Found the cycle, no need to continue
        }
      }
    }

    // Calculate first/last ovulation dates only if this is an ovulation date
    bool isFirstOvulationDate = false;
    bool isLastOvulationDate = false;

    if (isOvulationDate) {
      final ovulationWindows = _getOvulationWindows(ovulationDates);

      // Fast lookup - check each window for this date
      for (final window in ovulationWindows) {
        if (window.any((d) => _isSameDate(d, date))) {
          isFirstOvulationDate = _isSameDate(window.first, date);
          isLastOvulationDate = _isSameDate(window.last, date);
          break; // Found the window, no need to continue
        }
      }
    }

    // Determine if this is a filled gap date (visual period date but not actual)
    final isFilledGapDate = isPeriodDate && !isActualPeriodDate;

    // Create DateState with the calculated values
    final dateState = DateState(
      isTodayDate: isToday,
      isFutureDate: isFutureDate,
      isSelectedPeriodDate: isPeriodDate,
      isFirstPeriodDate: isFirstPeriodDate,
      isLastPeriodDate: isLastPeriodDate,
      isInPeriodWindow: false, // Can be implemented later
      isOvulationDate: isOvulationDate,
      isFirstOvulationDate: isFirstOvulationDate,
      isLastOvulationDate: isLastOvulationDate,
      isInOvulationWindow: false, // Can be implemented later
      isFilledGapDate: isFilledGapDate,
    );

    // Build the appropriate widget using the same logic as original but optimized
    return GestureDetector(
      onTap: () => _showSymptomBottomSheet(context, date),
      child: Container(
        height: 55.h,
        child: _buildDayWidget(
          date: date,
          dateState: dateState,
        ),
      ),
    );
  }

  // Main widget builder that uses cached date states for performance
// Main widget builder that uses cached date states for performance
  Widget _buildDayWidget({
    required DateTime date,
    required DateState dateState,
  }) {
    // Build the "base" state first
    final Widget base = _buildBaseState(date, dateState);

    // If today, wrap the base state with a "today" highlight
    if (dateState.isTodayDate) {
      return _buildTodayWrapper(base);
    }

    // Otherwise just return the base state
    return base;
  }

  Widget _buildBaseState(DateTime date, DateState dateState) {
    // Period dates (first/last have priority over middle)
    if (dateState.isFirstPeriodDate) {
      return _buildPeriodFirstDate(date, dateState.isFutureDate);
    }
    if (dateState.isLastPeriodDate) {
      return _buildPeriodLastDate(date, dateState.isFutureDate);
    }
    if (dateState.isSelectedPeriodDate) {
      // Use filled gap styling for gap dates, regular middle styling for actual dates
      return dateState.isFilledGapDate
          ? _buildPeriodFilledGapDate(date, dateState.isFutureDate)
          : _buildPeriodMiddleDate(date, dateState.isFutureDate);
    }

    // Ovulation dates (first/last have priority over middle)
    if (dateState.isFirstOvulationDate) {
      return _buildOvulationFirstDate(date, dateState.isFutureDate);
    }
    if (dateState.isLastOvulationDate) {
      return _buildOvulationLastDate(date, dateState.isFutureDate);
    }
    if (dateState.isOvulationDate) {
      return _buildOvulationMiddleDate(date, dateState.isFutureDate);
    }

    // Regular date
    return _buildRegularDate(date);
  }

  Widget _buildTodayWrapper(Widget child) {
    return Stack(
      alignment: Alignment.center,
      children: [
        child,
        _buildTodayDate(DateTime.now()), // period/ovulation/regular/etc.
      ],
    );
  }

  // Identical styling methods from original implementation
  Widget _buildTodayDate(DateTime date) {
    return Container(
      width: 55.w,
      height: 55.h,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Color(0xff5DADE2), width: 2),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff5DADE2),
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodFirstDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.hardEdge,
        children: [
          // Light purple extension to the right
          Positioned(
            left: 15.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(27.5.h),
                  bottomLeft: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: isFuture
                ? DottedBorder(
                    color: Color(0xff584294),
                    strokeWidth: 1.5,
                    child: _buildRegularDate(date),
                  )
                : _buildCircleContent(date, Color(0xff584294), Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodLastDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.hardEdge,
        children: [
          // Light purple extension to the left
          Positioned(
            right: 8.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(27.5.h),
                  bottomRight: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: isFuture
                ? DottedBorder(
                    color: Color(0xff584294),
                    strokeWidth: 1.5,
                    child: _buildRegularDate(date),
                  )
                : _buildCircleContent(date, Color(0xff584294), Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodMiddleDate(DateTime date, bool isFuture) {
    return Container(
      width: 104.w,
      height: 55.h,
      decoration: BoxDecoration(
        color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: isFuture ? Color(0xff71456F) : Colors.white,
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodFilledGapDate(DateTime date, bool isFuture) {
    return Container(
      width: 104.w,
      height: 55.h,
      decoration: BoxDecoration(
        color: isFuture ? Color(0xffE4DEFF) : Color(0xffCFB4FE),
        // Add a subtle pattern or border to indicate this is a filled gap
        border: Border.all(
          color: isFuture ? Color(0xff9B7EBF) : Color(0xff8A6DB8),
          width: 1.0,
          style: BorderStyle.solid,
        ),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: isFuture ? Color(0xff71456F) : Colors.white,
            fontWeight:
                FontWeight.w400, // Slightly lighter weight for filled gaps
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildOvulationFirstDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.hardEdge,
        children: [
          // Light orange extension to the right
          Positioned(
            left: 15.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(27.5.h),
                  bottomLeft: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: isFuture
                ? DottedBorder(
                    color: Color(0xffE6A500),
                    strokeWidth: 1.5,
                    child: _buildRegularDate(date),
                  )
                : _buildCircleContent(date, Color(0xffE6A500), Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildOvulationLastDate(DateTime date, bool isFuture) {
    return Container(
      width: 100.w,
      height: 55.h,
      child: Stack(
        clipBehavior: Clip.hardEdge,
        children: [
          // Light purple extension to the right
          Positioned(
            right: 8.0.w,
            top: 0,
            child: Container(
              width: 100.w,
              height: 55.h,
              decoration: BoxDecoration(
                color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(27.5.h),
                  bottomRight: Radius.circular(27.5.h),
                ),
              ),
            ),
          ),
          // Circle centered
          Center(
            child: isFuture
                ? DottedBorder(
                    color: Color(0xffE6A500),
                    strokeWidth: 1.5,
                    child: _buildRegularDate(date),
                  )
                : _buildCircleContent(date, Color(0xffE6A500), Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildOvulationMiddleDate(DateTime date, bool isFuture) {
    return Container(
      width: 104.w,
      height: 55.h,
      decoration: BoxDecoration(
        color: isFuture ? Color(0xffFFECC7) : Color(0xffFFDA94),
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff71456F),
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildCircleContent(
      DateTime date, Color backgroundColor, Color textColor) {
    return Container(
      width: 60.w,
      height: 60.h,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor,
      ),
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: textColor,
            fontWeight: FontWeight.w500,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }

  Widget _buildRegularDate(DateTime date) {
    return Container(
      width: 55.w,
      height: 55.h,
      child: Center(
        child: Text(
          '${date.day}',
          style: GoogleFonts.roboto(
            color: Color(0xff71456F),
            fontWeight: FontWeight.w400,
            fontSize: 25.sp,
          ),
        ),
      ),
    );
  }
}

class _SymptomBottomSheet extends StatelessWidget {
  final DateTime selectedDate;

  const _SymptomBottomSheet({required this.selectedDate});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.4,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(32),
          topRight: Radius.circular(32),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: EdgeInsets.only(top: 12.h),
            width: 40.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),
          // Header with date and + button
          Padding(
            padding: EdgeInsets.all(20.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  DateFormat('MMMM d, yyyy').format(selectedDate),
                  style: GoogleFonts.roboto(
                    fontSize: 35.sp,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryColor,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context); // Close current bottom sheet
                    // Show DailySymptomPage in a new bottom sheet
                    showModalBottomSheet<void>(
                      context: context,
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      builder: (context) => MultiBlocProvider(
                        providers: [
                          BlocProvider(
                            create: (context) {
                              final bloc = getIt<SymptomTrackingBloc>();
                              // Initialize the bloc with the selected date
                              bloc.add(SymptomTrackingEvent.loadSymptomData(
                                  date: selectedDate));
                              return bloc;
                            },
                          ),
                          BlocProvider(
                            create: (context) {
                              final bloc = getIt<SymptomWatcherBloc>();
                              // Initialize the bloc to start watching symptoms
                              bloc.add(
                                  const SymptomWatcherEvent.watchStarted());
                              return bloc;
                            },
                          ),
                        ],
                        child: Container(
                          height: .9.sh,
                          child: DailySymptomPage(),
                        ),
                      ),
                    );
                  },
                  child: Container(
                    width: 70.w,
                    height: 70.h,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Symptoms content - Made scrollable
          Expanded(
            child: SingleChildScrollView(
              child: BlocBuilder<SymptomTrackingBloc, SymptomTrackingState>(
                builder: (context, state) {
                  return state.when(
                    initial: (selectedDate) => _buildEmptyState(),
                    loading: (selectedDate) =>
                        Center(child: CircularProgressIndicator()),
                    loaded: (selectedDate, symptoms, painLevel, flowLevel) =>
                        _buildSymptomContent(symptoms, painLevel, flowLevel),
                    success: (selectedDate) => _buildEmptyState(),
                    failure: (selectedDate, failure) => _buildEmptyState(),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.sentiment_neutral,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            'No symptoms recorded',
            style: GoogleFonts.roboto(
              fontSize: 25.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Tap + to add symptoms',
            style: GoogleFonts.roboto(
              fontSize: 25.sp,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSymptomContent(
      List<SymptomModel>? symptoms, int? painLevel, int? flowLevel) {
    List<Widget> chips = [];

    // Add pain chip if pain level > 0
    if (painLevel != null && painLevel > 0) {
      chips.add(_buildSymptomChip(
        name: 'Pain',
        iconPath: 'assets/home/<USER>', // Using cramps icon for pain
        value: painLevel.toString(),
        isSelected: true,
      ));
    }

    // Add flow chip if flow level > 0
    if (flowLevel != null && flowLevel > 0) {
      String flowText = '';
      switch (flowLevel) {
        case 1:
          flowText = 'Light';
          break;
        case 2:
          flowText = 'Medium';
          break;
        case 3:
          flowText = 'Heavy';
          break;
      }
      chips.add(_buildSymptomChip(
        name: 'Flow',
        iconPath: 'assets/home/<USER>',
        value: flowText,
        isSelected: true,
      ));
    }

    // Add symptom chips
    if (symptoms != null && symptoms.isNotEmpty) {
      for (final symptom in symptoms) {
        chips.add(_buildSymptomChip(
          name: symptom.name,
          symptom: symptom, // Pass the full symptom object
          isSelected: true,
        ));
      }
    }

    if (chips.isEmpty) {
      return _buildEmptyState();
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 16.h),
          Wrap(
            spacing: 12.w,
            runSpacing: 12.h,
            children: chips,
          ),
        ],
      ),
    );
  }

  Widget _buildSymptomChip({
    required String name,
    String? iconPath, // Made optional for backward compatibility
    SymptomModel? symptom, // New parameter for symptom object
    String? value,
    bool isSelected = false,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 60.w,
            height: 60.w,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              shape: BoxShape.circle,
            ),
            child: Padding(
              padding: EdgeInsets.all(8.w),
              child: _buildChipIcon(symptom, iconPath),
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            value != null ? '$name: $value' : name,
            style: GoogleFonts.mulish(
              fontSize: 25.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryColor,
            ),
          ),
          SizedBox(width: 8.w),
        ],
      ),
    );
  }

  Widget _buildChipIcon(SymptomModel? symptom, String? iconPath) {
    // If we have a symptom with local icon path, use it
    if (symptom?.localIconPath != null &&
        File(symptom!.localIconPath!).existsSync()) {
      return SvgPicture.file(
        File(symptom.localIconPath!),
        width: 16.w,
        height: 16.w,
        colorFilter: ColorFilter.mode(
          Colors.white,
          BlendMode.srcIn,
        ),
      );
    }
    // If we have a symptom with network icon URL, use it
    else if (symptom?.iconUrl != null && symptom!.iconUrl!.isNotEmpty) {
      return SvgPicture.network(
        symptom.iconUrl!,
        width: 16.w,
        height: 16.w,
        colorFilter: ColorFilter.mode(
          Colors.white,
          BlendMode.srcIn,
        ),
        placeholderBuilder: (context) => Icon(
          Icons.healing,
          color: Colors.white,
          size: 16.w,
        ),
      );
    }
    // If we have a legacy iconPath, use it
    else if (iconPath != null) {
      return SvgPicture.asset(
        iconPath,
        width: 16.w,
        height: 16.w,
        colorFilter: ColorFilter.mode(
          Colors.white,
          BlendMode.srcIn,
        ),
      );
    }
    // Default fallback icon
    else {
      return Icon(
        Icons.healing,
        color: Colors.white,
        size: 30.w,
      );
    }
  }
}
