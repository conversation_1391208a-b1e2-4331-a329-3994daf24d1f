import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:account_management/account_management.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../custom_widgets/period_icon_button.dart';
import '../../custom_widgets/flow_selection_bottom_sheet.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../helpers.dart';
import 'package:design_system/design_system.dart';

@RoutePage()
class PeriodTrackingEditPage extends StatelessWidget {
  final int? initialYear;

  const PeriodTrackingEditPage({super.key, this.initialYear});

  @override
  Widget build(BuildContext context) {
    final yearToUse = initialYear ?? DateTime.now().year;
    print(
        'PeriodTrackingEditPage: initialYear = $initialYear, yearToUse = $yearToUse');

    return MultiBlocProvider(
      providers: [
        BlocProvider<PeriodTrackingWatcherBloc>(create: (context) {
          print(
              '📝 PeriodTrackingEditPage: Creating PeriodTrackingWatcherBloc');
          final bloc = getIt<PeriodTrackingWatcherBloc>();
          print(
              '📝 PeriodTrackingEditPage: Starting bloc with year $yearToUse');
          bloc.add(PeriodTrackingWatcherEvent.watchYearStarted(yearToUse));
          return bloc;
        }),
        BlocProvider<ManagePeriodTrackingBloc>(
          create: (context) => getIt<ManagePeriodTrackingBloc>(),
        ),
        BlocProvider<ManageOvulationBloc>(
          create: (context) => getIt<ManageOvulationBloc>(),
        ),
      ],
      child: PeriodTrackingEditScaffold(initialYear: yearToUse),
    );
  }
}

class PeriodTrackingEditScaffold extends StatefulWidget {
  final int initialYear;

  const PeriodTrackingEditScaffold({required this.initialYear, super.key});

  @override
  State<PeriodTrackingEditScaffold> createState() =>
      _PeriodTrackingEditScaffoldState();
}

class _PeriodTrackingEditScaffoldState
    extends State<PeriodTrackingEditScaffold> {
  late int _selectedYear;
  bool _isInitialized = false;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _selectedYear = widget.initialYear;
    _scrollController = ScrollController();

    // Auto-scroll to today's month after the widget is built (if this year)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToTodaysMonth();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToTodaysMonth() {
    final today = DateTime.now();
    if (today.year == _selectedYear) {
      // Find the index of today's month (0-based)
      final todayMonthIndex = today.month - 1;

      if (todayMonthIndex >= 0 && todayMonthIndex < 12) {
        // Check if scroll controller is attached to avoid assertion error
        if (_scrollController.hasClients) {
          // Calculate scroll position more accurately
          // Each month card is approximately 450 pixels including margins
          final cardHeight = 450.0;
          final scrollPosition = todayMonthIndex * cardHeight;

          // Add a delay to ensure the ListView is fully rendered
          Future.delayed(Duration(milliseconds: 200), () {
            if (_scrollController.hasClients && mounted) {
              // Ensure we don't scroll beyond the maximum scroll extent
              final maxScrollExtent =
                  _scrollController.position.maxScrollExtent;
              final targetPosition = scrollPosition > maxScrollExtent
                  ? maxScrollExtent
                  : scrollPosition;

              _scrollController.animateTo(
                targetPosition,
                duration: Duration(milliseconds: 1000),
                curve: Curves.easeInOut,
              );
            }
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CurvedAppBar(
        appBarColor: AppTheme.primaryColor,
        logoColor: const Color(0xffFAF2DF),
        height: .35.sw,
        subtitle: 'Insights',
        subtitleColor: const Color(0xffFAF2DF),
        topLeftIcon: Container(
          height: 40,
          width: 40,
          decoration: BoxDecoration(
            color: const Color(0xffFAF2DF),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back,
                color: AppTheme.primaryColor, size: 20),
            onPressed: () {
              context.router.pop();
            },
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xffFBF0D5), // Light cream
              Color(0xffF8EEFF), // Light purple
            ],
          ),
        ),
        child: MultiBlocListener(
          listeners: [
            BlocListener<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
              listener: (context, state) {
                final bloc = context.read<ManagePeriodTrackingBloc>();
                print('🎯 BlocListener received state: ${state.runtimeType}');
                print(
                    '🎯 BlocListener listening to bloc instance: ${bloc.hashCode}');
                print('🎯 BlocListener state details: $state');
                state.maybeWhen(
                  periodDatesUpdated:
                      (newlySelected, newlyDeselected, allPeriodDates) {
                    print(
                        '🥚 Period dates updated - triggering ovulation calculation');
                    print(
                        '🥚 Newly selected: ${newlySelected.length}, newly deselected: ${newlyDeselected.length}');
                    print('🥚 All period dates: ${allPeriodDates.length}');

                    // Trigger ovulation calculation after period dates are successfully updated
                    context.read<ManageOvulationBloc>().add(
                          ManageOvulationEvent.handlePeriodTrackingChanges(
                            newlySelected: newlySelected,
                            newlyDeselected: newlyDeselected,
                            allPeriodDates: allPeriodDates,
                          ),
                        );
                  },
                  success: () {
                    print(
                        '🎯 BlocListener received success state (no ovulation trigger)');
                  },
                  failure: (failure) {
                    print('🎯 BlocListener received failure state: $failure');
                  },
                  loading: () {
                    print('🎯 BlocListener received loading state');
                  },
                  dateSelectionChanged: (selectedDates, flowLevels) {
                    print(
                        '🎯 BlocListener received dateSelectionChanged state: ${selectedDates.length} dates');
                  },
                  orElse: () {
                    print(
                        '🎯 BlocListener received other state: ${state.runtimeType}');
                  },
                );
              },
            ),
            BlocListener<ManageOvulationBloc, ManageOvulationState>(
              listener: (context, state) {
                print(
                    '🥚 ManageOvulationBloc listener received state: ${state.runtimeType}');
                state.maybeWhen(
                  success: () {
                    print(
                        '🥚 Ovulation calculation completed successfully - navigating back');
                    Navigator.of(context).pop();
                  },
                  failure: (failure) {
                    print(
                        '🥚 Ovulation calculation failed: $failure - navigating back anyway');
                    Navigator.of(context).pop();
                  },
                  orElse: () {
                    print(
                        '🥚 ManageOvulationBloc other state: ${state.runtimeType}');
                  },
                );
              },
            ),
          ],
          child: BlocBuilder<PeriodTrackingWatcherBloc,
              PeriodTrackingWatcherState>(
            builder: (context, state) {
              print(
                  '📝 PeriodTrackingEditPage: BlocBuilder received state: ${state.runtimeType}');

              return state.maybeMap(
                loading: (_) {
                  print(
                      '📝 PeriodTrackingEditPage: Loading state - showing progress indicator');
                  return Padding(
                    padding: EdgeInsets.only(
                      top: .05.sw + MediaQuery.of(context).padding.top,
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const CircularProgressIndicator(
                            color: AppTheme.primaryColor,
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            'Loading period data...',
                            style: GoogleFonts.roboto(
                              color: AppTheme.primaryColor,
                              fontSize: 16.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
                loadSuccess: (dataState) {
                  print(
                      '📝 PeriodTrackingEditPage: LoadSuccess state with ${dataState.yearData.keys.length} months');

                  // Extract period dates from the year data
                  final periodDates = <DateTime>{};
                  for (final monthEntry in dataState.yearData.entries) {
                    print(
                        '📝 Processing month ${monthEntry.key} with ${monthEntry.value.keys.length} days');

                    for (final dayEntry in monthEntry.value.entries) {
                      final periodModel = dayEntry.value;
                      if (periodModel.isPeriodDate == true &&
                          periodModel.date != null) {
                        periodDates.add(periodModel.date!);
                      }
                    }
                  }

                  print(
                      '📝 PeriodTrackingEditPage: Extracted ${periodDates.length} period dates');

                  // Initialize BLoC with existing data only once
                  if (!_isInitialized) {
                    print(
                        '📝 PeriodTrackingEditPage: Initializing BLoC with ${periodDates.length} period dates');

                    // Extract flow levels from existing data
                    final flowLevels = <DateTime, int>{};
                    for (final monthEntry in dataState.yearData.entries) {
                      for (final dayEntry in monthEntry.value.entries) {
                        final periodModel = dayEntry.value;
                        if (periodModel.date != null &&
                            periodModel.flowLevel != null) {
                          flowLevels[periodModel.date!] =
                              periodModel.flowLevel!;
                        }
                      }
                    }

                    // Initialize the BLoC with existing data
                    context.read<ManagePeriodTrackingBloc>().add(
                          ManagePeriodTrackingEvent.initializeFromData(
                            periodDates: periodDates,
                            flowLevels: flowLevels,
                          ),
                        );

                    _isInitialized = true;
                    print('📝 PeriodTrackingEditPage: Initialization complete');
                  }

                  return BlocBuilder<ManagePeriodTrackingBloc,
                      ManagePeriodTrackingState>(
                    buildWhen: (previous, current) {
                      // Only rebuild when the date selection actually changes
                      return current.maybeMap(
                        dateSelectionChanged: (_) => true,
                        orElse: () => false,
                      );
                    },
                    builder: (context, managePeriodState) {
                      return _buildCalendarView(managePeriodState);
                    },
                  );
                },
                orElse: () {
                  print(
                      '📝 PeriodTrackingEditPage: OrElse case - showing no data found');
                  return Padding(
                    padding: EdgeInsets.only(
                      top: .0.sw + MediaQuery.of(context).padding.top,
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.calendar_today,
                              size: 64.w, color: Colors.grey),
                          SizedBox(height: 16.h),
                          Text(
                            'No period data found',
                            style: GoogleFonts.roboto(
                              fontSize: 18.sp,
                              color: Colors.grey,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'Start by selecting some dates',
                            style: GoogleFonts.roboto(
                              fontSize: 14.sp,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
      bottomNavigationBar: _buildDoneButton(),
    );
  }

  Widget _buildCalendarView(ManagePeriodTrackingState managePeriodState) {
    return Column(
      children: [
        // Scrollable calendar months
        Expanded(
          child: _buildScrollableCalendar(managePeriodState),
        ),
        // Done button
      ],
    );
  }

  Widget _buildScrollableCalendar(ManagePeriodTrackingState managePeriodState) {
    final months =
        List.generate(12, (index) => DateTime(_selectedYear, index + 1, 1));

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.only(
        top: .3.sw + MediaQuery.of(context).padding.top,
        left: 16.w,
        right: 16.w,
        bottom: 8.h,
      ),
      itemCount: months.length,
      itemBuilder: (context, index) {
        return _buildMonthCard(months[index], managePeriodState);
      },
    );
  }

  Widget _buildMonthCard(
      DateTime month, ManagePeriodTrackingState managePeriodState) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6),
      child: Container(
        margin: EdgeInsets.only(bottom: 16.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(32),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              spreadRadius: 2,
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 40.w, horizontal: 10.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              // Month title
              Text(
                _getMonthYearText(month),
                style: GoogleFonts.inter(
                  fontSize: 34.sp,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
              SizedBox(height: 12.h),
              Divider(),
              // Days of week header
              _buildDaysOfWeekHeader(),
              // Calendar grid for this month
              _buildMonthCalendarGrid(month, managePeriodState),
            ],
          ),
        ),
      ),
    );
  }

  // Days of week header
  Widget _buildDaysOfWeekHeader() {
    final daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return Row(
      children: daysOfWeek
          .map((day) => Expanded(
                child: Center(
                  child: Text(
                    day,
                    style: GoogleFonts.roboto(
                      fontSize: 25.sp,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ))
          .toList(),
    );
  }

  Widget _buildMonthCalendarGrid(
      DateTime month, ManagePeriodTrackingState managePeriodState) {
    final daysInMonth = _getDaysInMonth(month);
    final firstDayOfMonth = DateTime(month.year, month.month, 1);
    final firstWeekday = firstDayOfMonth.weekday % 7; // Sunday = 0

    // Build calendar using Column and Row for better control
    List<Widget> weeks = [];
    List<Widget> currentWeek = [];

    // Add empty cells for days before the first day of the month
    for (int i = 0; i < firstWeekday; i++) {
      currentWeek.add(Expanded(child: Container()));
    }

    // Add days of the month
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(month.year, month.month, day);
      currentWeek.add(Expanded(child: _buildDateCell(date, managePeriodState)));

      // If we've filled a week (7 days), start a new week
      if (currentWeek.length == 7) {
        weeks.add(
          Padding(
            padding: EdgeInsets.symmetric(vertical: 2.h),
            child: Row(children: List.from(currentWeek)),
          ),
        );
        currentWeek.clear();
      }
    }

    // Fill remaining cells in the last week
    while (currentWeek.length < 7) {
      currentWeek.add(Expanded(child: Container()));
    }
    if (currentWeek.isNotEmpty) {
      weeks.add(
        Padding(
          padding: EdgeInsets.symmetric(vertical: 2.h),
          child: Row(children: List.from(currentWeek)),
        ),
      );
    }

    return Column(
      children: weeks,
    );
  }

  Widget _buildDateCell(
      DateTime date, ManagePeriodTrackingState managePeriodState) {
    final today = DateTime.now();
    final todayNormalized = DateTime(today.year, today.month, today.day);
    final dateNormalized = DateTime(date.year, date.month, date.day);
    final januaryFirst = DateTime(_selectedYear, 1, 1);

    // Get selected dates from BLoC state
    final selectedDates = managePeriodState.maybeMap(
      dateSelectionChanged: (state) => state.selectedDates,
      orElse: () => <DateTime>{},
    );

    // Check date properties
    bool isTodayDate = dateNormalized.isAtSameMomentAs(todayNormalized);
    bool isFutureDate = dateNormalized.isAfter(todayNormalized);
    bool isSelectableDate = dateNormalized
            .isAfter(januaryFirst.subtract(const Duration(days: 1))) &&
        !isFutureDate;

    // Generate visual period dates (includes gap-filled dates)
    final visualPeriodDates = _generateVisualPeriodDates(selectedDates);

    // Check if this date is actually selected by user
    bool isActuallySelected = selectedDates.any((selectedDay) =>
        selectedDay.year == date.year &&
        selectedDay.month == date.month &&
        selectedDay.day == date.day);

    // Check if this date is visually shown as period (includes gap-filled)
    bool isVisualPeriodDate = visualPeriodDates.any((periodDate) =>
        periodDate.year == date.year &&
        periodDate.month == date.month &&
        periodDate.day == date.day);

    // Determine if this is a gap-filled date (visual but not actual)
    bool isGapFilledDate = isVisualPeriodDate && !isActuallySelected;

    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Day number
          Text(
            '${date.day}',
            style: GoogleFonts.roboto(
              color:
                  isFutureDate ? Colors.grey.shade400 : AppTheme.primaryColor,
              fontWeight: FontWeight.w500,
              fontSize: 25.sp,
            ),
          ),
          SizedBox(height: 2.h),
          // Period icon button for selectable dates
          if (isSelectableDate)
            PeriodIconButton(
              isSelected: isActuallySelected,
              isGapFilled: isGapFilledDate,
              size: 50.0,
              onTap: () {
                final normalizedDate =
                    DateTime(date.year, date.month, date.day);

                if (isActuallySelected) {
                  // Deselect the date
                  print('🖱️ UI: Deselecting date $normalizedDate');
                  context.read<ManagePeriodTrackingBloc>().add(
                        ManagePeriodTrackingEvent.dateDeselected(
                            normalizedDate),
                      );
                } else {
                  // Select the date
                  print('🖱️ UI: Selecting date $normalizedDate');
                  context.read<ManagePeriodTrackingBloc>().add(
                        ManagePeriodTrackingEvent.dateSelected(normalizedDate),
                      );
                  // Show flow selection bottom sheet
                  _showFlowSelectionBottomSheet(
                      normalizedDate, managePeriodState);
                }
              },
            )
          else
            SizedBox(height: 50.0), // Maintain spacing for non-selectable dates
          // Today indicator
          if (isTodayDate)
            Container(
              width: 20.w,
              height: 2.h,
              margin: EdgeInsets.only(top: 1.h),
              decoration: BoxDecoration(
                color: const Color(0xFFE91E63),
                borderRadius: BorderRadius.circular(1.r),
              ),
            ),
        ],
      ),
    );
  }

  // CRITICAL: Done button with ovulation calculation logic
  Widget _buildDoneButton() {
    return BlocListener<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      listener: (context, state) {
        state.maybeWhen(
          success: () {
            // Show success message and navigate back
            Fluttertoast.showToast(
              msg: 'Period dates saved successfully!',
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
            );
            Navigator.of(context).pop();
          },
          failure: (failure) {
            // Show error message
            Fluttertoast.showToast(
              msg: 'Failed to save period dates. Please try again.',
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.BOTTOM,
            );
          },
          orElse: () {},
        );
      },
      child: BlocBuilder<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
        builder: (context, state) {
          final isLoading = state.maybeWhen(
            loading: () => true,
            orElse: () => false,
          );

          return GestureDetector(
            onTap: isLoading
                ? null
                : () {
                    // Save the selected dates using the simplified BLoC
                    print('🖱️ UI: Done button pressed - saving changes');
                    final bloc = context.read<ManagePeriodTrackingBloc>();
                    print(
                        '🎯 Using ManagePeriodTrackingBloc instance: ${bloc.hashCode}');
                    bloc.add(
                      const ManagePeriodTrackingEvent.savePeriodDates(),
                    );

                    // Navigation will happen in BlocListener after ovulation calculation
                  },
            child: Container(
              decoration: BoxDecoration(
                color: Color(0xffF8EEFF), // Light pu,
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 25.0, vertical: 15.0),
                child: Container(
                  height: .13.sw,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    borderRadius: BorderRadius.circular(32),
                  ),
                  alignment: Alignment.center,
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 10.h),
                    child: isLoading
                        ? SizedBox(
                            width: 20.w,
                            height: 20.h,
                            child: const CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : Text(
                            'Done',
                            style: GoogleFonts.mulish(
                              color: Colors.white,
                              fontSize: 35.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  int _getDaysInMonth(DateTime month) {
    return DateTime(month.year, month.month + 1, 0).day;
  }

  String _getMonthYearText(DateTime month) {
    const monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return '${monthNames[month.month - 1]} ${month.year}';
  }

  /// Generate visual period dates (includes gap-filled dates for better UX)
  Set<DateTime> _generateVisualPeriodDates(Set<DateTime> actualSelectedDates) {
    if (actualSelectedDates.isEmpty) return {};

    final visualDates = Set<DateTime>.from(actualSelectedDates);
    final sortedDates = actualSelectedDates.toList()..sort();

    // Fill gaps between consecutive period dates (within 2 days)
    for (int i = 0; i < sortedDates.length - 1; i++) {
      final currentDate = sortedDates[i];
      final nextDate = sortedDates[i + 1];
      final daysBetween = nextDate.difference(currentDate).inDays;

      // Fill gaps of 1-2 days between period dates
      if (daysBetween <= 3) {
        for (int j = 1; j < daysBetween; j++) {
          final gapDate = currentDate.add(Duration(days: j));
          visualDates.add(gapDate);
        }
      }
    }

    return visualDates;
  }

  // Show flow selection bottom sheet
  void _showFlowSelectionBottomSheet(
      DateTime date, ManagePeriodTrackingState managePeriodState) {
    final flowLevels = managePeriodState.maybeMap(
      dateSelectionChanged: (state) => state.flowLevels,
      orElse: () => <DateTime, int>{},
    );
    final currentFlowLevel = flowLevels[date];

    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext bottomSheetContext) {
        return BlocProvider.value(
          value: context.read<ManagePeriodTrackingBloc>(),
          child: FlowSelectionBottomSheet(
            selectedDate: date,
            currentFlowLevel: currentFlowLevel,
            onFlowSelected: (flowLevel) {
              context.read<ManagePeriodTrackingBloc>().add(
                    ManagePeriodTrackingEvent.setFlowLevel(
                      date: date,
                      flowLevel: flowLevel,
                    ),
                  );
            },
          ),
        );
      },
    );
  }
}
