import 'package:account_management/application/onboardin_form_bloc/onboarding_form_bloc.dart';
import 'package:account_management/di/di.dart';
import 'package:account_management/domain/model/health_data.dart';
import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:date_picker_plus/date_picker_plus.dart';
import 'package:design_system/design/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:skeletonizer/skeletonizer.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../routing/app_pages.gr.dart';
import 'package:flutter_svg/svg.dart';
import 'help_info_button.dart';
import 'package:another_xlider/another_xlider.dart';
import 'package:another_xlider/models/handler.dart';
import 'package:another_xlider/models/slider_step.dart';
import 'package:another_xlider/models/tooltip/tooltip.dart';
import 'package:another_xlider/models/trackbar.dart';
import 'onboarding_slider.dart';

@RoutePage()
class GetStartedPage extends StatefulWidget {
  @override
  _GetStartedPageState createState() => _GetStartedPageState();
}

class _GetStartedPageState extends State<GetStartedPage>
    with TickerProviderStateMixin {
  int cycleLength = 28;
  int periodDays = 4;
  List<String>? contraception = [];
  DateTime dob = DateTime.now().subtract(Duration(days: 365 * 10));
  DateTime lastPeriod = DateTime.now();
  final PageController _controller = PageController();
  int currentPage = 0;
  bool get isLastPage => currentPage == 3;

  late AnimationController _titleAnimationController;
  late Animation<double> _titleFadeAnimation;
  late Animation<Offset> _titleSlideAnimation;

  @override
  void initState() {
    super.initState();
    _titleAnimationController = AnimationController(
      duration: Duration(milliseconds: 800),
      vsync: this,
    );

    _titleFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 3.0,
    ).animate(CurvedAnimation(
      parent: _titleAnimationController,
      curve: Curves.easeInOut,
    ));

    _titleSlideAnimation = Tween<Offset>(
      begin: Offset(0, 0.8),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _titleAnimationController,
      curve: Curves.linear,
    ));

    // Start animation for the first page
    _titleAnimationController.forward();
  }

  @override
  void dispose() {
    _titleAnimationController.dispose();
    _controller.dispose();
    super.dispose();
  }

  void _goNext() {
    if (isLastPage) {
      context.router.push(const WelcomeRoute());
    } else {
      _controller.nextPage(
          duration: Duration(milliseconds: 500), curve: Curves.easeIn);
    }
  }

  void _onDone() {
    context
        .read<OnboardingFormBloc>()
        .add(OnboardingFormEvent.updateOnboardingForm(
            HealthDataModel(
              cycleLength: cycleLength,
              periodLength: periodDays,
              contraceptionType: contraception,
              lastPeriodDate: lastPeriod,
            ),
            dob));
    context.router.push(HomeRoute());
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<OnboardingFormBloc, OnboardingFormState>(
      listener: (context, state) {
        // TODO: implement listener
      },
      child: PopScope(
        canPop: false,
        child: Scaffold(
          extendBodyBehindAppBar: true,
          appBar: CurvedAppBar(
            appBarColor: AppTheme.primaryColor,
            logoColor: AppTheme.loginAppBarColor,
            height: .30.sw,
             // Invisible, empty widget
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xffFAF2DF),
                  Color.fromARGB(255, 247, 224, 255),
                ],
              ),
            ),
            child: Stack(
              children: [
                PageView(
                  controller: _controller,
                  onPageChanged: (index) {
                    setState(() => currentPage = index);
                    // Restart title animation for new page
                    _titleAnimationController.reset();
                    _titleAnimationController.forward();
                  },
                  children: [
                    _buildPage(
                      card: _buildbirthDayCard(),
                      title: "Let's Get Personal",
                      circleImage: SvgPicture.asset(
                        'assets/onboarding/cake.svg',
                        width: 200,
                      ),
                    ),
                    _buildPage(
                      card: _buildCycleLengthCard(),
                      title: "Cycle & Flow",
                      circleImage: SvgPicture.asset(
                        'assets/onboarding/calendar.svg',
                        width: 170,
                      ),
                    ),
                    _buildPage(
                      card: _buildlastPeriodDayCard(),
                      title: "Last Period",
                    ),
                    _buildPage(
                      card: _buildContraceptionCard(),
                      title: "Birth Control",
                    ),
                  ],
                ),
                //
                Positioned(
                  bottom: 38,
                  left: 30,
                  child: Center(
                    // Ensures the child is centered
                    child: TextButton(
                      onPressed: _goNext,
                      style: ButtonStyle(
                        overlayColor: MaterialStateProperty.all(
                          Colors.white.withOpacity(0.1),
                        ),
                        shape: MaterialStateProperty.all(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        minimumSize: MaterialStateProperty.all(
                          Size(0, 36),
                        ),
                        padding: MaterialStateProperty.all(
                          EdgeInsets.symmetric(horizontal: 10),
                        ),
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      child: Text(
                        'Skip',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: 140,
                  left: 0,
                  right: 0,
                  child: Center(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(4, (index) {
                      final isPast = index < currentPage;
                      final isCurrent = index == currentPage;
                      return Container(
                        margin: EdgeInsets.symmetric(horizontal: 3),
                        width: isCurrent ? 50 : 41,
                        height: isCurrent ? 6 : 3,
                        decoration: BoxDecoration(
                            color: isCurrent
                                ? AppTheme.primaryColor
                                : isPast
                                    ? Color(0xFF8375F4) // passed
                                    : Color(0xFFD9D9D9), // future
                            borderRadius: BorderRadius.circular(5)),
                      );
                    }),
                  )),
                ),
                Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 60),
                    child: _buildHelpForPage(currentPage),
                  ),
                ),

                Positioned(
                  bottom: 25,
                  right: 20,
                  child: GestureDetector(
                    onTap: () {
                      if (isLastPage) {
                        _onDone();
                      } else {
                        _goNext();
                      }
                    },
                    child: Container(
                      width: 60,
                      height: 60,
                      alignment: Alignment.center,
                      child: isLastPage
                          ? Text(
                              'Done',
                              style: GoogleFonts.mulish(
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.primaryColor,
                              ),
                            )
                          : const Icon(
                              Icons.keyboard_arrow_right_rounded,
                              size: 35,
                              color: AppTheme.primaryColor,
                            ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPage({
    required Widget card,
    required String title,
    Widget? circleImage,
  }) {
    return Stack(children: [
      Positioned(
        top: 160,
        left: 0,
        right: 0,
        child: Container(
          height: 50,
          width: 190,
          alignment: Alignment.center,
          child: AnimatedBuilder(
            animation: _titleAnimationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _titleFadeAnimation,
                child: SlideTransition(
                  position: _titleSlideAnimation,
                  child: Text(
                    title,
                    textAlign: TextAlign.center,
                    style: GoogleFonts.dmSerifDisplay(
                      height: 1.0,
                      fontSize: 28,
                      color: Color(0xFF574E8E),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
      Center(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                child: Column(
                  children: [
                    SizedBox(
                      height: 90,
                    ),
                    Container(
                      child: Container(
                        child: card,
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ]);
  }

  Widget _buildCycleLengthCard() {
    return Column(
      children: [
        Card(
          margin: EdgeInsets.zero,
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              children: [
                SizedBox(height: 5),
                Text(
                  'How long is your average cycle?',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(
                  height: 5,
                ),
                CustomIntSlider(
                  value: cycleLength,
                  min: 10,
                  max: 50,
                  colour: Color(0xFF9D8BCD),
                  onChanged: (val) {
                    setState(() => cycleLength = val);
                  },
                ),
                SizedBox(
                  height: 5,
                ),
                Text(
                  'The average cycle is between 23-35 days',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 15,
                  ),
                ),
                SizedBox(
                  height: 5,
                ),
              ],
            ),
          ),
        ),
        SizedBox(
          height: 15,
        ),
        Card(
          margin: EdgeInsets.zero,
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              children: [
                SizedBox(height: 5),
                Text(
                  'How many days is your period?',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(
                  height: 5,
                ),
                CustomIntSlider(
                  value: periodDays,
                  min: 2,
                  max: 10,
                  colour: Color(0xFFF4BA4A),
                  onChanged: (val) {
                    setState(() => periodDays = val);
                  },
                ),
                SizedBox(
                  height: 5,
                ),
                Text(
                  'The average period usually lasts 3-5 days',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 15,
                  ),
                ),
                SizedBox(
                  height: 5,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContraceptionCard() {
    return Card(
      margin: EdgeInsets.zero,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 0),
        child: Column(
          children: [
            Text(
              'Which contraception(s) do you use?',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.black,
                fontSize: 19,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 8),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildImageOption('Pill', 'assets/onboarding/capsules.svg'),
                    _buildImageOption('Ring', 'assets/onboarding/ring.svg'),
                    _buildImageOption(
                        'Hormonal IUD', 'assets/onboarding/hormonal-iud.svg'),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildImageOption(
                        'Implant', 'assets/onboarding/implant.svg'),
                    _buildImageOption('Patch', 'assets/onboarding/patch.svg'),
                    _buildImageOption(
                        'Depo-shot', 'assets/onboarding/shot.svg'),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildImageOption(
                        'Copper IUD', 'assets/onboarding/copper-iud.svg'),
                    _buildImageOption('Other', 'assets/onboarding/other.svg'),
                    _buildImageOption('None', 'assets/onboarding/none.svg'),
                  ],
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _buildImageOption(String value, String imagePath) {
    final bool isSelected = contraception!.contains(value);

    return Column(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              if (isSelected) {
                contraception!.remove(value);
              } else {
                contraception!.add(value);
              }
            });
          },
          child: Container(
            margin: EdgeInsets.symmetric(vertical: 7, horizontal: 5),
            padding: EdgeInsets.symmetric(horizontal: 8),
            width: 76,
            height: 78,
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected ? AppTheme.primaryColor : Colors.transparent,
                width: 2,
              ),
              borderRadius: BorderRadius.circular(12),
              color: isSelected
                  ? AppTheme.primaryColor.withOpacity(0.1)
                  : Color(0xFFFAF2DF),
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 4,
                  offset: Offset(0, 4),
                )
              ],
            ),
            alignment: Alignment.center,
            child: SvgPicture.asset(
              imagePath,
              width: 50,
              height: 50,
            ),
          ),
        ),
        Container(
          constraints: BoxConstraints(
            minHeight: 20,
            minWidth: 108,
          ),
          child: Text(
            textAlign: TextAlign.center,
            value,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                  fontWeight: FontWeight.w400,
                  color: isSelected ? AppTheme.primaryColor : Colors.black,
                ),
          ),
        ),
      ],
    );
  }

  Widget _buildbirthDayCard() {
    return Card(
      margin: EdgeInsets.zero,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 14),
        child: Column(
          children: [
            SizedBox(height: 5),
            Text(
              'When is your birthday?',
              style: TextStyle(
                color: Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 5),
            SizedBox(
              height: 180,
              child: CupertinoTheme(
                data: CupertinoThemeData(
                  textTheme: CupertinoTextThemeData(
                    dateTimePickerTextStyle: TextStyle(
                      color: Color(0xff30285D),
                      fontSize: 18,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                child: CupertinoDatePicker(
                  mode: CupertinoDatePickerMode.date,
                  initialDateTime: dob ==
                          DateTime.now().subtract(Duration(days: 365 * 10))
                      ? DateTime(2000, DateTime.now().month, DateTime.now().day)
                      : dob,
                  minimumDate: DateTime(1960, 10, 10),
                  maximumDate:
                      DateTime.now().subtract(Duration(days: 365 * 10)),
                  onDateTimeChanged: (DateTime newDate) {
                    setState(() {
                      dob = newDate;
                    });
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildlastPeriodDayCard() {
    return Card(
      margin: EdgeInsets.zero,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Padding(
        padding: const EdgeInsets.all(17.0),
        child: Column(
          children: [
            Text(
              'When was the first day of your last period?',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.black,
                fontSize: 20,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 10),
            SizedBox(
              width: .8.sw,
              height: 300,
              child: DatePicker(
                initialDate: lastPeriod ?? DateTime.now(),
                minDate: DateTime.now().subtract(Duration(days: 670)),
                maxDate: DateTime.now(),
                padding: EdgeInsets.zero,
                currentDate: DateTime.now(),
                selectedDate: lastPeriod,
                onDateSelected: (date) {
                  lastPeriod = date;
                },
                currentDateDecoration: const BoxDecoration(),
                currentDateTextStyle: GoogleFonts.mulish(
                  color: const Color(0xff30285D),
                  fontWeight: FontWeight.w400,
                  fontSize: 18,
                ),
                daysOfTheWeekTextStyle: const TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
                disabledCellsDecoration: const BoxDecoration(),
                disabledCellsTextStyle: const TextStyle(color: Colors.grey),
                enabledCellsDecoration: const BoxDecoration(),
                enabledCellsTextStyle: GoogleFonts.mulish(
                  color: const Color(0xff71456F),
                  fontWeight: FontWeight.w400,
                  fontSize: 18,
                ),
                initialPickerType: PickerType.days,
                selectedCellDecoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  shape: BoxShape.circle,
                ),
                selectedCellTextStyle: GoogleFonts.mulish(
                  color: const Color(0xffFBF0D5),
                  fontWeight: FontWeight.w400,
                  fontSize: 20,
                ),
                leadingDateTextStyle: GoogleFonts.mulish(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 22,
                ),
                slidersColor: const Color(0xff30285D),
                highlightColor: const Color(0xff30285D),
                slidersSize: 20,
                splashColor: Theme.of(context).primaryColor.withOpacity(0.5),
                splashRadius: 0,
                centerLeadingDate: true,
              ),
            ),
            SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}

Widget _buildHelpForPage(int page) {
  switch (page) {
    case 0:
      return Help(
        why: "Knowing your age helps us personalize your cycle predictions.",
      );
    case 1:
      return Help(
        why:
            "Knowing your cycle length & period days helps us:\n • Predict your next period\n• Tailor insights to your unique pattern",
        what:
            "Your cycle length is the number of days from the first day of one period to the day before the next one starts.\n "
            "Period days is how many days you bleed.",
      );
    case 2:
      return Help(
        why:
            "Knowing this helps us start tracking your cycle right away and give your more accurate predictions from the start.",
        what: "Choose the first day of your most recent period.",
      );
    case 3:
      return Help(
        why:
            "Knowing your contraception method helps us tailor predictions, since different methods can affect your cycle.",
      );
    default:
      return SizedBox.shrink();
  }
}
