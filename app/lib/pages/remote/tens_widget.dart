import 'package:design_system/design/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:remote/application/device_control_heat_bloc/device_control_heat_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:remote/application/device_control_tens_bloc/device_control_tens_bloc.dart';
import 'package:remote/application/device_control_tens_watcher_bloc/device_control_tens_watcher_bloc.dart';
import 'package:remote/domain/failures/remote_failures.dart';
import 'package:remote/application/device_control_bloc/device_control_bloc.dart';
import 'package:juno_plus/utils/ui/toast_helper.dart';

class TensWidget extends StatefulWidget {
  const TensWidget({Key? key}) : super(key: key);

  @override
  State<TensWidget> createState() => _TensWidgetState();
}

class _TensWidgetState extends State<TensWidget> with TickerProviderStateMixin {
  late AnimationController _blinkController;
  bool _showBlink = false;

  @override
  void initState() {
    super.initState();
    _blinkController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          setState(() {
            _showBlink = !_showBlink;
          });
          _blinkController.reset();
          _blinkController.forward();
        }
      });
    _blinkController.forward();
  }

  @override
  void dispose() {
    _blinkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<DeviceControlHeatBloc, DeviceControlHeatState>(
          listener: (context, state) {
            state.maybeMap(
              changeHeatLevelFailure: (state) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.remoteFailure.maybeMap(
                      serverError: (_) => 'Server Error',
                      orElse: () => 'Unexpected Error',
                    )),
                  ),
                );
              },
              orElse: () {},
            );
          },
        ),
      ],
      child: BlocBuilder<DeviceControlTensWatcherBloc,
          DeviceControlTensWatcherState>(
        builder: (context, state) {
          // Calculate the applying level based on selected and actual levels
          int? applyingLevel;
          if (state.selectedTensLevel == state.actualTensLevel) {
            applyingLevel = null;
          } else if (state.selectedTensLevel > state.actualTensLevel) {
            // If we're increasing, the next level up is being applied
            applyingLevel = state.actualTensLevel;
          } else if (state.selectedTensLevel < state.actualTensLevel) {
            // If we're decreasing, the next level down is being applied
            applyingLevel = state.actualTensLevel - 1;
          }

          // If selected == actual, no level is being applied

          return Container(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Increase Button
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.6),
                        offset: Offset(4, 4),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  child: BlocBuilder<DeviceControlBloc, DeviceControlState>(
                    builder: (context, deviceState) {
                      final isTherapyActive = deviceState.maybeWhen(
                          therapyStateChanged: (active) => active,
                          orElse: () => true);
                      return IconButton(
                        key: const Key('tens_increase_button'),
                        icon: Icon(
                          Icons.add,
                          size: 28,
                        ),
                        onPressed: () {
                          print('TENS: Increase button pressed');
                          print('TENS: therapy active: $isTherapyActive');
                          if (!isTherapyActive) {
                            showTherapyPausedToast(context);
                            return;
                          }

                          if (state.selectedMode == 0) {
                            FToast().init(context).showToast(
                                  child: Container(
                                    padding: const EdgeInsets.all(8.0),
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                    child: const Text(
                                      'Please select a mode first',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  ),
                                  gravity: ToastGravity.BOTTOM,
                                  toastDuration: const Duration(seconds: 2),
                                );
                          } else {
                            context.read<DeviceControlTensBloc>().add(
                                  const DeviceControlTensEvent.increaseTens(),
                                );
                          }
                        },
                      );
                    },
                  ),
                ),
                SizedBox(height: 8),
                // Tens Level Indicator
                Container(
                  width: 35,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.6),
                        offset: Offset(4, 4),
                        blurRadius: 10,
                      ),
                      BoxShadow(
                        color: Colors.white,
                        offset: Offset(-4, -4),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(5.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: List.generate(10, (index) {
                        int levelIndex = 9 - index;

                        // Determine color based on the actual, applying, and selected Tens levels
                        Color color;

                        if (levelIndex < state.actualTensLevel &&
                            levelIndex != applyingLevel) {
                          // Already applied level - purple
                          color = AppTheme.primaryColor;
                        } else if (applyingLevel != null &&
                            levelIndex == applyingLevel) {
                          // Currently applying level - blinking red
                          color = _showBlink
                              ? Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withOpacity(0.6)
                              : Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withOpacity(0.2);
                        } else if (levelIndex < state.selectedTensLevel) {
                          // Selected but not yet applied - white
                          color = Colors.white;
                        } else {
                          // Inactive level - grey
                          color = Colors.grey[300]!;
                        }

                        return Container(
                          height: 64.dg,
                          margin: EdgeInsets.symmetric(vertical: 1),
                          decoration: BoxDecoration(
                            color: color,
                            borderRadius: BorderRadius.only(
                              topLeft: levelIndex == 9
                                  ? Radius.circular(30)
                                  : Radius.zero,
                              topRight: levelIndex == 9
                                  ? Radius.circular(30)
                                  : Radius.zero,
                              bottomLeft: levelIndex == 0
                                  ? Radius.circular(30)
                                  : Radius.zero,
                              bottomRight: levelIndex == 0
                                  ? Radius.circular(30)
                                  : Radius.zero,
                            ),
                          ),
                        );
                      }),
                    ),
                  ),
                ),
                SizedBox(height: 8),
                // Decrease Button
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.6),
                        offset: Offset(4, 4),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  child: BlocBuilder<DeviceControlBloc, DeviceControlState>(
                    builder: (context, deviceState) {
                      final isTherapyActive = deviceState.maybeWhen(
                          therapyStateChanged: (active) => active,
                          orElse: () => true);
                      return IconButton(
                        key: const Key('tens_decrease_button'),
                        icon: Icon(
                          Icons.remove,
                          size: 28,
                        ),
                        onPressed: () {
                          if (!isTherapyActive) {
                            showTherapyPausedToast(context);
                            return;
                          }
                          context.read<DeviceControlTensBloc>().add(
                                const DeviceControlTensEvent.decreaseTens(),
                              );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
