import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:remote/application/device_control_tens_bloc/device_control_tens_bloc.dart';
import 'package:remote/application/device_control_tens_watcher_bloc/device_control_tens_watcher_bloc.dart';
import 'package:remote/application/device_control_bloc/device_control_bloc.dart';
import 'package:juno_plus/utils/ui/toast_helper.dart';

class TensMode extends StatelessWidget {
  const TensMode({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DeviceControlTensWatcherBloc,
        DeviceControlTensWatcherState>(
      builder: (context, state) {
        print("${state.selectedMode}");
        return BlocBuilder<DeviceControlBloc, DeviceControlState>(
          builder: (context, deviceState) {
            final isTherapyActive = deviceState.maybeWhen(
                therapyStateChanged: (active) => active, orElse: () => true);
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GestureDetector(
                  onTap: () {
                    if (!isTherapyActive) {
                      showTherapyPausedToast(context);
                      return;
                    }
                    BlocProvider.of<DeviceControlTensBloc>(context)
                        .add(DeviceControlTensEvent.changeMode(1));
                  },
                  key: Key('tens_mode_1'),
                  child: Container(
                    height: 40,
                    width: 40,
                    decoration: BoxDecoration(
                      color: state.selectedMode == 1
                          ? Color(0xffF7A600)
                          : Colors.white,
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: Color(
                              0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                          blurRadius: 4.0, // the blur radius
                          offset: Offset(0, 1), // the x,y offset of the shadow
                        ),
                      ],
                    ),
                    child: Center(
                        child: Text(
                      '1',
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: state.selectedMode == 1
                              ? Colors.white
                              : Color(0xff26204A),
                          fontWeight: FontWeight.w400,
                          fontSize: 16),
                    )),
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                GestureDetector(
                  onTap: () {
                    if (!isTherapyActive) {
                      showTherapyPausedToast(context);
                      return;
                    }
                    BlocProvider.of<DeviceControlTensBloc>(context)
                        .add(DeviceControlTensEvent.changeMode(2));
                  },
                  key: Key('tens_mode_2'),
                  child: Container(
                    height: 40,
                    width: 40,
                    decoration: BoxDecoration(
                      color: state.selectedMode == 2
                          ? Color(0xffF7A600)
                          : Colors.white,
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: Color(
                              0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                          blurRadius: 4.0, // the blur radius
                          offset: Offset(0, 1), // the x,y offset of the shadow
                        ),
                      ],
                    ),
                    child: Center(
                        child: Text(
                      '2',
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: state.selectedMode == 2
                              ? Colors.white
                              : Color(0xff26204A),
                          fontWeight: FontWeight.w400,
                          fontSize: 16),
                    )),
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                GestureDetector(
                  onTap: () {
                    if (!isTherapyActive) {
                      showTherapyPausedToast(context);
                      return;
                    }
                    BlocProvider.of<DeviceControlTensBloc>(context)
                        .add(DeviceControlTensEvent.changeMode(3));
                  },
                  key: Key('tens_mode_3'),
                  child: Container(
                    height: 40,
                    width: 40,
                    decoration: BoxDecoration(
                      color: state.selectedMode == 3
                          ? Color(0xffF7A600)
                          : Colors.white,
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: Color(
                              0x40000000), // #00000040 in CSS corresponds to 0x40000000 in Flutter
                          blurRadius: 4.0, // the blur radius
                          offset: Offset(0, 1), // the x,y offset of the shadow
                        ),
                      ],
                    ),
                    child: Center(
                        child: Text(
                      '3',
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: state.selectedMode == 3
                              ? Colors.white
                              : Color(0xff26204A),
                          fontWeight: FontWeight.w400,
                          fontSize: 16),
                    )),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
