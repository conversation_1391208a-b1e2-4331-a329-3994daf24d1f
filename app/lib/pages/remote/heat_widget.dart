import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:remote/application/device_control_heat_bloc/device_control_heat_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:remote/application/device_control_heat_watcher_bloc/device_control_heat_watcher_bloc.dart';
import 'package:remote/application/device_control_bloc/device_control_bloc.dart';
import 'package:remote/domain/failures/remote_failures.dart';
import 'package:juno_plus/utils/ui/toast_helper.dart';

class CustomProgressWidget extends StatefulWidget {
  const CustomProgressWidget({Key? key}) : super(key: key);

  @override
  State<CustomProgressWidget> createState() => _CustomProgressWidgetState();
}

class _CustomProgressWidgetState extends State<CustomProgressWidget>
    with TickerProviderStateMixin {
  late AnimationController _blinkController;
  bool _showBlink = false;

  @override
  void initState() {
    super.initState();
    _blinkController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          setState(() {
            _showBlink = !_showBlink;
          });
          _blinkController.reset();
          _blinkController.forward();
        }
      });
    _blinkController.forward();
  }

  @override
  void dispose() {
    _blinkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<DeviceControlHeatBloc, DeviceControlHeatState>(
          listener: (context, state) {
            state.maybeMap(
              changeHeatLevelFailure: (state) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.remoteFailure.maybeMap(
                      serverError: (val) => val.failureMessage,
                      orElse: () => 'Unexpected Error',
                    )),
                  ),
                );
              },
              orElse: () {},
            );
          },
        ),
      ],
      child: BlocBuilder<DeviceControlHeatWatcherBloc,
          DeviceControlHeatWatcherState>(
        builder: (context, state) {
          // Calculate the applying level based on selected and actual levels
          int? applyingLevel;
          if (state.selectedHeatLevel == state.actualHeatLevel) {
            applyingLevel = null;
          } else if (state.selectedHeatLevel > state.actualHeatLevel) {
            applyingLevel = state.actualHeatLevel;
          } else if (state.selectedHeatLevel < state.actualHeatLevel) {
            applyingLevel = state.actualHeatLevel - 1;
          }

          return Container(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Increase Button
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.6),
                        offset: Offset(4, 4),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  child: BlocBuilder<DeviceControlBloc, DeviceControlState>(
                    builder: (context, deviceState) {
                      final isTherapyActive = deviceState.maybeWhen(
                          therapyStateChanged: (active) => active,
                          orElse: () => true);
                      return IconButton(
                        icon: Icon(
                          Icons.add,
                          size: 30,
                        ),
                        onPressed: () {
                          if (!isTherapyActive) {
                            showTherapyPausedToast(context);
                            return;
                          }

                          context.read<DeviceControlHeatBloc>().add(
                                const DeviceControlHeatEvent.increaseHeat(),
                              );
                        },
                      );
                    },
                  ),
                ),
                SizedBox(height: 10),
                // Heat Level Indicator
                Container(
                  width: 35,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.6),
                        offset: Offset(4, 4),
                        blurRadius: 10,
                      ),
                      BoxShadow(
                        color: Colors.white,
                        offset: Offset(-4, -4),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(5.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: List.generate(3, (index) {
                        int levelIndex = 2 - index;
                        Color color;
                        if (levelIndex < state.actualHeatLevel &&
                            levelIndex != applyingLevel) {
                          color = Colors.orange;
                        } else if (applyingLevel != null &&
                            levelIndex == applyingLevel) {
                          color = _showBlink
                              ? Colors.orange.withOpacity(0.7)
                              : Colors.orange.withOpacity(0.2);
                        } else if (levelIndex < state.selectedHeatLevel) {
                          color = Colors.white;
                        } else {
                          color = Colors.grey[300]!;
                        }
                        return Container(
                          height: 55.h,
                          margin: EdgeInsets.symmetric(vertical: 1),
                          decoration: BoxDecoration(
                            color: color,
                            borderRadius: BorderRadius.only(
                              topLeft: levelIndex == 2
                                  ? Radius.circular(30)
                                  : Radius.zero,
                              topRight: levelIndex == 2
                                  ? Radius.circular(30)
                                  : Radius.zero,
                              bottomLeft: levelIndex == 0
                                  ? Radius.circular(30)
                                  : Radius.zero,
                              bottomRight: levelIndex == 0
                                  ? Radius.circular(30)
                                  : Radius.zero,
                            ),
                          ),
                        );
                      }),
                    ),
                  ),
                ),
                SizedBox(height: 10),
                // Decrease Button
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.6),
                        offset: Offset(4, 4),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                  child: BlocBuilder<DeviceControlBloc, DeviceControlState>(
                    builder: (context, deviceState) {
                      final isTherapyActive = deviceState.maybeWhen(
                          therapyStateChanged: (active) => active,
                          orElse: () => true);
                      return IconButton(
                        icon: Icon(
                          Icons.remove,
                          size: 30,
                        ),
                        onPressed: () {
                          if (!isTherapyActive) {
                            showTherapyPausedToast(context);
                            return;
                          }
                          context.read<DeviceControlHeatBloc>().add(
                                const DeviceControlHeatEvent.decreaseHeat(),
                              );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
