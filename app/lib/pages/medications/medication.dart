import 'package:account_management/domain/model/daily_medication_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:design_system/design/theme.dart';
import 'package:auto_route/annotations.dart';
import 'package:account_management/application/daily_medication_bloc/daily_medication_bloc.dart';
import 'package:account_management/di/di.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:juno_plus/pages/medications/medication_cabinet_button.dart';
import '../../custom_widgets/curved_app_bar.dart';
import 'package:timeline_tile/timeline_tile.dart';
import 'package:google_fonts/google_fonts.dart';


@RoutePage()
class MedicationPage extends StatelessWidget {
  const MedicationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<DailyMedicationBloc>()
            ..add(const DailyMedicationEvent.watchStarted()),
        ),
      ],
      child: const MedicationViewPage(),
    );
  }
}

class MedicationViewPage extends StatefulWidget {
  const MedicationViewPage({super.key});

  @override
  State<MedicationViewPage> createState() => _MedicationViewPageState();
}

class _MedicationViewPageState extends State<MedicationViewPage> {
  @override
  Widget build(BuildContext context) {
    return FloatingMedicationCabinetButton(
      child: BlocBuilder<DailyMedicationBloc, DailyMedicationState>(
        builder: (context, state) {
          return state.map(
            initial: (_) => const Center(child: Text('Loading...')),
            loadInProgress: (_) => const Center(child: CircularProgressIndicator()),
            loadFailure: (_) => const Center(child: Text('Error loading medications.')),
            loadSuccess: (s) {
              final meds = s.medications;
              //final groupedMeds = _groupMedicationsByTime(meds);
              final split = splitMedications(meds);
              final grouped = _groupMedicationsByTime(split.withTime);
              final anytime = split.withoutTime;

              return Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xffFAF2DF), Color.fromARGB(255, 247, 224, 255)],
                  ),
                ),
                child: Scaffold(
                  extendBodyBehindAppBar: true,
                  backgroundColor: Colors.transparent,
                  appBar: CurvedAppBar(
                    appBarColor: AppTheme.primaryColor,
                    logoColor: const Color(0xffFAF2DF),
                    height: .35.sw,
                    topLeftIcon: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ),
                  body: SingleChildScrollView(
                    padding: const EdgeInsets.all(22.0),
                    child: Column(
                      children: [
                        SizedBox(height: .35.sw),
                         SizedBox(
                           width: 1.sw,
                          height: 0.08.sh,
                          child: Center(
                            child: Text(
                              "Today's Medications",
                              style: TextStyle(color: Colors.black, fontSize: 25),
                            ),
                          ),
                        ),
                        if (meds.isEmpty)
                          _buildEmptyMessage()
                        else ...[

                          if (anytime.isNotEmpty) ...[
                            SizedBox(height: 0.01.sh),
                            SizedBox(
                              width: 1.sw,
                              height: 0.04.sh,
                              child: Text(
                                "Anytime",
                                style: GoogleFonts.interTight(
                                  color: Colors.black,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            ListView.builder(
                              padding: const EdgeInsets.only(top: 20),
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: anytime.length,
                              itemBuilder: (context, index) {
                                final group = anytime[index];
                                return _buildMedicationCard(group);
                              },
                            ),
                          ],

                          if (grouped.isNotEmpty) ...[
                            SizedBox(height: 0.03.sh),
                            SizedBox(
                              width: 1.sw,
                              height: 0.03.sh,
                              child: Text(
                                "Scheduled",
                                style: GoogleFonts.interTight(
                                  color: Colors.black,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            ListView.builder(
                              padding: const EdgeInsets.only(top: 20),
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: grouped.length,
                              itemBuilder: (context, index) {
                                final group = grouped[index];
                                return _buildMedicationItem(
                                  group,
                                  isFirst: index == 0,
                                  isLast: index == grouped.length - 1,
                                );
                              },
                            ),
                          ],

                          SizedBox(height: 0.2.sh),

                        ],
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildEmptyMessage() {
    return Column(
      children: [
        const Text("No medications today. \n", style: TextStyle(color: Colors.black)),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: const [
            Text("Access Your  ", style: TextStyle(color: Colors.black)),
            Icon(Icons.medical_services_rounded, color: AppTheme.primaryColor),
            Text(" Medicine Cabinet", style: TextStyle(color: Colors.black)),
          ],
        ),
        const Text("to add a new medication.", style: TextStyle(color: Colors.black)),
      ],
    );
  }

  String formatTimeOfDay12h(TimeOfDay time) {
    final now = DateTime.now();
    final dt = DateTime(now.year, now.month, now.day, time.hour, time.minute);
    return DateFormat('h:mm a').format(dt);
  }

  String formatTimeOfDay24h(TimeOfDay tod) {
    final now = DateTime.now();
    final dt = DateTime(now.year, now.month, now.day, tod.hour, tod.minute);
    return DateFormat('HH:mm').format(dt);
  }

  List<List<DailyMedicationModel>> _groupMedicationsByTime(List<DailyMedicationModel> medications) {
    final Map<String, List<DailyMedicationModel>> grouped = {};
    for (final med in medications) {
      final timeKey = _timeOfDayToString(med.time!);
      grouped.putIfAbsent(timeKey, () => []).add(med);
    }
    return grouped.values.toList();
  }


  String _timeOfDayToString(TimeOfDay tod) =>
      '${tod.hour.toString().padLeft(2, '0')}:${tod.minute.toString().padLeft(2, '0')}';

  Widget _buildMedicationCard(DailyMedicationModel med) {
    final use24Hour = MediaQuery.of(context).alwaysUse24HourFormat;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(40),
        boxShadow: const [BoxShadow(color: Color(0x40000000), blurRadius: 4.0, offset: Offset(0, 1))],
      ),
      constraints: BoxConstraints(minHeight: 0.10.sw, minWidth: 0.75.sw),
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.all(10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildPillIcon(),
          SizedBox(width: 10.sp),
          _buildMedicationDetails(med, use24Hour),
          SizedBox(
            width: 0.14.sw,
            height: 0.14.sw,
            child: ToggleCheckWidget(
              loggedTime: med.loggedTime,
              onLoggedTime: (newTime) => setState(() => med.loggedTime = newTime),
              checkedOff: () => setState(() => med.loggedTime = null),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPillIcon() {
    return Container(
      width: 0.14.sw,
      height: 0.14.sw,
      padding: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: const Color(0xfffcecd9),
        borderRadius: BorderRadius.circular(42),
        boxShadow: const [BoxShadow(color: Color(0x40000000), blurRadius: 4.0, offset: Offset(0, 3))],
      ),
      child: Center(
        child: SvgPicture.asset('assets/home/<USER>', height: 50, width: 50),
      ),
    );
  }

  Widget _buildMedicationDetails(DailyMedicationModel med, bool use24Hour) {
    return SizedBox(
      width: .42.sw,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(med.medName!, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w600, color: Color(0xff30285D))),
          const SizedBox(height: 3),
          Row(
            children: [
              Text(med.time == null ? "${med.medDosage} ${med.medDosageUnit}, ${med.medFrequency?.substring(0,1).toUpperCase()}${med.medFrequency!.substring(1, med.medFrequency!.length)}": "${med.medDosage} ${med.medDosageUnit}",
                  style: TextStyle(color: const Color(0xff30285D).withOpacity(0.6))),
            ],
          ),
          const SizedBox(height: 2),
          if (med.loggedTime != null) ...[
            Container(
              width: 70,
              height: 25,
              alignment: Alignment.center,
              decoration: BoxDecoration(color: const Color(0xFFFFD277), borderRadius: BorderRadius.circular(5)),
              child: Text(
                use24Hour ? formatTimeOfDay24h(med.loggedTime!) : formatTimeOfDay12h(med.loggedTime!),
                style: const TextStyle(color: Colors.black, fontSize: 15),
              ),
            ),
          ],
          Visibility(
            visible: med.medNotes != null && med.medNotes!.isNotEmpty,
            child: Text(
              med.medNotes ?? '',
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
              style: const TextStyle(color: Color(0xff30285D)),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildMedicationItem(List<DailyMedicationModel> meds, {required bool isFirst, required bool isLast}) {
    final use24Hour = MediaQuery.of(context).alwaysUse24HourFormat;
    bool isPast(TimeOfDay time) {
      final now = TimeOfDay.now();
      return time.hour < now.hour || (time.hour == now.hour && time.minute < now.minute);
    }
    return TimelineTile(
      alignment: TimelineAlign.manual,
      lineXY: 0.0,
      isFirst: isFirst,
      isLast: isLast,
      indicatorStyle: IndicatorStyle(
        indicatorXY: 0.0,
        width: 25,
        color: isPast(meds.first.time!) ? AppTheme.primaryColor : const Color(0xFFDDDAF5),
        padding: const EdgeInsets.only(right: 10),
      ),
      afterLineStyle: LineStyle(
        color: isPast(meds.first.time!) ? AppTheme.primaryColor : const Color(0xFFDDDAF5),
        thickness: 3,
      ),
      endChild: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            use24Hour ? formatTimeOfDay24h(meds.first.time!) : formatTimeOfDay12h(meds.first.time!),
            style: const TextStyle(fontWeight: FontWeight.w500, color: Colors.black),
          ),
          const SizedBox(height: 12),
          ...meds.map((med) => Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: _buildMedicationCard(med),
          )),
        ],
      ),
    );
  }
}

class ToggleCheckWidget extends StatefulWidget {
  final TimeOfDay? loggedTime;
  final ValueChanged<TimeOfDay> onLoggedTime;
  final VoidCallback checkedOff;

  const ToggleCheckWidget({super.key, required this.loggedTime, required this.onLoggedTime, required this.checkedOff});

  @override
  State<ToggleCheckWidget> createState() => _ToggleCheckWidgetState();
}

class _ToggleCheckWidgetState extends State<ToggleCheckWidget> {
  bool get isChecked => widget.loggedTime != null;

  Future<void> _handleTap() async {
    if (isChecked) {
      widget.checkedOff();
    } else {
      final picked = await showTimePicker(context: context, initialTime: TimeOfDay.now());
      if (picked != null) widget.onLoggedTime(picked);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: isChecked ? const Color(0xFFFFB854) : const Color(0xFFDDDAF5),
          shape: BoxShape.circle,
        ),
        child: Icon(Icons.check_rounded, color: isChecked ? Colors.white : Colors.grey[700], size: 30),
      ),
    );
  }
}

class SplitMeds {
  final List<DailyMedicationModel> withTime;
  final List<DailyMedicationModel> withoutTime;

  SplitMeds({
    required this.withTime,
    required this.withoutTime,
  });
}

SplitMeds splitMedications(List<DailyMedicationModel> medications) {
  final List<DailyMedicationModel> withTime = [];
  final List<DailyMedicationModel> withoutTime = [];

  for (final med in medications) {
    if (med.time == null) {
      withoutTime.add(med);
    } else {
      withTime.add(med);
    }
  }

  return SplitMeds(withTime: withTime, withoutTime: withoutTime);
}





