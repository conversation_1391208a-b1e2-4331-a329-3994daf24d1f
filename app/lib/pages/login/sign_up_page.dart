import 'dart:io';
import 'package:auto_route/auto_route.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../helpers.dart';
import 'package:authentication/authentication.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../routing/app_pages.gr.dart';

@RoutePage()
class SignUpPage extends StatelessWidget {
  const SignUpPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xffFAF2DF),
            const Color(0xFFF7E0FF),
          ],
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        resizeToAvoidBottomInset: true,
        appBar: CurvedAppBar(
          appBarColor: AppTheme.primaryColor,
          logoColor: AppTheme.loginAppBarColor,
          height: 0.35.sw,
          topLeftIcon: IconButton(
            icon: const Icon(Icons.arrow_back_rounded, color: Colors.white),
            onPressed: () => context.router.pop(),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              BlocProvider(
                create: (context) => getIt<SignInBloc>(),
                child: EmailRegisterWrapper(
                  child: Container(
                    width: 0.7.sw,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(30),
                      color: Theme.of(context).primaryColor,
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      "Sign up",
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontSize: 20,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  onRegisterSuccess: (user) {
                    user.isEmailVerified!
                        ? (user.isOnboarded!
                        ? context.router.push(HomeRoute())
                        : context.router.push(GetStartedRoute()))
                        : context.router.push(EmailVerificationRoute());
                  },
                  onRegisterFailure: (failure) {},
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: const [
                  Expanded(child: Divider(color: Color(0xff554C9F))),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text("or", style: TextStyle(color: Color(0xff554C9F))),
                  ),
                  Expanded(child: Divider(color: Color(0xff554C9F))),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Platform.isIOS
                      ? BlocProvider(
                          create: (context) => getIt<SignInBloc>(),
                          child: AppleSignInButtonWrapper(
                            buttonChild: Container(
                              margin: EdgeInsets.only(right: 25),
                              width: 70,
                              height: 70,
                              padding: EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Color(0x40000000),
                                    blurRadius: 4.0,
                                    offset: Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: SvgPicture.asset(
                                'assets/social_icons/apple_social_icon.svg',
                                height: 30,
                                width: 30,
                              ),
                            ),
                            onSignInSuccess: (user) {
                              user.isOnboarded == true
                                  ? context.router.push(HomeRoute())
                                  : context.router.push(GetStartedRoute());
                            },
                            onSignInFailure: (failure) {},
                          ),
                        )
                      : Container(),
                  Container(
                    child: BlocProvider(
                      create: (context) => getIt<SignInBloc>(),
                      child: GoogleSignInButtonWrapper(
                        buttonChild: Container(
                          width: 70,
                          height: 70,
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Color(0x40000000),
                                blurRadius: 4.0,
                                offset: Offset(0, 4),
                              ),
                            ],
                          ),
                          child: SvgPicture.asset(
                            'assets/social_icons/google_social_icon.svg',
                            height: 30,
                            width: 30,
                          ),
                        ),
                        onSignInSuccess: (user) {
                          user.isOnboarded == true
                              ? context.router.push(HomeRoute())
                              : context.router.push(GetStartedRoute());
                        },
                        onSignInFailure: (failure) {
                          // Handle sign-in failure
                        },
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 20),
              GestureDetector(
                onTap: () {
                  context.router.push(const LoginRoute());
                },
                child: Container(
                  width: .7.sw,
                  height: .12.sw,
                  child: Container(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                            child: Text(
                          "Already have an account? ",
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              fontSize: 16,
                              color: Theme.of(context).primaryColor),
                        )),
                        Container(
                          child: Text(
                            "Log In",
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w800,
                                  decoration: TextDecoration.underline,
                                  decorationColor: Theme.of(context).primaryColor,
                                  color: Theme.of(context).primaryColor,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
