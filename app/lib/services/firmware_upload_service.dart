import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/services.dart';
import 'package:crypto/crypto.dart';
import 'package:bluetooth/domain/model/ota_firmware_model.dart';

/// Service for uploading firmware files from assets to Firebase Storage and Firestore
class FirmwareUploadService {
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// List of firmware files in assets
  static const List<String> firmwareAssets = [
    'assets/firmware/dfu_application.zip',
    'assets/firmware/dfu_application_1.zip',
    'assets/firmware/dfu_application_2.zip',
  ];

  /// Upload all firmware files from assets to Firebase
  Future<List<String>> uploadAllFirmware() async {
    final results = <String>[];
    
    for (int i = 0; i < firmwareAssets.length; i++) {
      final assetPath = firmwareAssets[i];
      try {
        print('📤 [FIRMWARE UPLOAD] Starting upload for: $assetPath');
        
        // Load firmware data from assets
        final firmwareData = await rootBundle.load(assetPath);
        final bytes = firmwareData.buffer.asUint8List();
        
        // Generate firmware metadata
        final fileName = assetPath.split('/').last;
        final version = _extractVersionFromFileName(fileName, i);
        final firmwareId = 'juno_v1_${version.replaceAll('.', '_')}_${DateTime.now().millisecondsSinceEpoch}';
        
        // Upload to Firebase Storage
        final downloadUrl = await _uploadToStorage(firmwareId, fileName, bytes);
        
        // Calculate checksum
        final checksum = _calculateChecksum(bytes);
        
        // Create firmware model
        final firmware = OtaFirmwareModel(
          firmwareId: firmwareId,
          deviceType: 'juno_v1',
          downloadUrl: downloadUrl,
          version: version,
          createdAt: DateTime.now().subtract(Duration(days: firmwareAssets.length - i)), // Stagger dates
          fileSize: bytes.length,
          checksum: checksum,
          buildDate: DateTime.now().subtract(Duration(days: firmwareAssets.length - i)).toIso8601String().split('T')[0],
          hardwareVersion: '1.0',
          isStable: true,
          releaseNotes: _generateReleaseNotes(version),
          minBootloaderVersion: '1.0.0',
          metadata: {
            'buildNumber': '${12345 + i}',
            'gitCommit': 'abc123def${456 + i}',
            'buildEnvironment': 'production',
            'originalAssetPath': assetPath,
          },
        );
        
        // Save to Firestore
        await _saveToFirestore(firmware);
        
        results.add('✅ Uploaded $fileName as version $version');
        print('✅ [FIRMWARE UPLOAD] Successfully uploaded: $fileName');
        
      } catch (e) {
        final errorMsg = '❌ Failed to upload $assetPath: $e';
        results.add(errorMsg);
        print('❌ [FIRMWARE UPLOAD] Error uploading $assetPath: $e');
      }
    }
    
    return results;
  }

  /// Upload a single firmware file to Firebase Storage
  Future<String> _uploadToStorage(String firmwareId, String fileName, Uint8List data) async {
    try {
      // Create storage reference
      final storageRef = _storage.ref().child('firmware/juno_v1/$firmwareId.zip');
      
      // Upload file
      final uploadTask = storageRef.putData(
        data,
        SettableMetadata(
          contentType: 'application/zip',
          customMetadata: {
            'originalFileName': fileName,
            'uploadedAt': DateTime.now().toIso8601String(),
          },
        ),
      );
      
      // Wait for upload to complete
      final snapshot = await uploadTask;
      
      // Get download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      
      print('📤 [STORAGE] Uploaded to: $downloadUrl');
      return downloadUrl;
      
    } catch (e) {
      print('❌ [STORAGE] Upload failed: $e');
      rethrow;
    }
  }

  /// Save firmware metadata to Firestore
  Future<void> _saveToFirestore(OtaFirmwareModel firmware) async {
    try {
      await _firestore
          .collection('ota_firmwares')
          .doc(firmware.firmwareId)
          .set(firmware.toJson());
      
      print('💾 [FIRESTORE] Saved firmware metadata: ${firmware.firmwareId}');
      
    } catch (e) {
      print('❌ [FIRESTORE] Save failed: $e');
      rethrow;
    }
  }

  /// Extract version from filename
  String _extractVersionFromFileName(String fileName, int index) {
    // Extract version from filename or generate based on index
    if (fileName.contains('dfu_application_2')) {
      return '2.1.0';
    } else if (fileName.contains('dfu_application_1')) {
      return '2.0.1';
    } else if (fileName.contains('dfu_application.zip')) {
      return '2.0.0';
    } else {
      return '2.${index}.0';
    }
  }

  /// Calculate SHA-256 checksum
  String _calculateChecksum(Uint8List data) {
    final digest = sha256.convert(data);
    return 'sha256:${digest.toString()}';
  }

  /// Generate release notes based on version
  String _generateReleaseNotes(String version) {
    switch (version) {
      case '2.1.0':
        return 'Latest firmware with improved performance, enhanced security features, and bug fixes. '
               'Includes new therapy modes and better battery optimization.';
      case '2.0.1':
        return 'Bug fixes and stability improvements. Resolved connectivity issues and improved '
               'device responsiveness during therapy sessions.';
      case '2.0.0':
        return 'Major firmware update with new features including enhanced therapy algorithms, '
               'improved user interface, and better device diagnostics.';
      default:
        return 'Firmware update with performance improvements and bug fixes.';
    }
  }

  /// Check if firmware already exists in Firestore
  Future<bool> firmwareExists(String version) async {
    try {
      final query = await _firestore
          .collection('ota_firmwares')
          .where('deviceType', isEqualTo: 'juno_v1')
          .where('version', isEqualTo: version)
          .limit(1)
          .get();
      
      return query.docs.isNotEmpty;
    } catch (e) {
      print('❌ [FIRESTORE] Error checking firmware existence: $e');
      return false;
    }
  }

  /// Delete all firmware from Firebase (for testing purposes)
  Future<List<String>> deleteAllFirmware() async {
    final results = <String>[];
    
    try {
      // Delete from Firestore
      final query = await _firestore
          .collection('ota_firmwares')
          .where('deviceType', isEqualTo: 'juno_v1')
          .get();
      
      for (final doc in query.docs) {
        await doc.reference.delete();
        results.add('🗑️ Deleted Firestore document: ${doc.id}');
      }
      
      // Delete from Storage (list all files in firmware/juno_v1/)
      final storageRef = _storage.ref().child('firmware/juno_v1/');
      final listResult = await storageRef.listAll();
      
      for (final item in listResult.items) {
        await item.delete();
        results.add('🗑️ Deleted Storage file: ${item.name}');
      }
      
      results.add('✅ All firmware files deleted successfully');
      
    } catch (e) {
      final errorMsg = '❌ Error deleting firmware: $e';
      results.add(errorMsg);
      print('❌ [CLEANUP] $errorMsg');
    }
    
    return results;
  }

  /// Get upload progress (placeholder for future implementation)
  Stream<double> getUploadProgress() {
    // This could be implemented to track upload progress
    // For now, return a simple stream
    return Stream.value(1.0);
  }
}
