import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:design_system/design_system.dart';

class PeriodIconButton extends StatelessWidget {
  final bool isSelected;
  final bool isGapFilled;
  final VoidCallback? onTap;
  final double size;
  final bool isEnabled;

  const PeriodIconButton({
    required this.isSelected,
    required this.size,
    this.isGapFilled = false,
    this.onTap,
    this.isEnabled = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isEnabled ? onTap : null,
      child: Container(
        width: size.w,
        height: size.h,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Circular background for selected dates or gap-filled dates
            if (isSelected || isGapFilled)
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                width: size.w,
                height: size.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppTheme.primaryColor.withValues(alpha: 0.2),
                  border: Border.all(
                    color: AppTheme.primaryColor.withValues(alpha: 0.4),
                    width: 1.0,
                  ),
                ),
              ),
            // Period icon
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: (size * 0.6).w,
              height: (size * 0.6).h,
              child: SvgPicture.asset(
                'assets/settings/period.svg',
                width: (size * 0.6).w,
                height: (size * 0.6).h,
                colorFilter: ColorFilter.mode(
                  _getIconColor(),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getIconColor() {
    if (!isEnabled) {
      return Colors.grey.shade400;
    }

    if (isSelected) {
      // Filled/solid icon for user-selected dates
      return AppTheme.primaryColor;
    } else if (isGapFilled) {
      // Lighter color for gap-filled dates (no icon, just background)
      return Colors.transparent;
    } else {
      // Outlined icon for unselected dates
      return AppTheme.primaryColor.withValues(alpha: 0.6);
    }
  }
}
