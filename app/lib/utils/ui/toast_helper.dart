import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

void showToast(
  BuildContext context,
  String message, {
  Color backgroundColor = Colors.black87,
  Duration duration = const Duration(seconds: 2),
}) async {
  try {
    // Choose toast length based on duration (approximate)
    final toastLength =
        duration.inSeconds > 2 ? Toast.LENGTH_LONG : Toast.LENGTH_SHORT;
    await Fluttertoast.showToast(
      msg: message,
      toastLength: toastLength,
      gravity: ToastGravity.BOTTOM,
      timeInSecForIosWeb: duration.inSeconds,
      backgroundColor: backgroundColor,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  } catch (e) {
    // Fallback to a SnackBar if Fluttertoast fails for any platform or integration reason
    try {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text(message)));
    } catch (_) {
      // As a last resort, swallow the error to avoid crashing the app
    }
  }
}

void showTherapyPausedToast(BuildContext context) {
  showToast(context, 'Therapy is paused — please resume therapy first');
}
