plugins {
    id "com.android.application"
    id "kotlin-android"
    id 'com.google.gms.google-services'
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode', '1').toInteger()
def flutterVersionName = localProperties.getProperty('flutter.versionName', '1.0')

def keystorePropertiesFile = rootProject.file("keystore.properties")
def keystoreProperties = new Properties()
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
} else {
    keystoreProperties['uatStoreFile'] = 'dummy.keystore'
    keystoreProperties['prodStoreFile'] = 'dummy.keystore'
}

android {
    namespace "com.junotechno.junoplus" // Set the namespace to match your applicationId
    compileSdk 35
    ndkVersion "27.0.12077973"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.junotechno.junoplus"
        minSdkVersion 24 // Change this to at least 19
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode
        versionName flutterVersionName
        multiDexEnabled true
        testInstrumentationRunner "pl.leancode.patrol.PatrolJUnitRunner"
        testInstrumentationRunnerArguments clearPackageData: "true"
    }

    flavorDimensions "flavor-type"

    signingConfigs {
        uatRelease {
            storeFile file(keystoreProperties['uatStoreFile'])
            storePassword keystoreProperties['uatStorePassword']
            keyAlias keystoreProperties['uatKeyAlias']
            keyPassword keystoreProperties['uatKeyPassword']
        }
        prodRelease {
            storeFile file(keystoreProperties['prodStoreFile'])
            storePassword keystoreProperties['prodStorePassword']
            keyAlias keystoreProperties['prodKeyAlias']
            keyPassword keystoreProperties['prodKeyPassword']
        }
    }

    productFlavors {
        dev {
            dimension "flavor-type"
            resValue "string", "app_name", "Juno+ - Dev"
            applicationIdSuffix ".dev"
            versionNameSuffix "-dev"
            signingConfig signingConfigs.prodRelease  // Using prod signing config for dev
        }
        uat {
            dimension "flavor-type"
            resValue "string", "app_name", "Juno+ - UAT"
            applicationIdSuffix ".uat"
            versionNameSuffix "-uat"
            signingConfig signingConfigs.uatRelease
        }
        prod {
            dimension "flavor-type"
            resValue "string", "app_name", "Juno+"
            signingConfig signingConfigs.prodRelease
        }
    }

    testOptions {
        execution "ANDROIDX_TEST_ORCHESTRATOR"
    }

    buildTypes {
        release {
            signingConfig signingConfigs.prodRelease
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    // ... (other dependencies)

    implementation platform('com.google.firebase:firebase-bom:33.7.0')

    // Add the plain Java version of Analytics explicitly if not already there
    implementation 'com.google.firebase:firebase-analytics'
    // Keep the KTX version if you are using Kotlin extensions in your app code
    implementation 'com.google.firebase:firebase-analytics-ktx:22.1.2'

    implementation 'com.google.firebase:firebase-messaging-ktx:24.1.0'
    implementation 'androidx.multidex:multidex:2.0.1'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
    implementation 'org.slf4j:slf4j-api:1.7.36'
    implementation 'org.slf4j:slf4j-simple:1.7.36'

    // Single Play Core dependency for split installation support
    implementation 'com.google.android.play:feature-delivery:2.1.0'

    // ... (other dependencies)
}
