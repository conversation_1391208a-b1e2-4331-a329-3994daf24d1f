import 'dart:async';

import 'package:flutter/foundation.dart';

/// Manages adaptive polling for device data updates
///
/// Responsible for scheduling and executing polls at appropriate intervals
/// based on user activity and device state
class PollingManager {
  // Mutex locks for preventing concurrent operations
  bool _isHeatPolling = false;
  bool _isTensPolling = false;
  bool _isDeviceStatusPolling = false;

  // User activity timestamps
  DateTime _lastHeatActivity = DateTime.now();
  DateTime _lastTensActivity = DateTime.now();

  // Error tracking for safety
  int _consecutiveErrors = 0;
  static const int _maxConsecutiveErrors = 5;

  // Single timer for all polling operations
  Timer? _pollingTimer;

  // Polling intervals - made less aggressive to prevent system overload
  static const Duration _normalPollInterval = Duration(seconds: 20);
  static const Duration _rapidPollInterval = Duration(seconds: 8);
  static const Duration _activityThreshold = Duration(seconds: 45);
  static const Duration _afterCommandReadDelay = Duration(milliseconds: 500);

  // Polling callbacks
  final Future<void> Function() _onPollHeat;
  final Future<void> Function() _onPollTens;
  final Future<void> Function() _onPollDeviceStatus;

  PollingManager({
    required Future<void> Function() onPollHeat,
    required Future<void> Function() onPollTens,
    required Future<void> Function() onPollDeviceStatus,
  })  : _onPollHeat = onPollHeat,
        _onPollTens = onPollTens,
        _onPollDeviceStatus = onPollDeviceStatus {
    _scheduleNextPoll();
  }

  /// Start adaptive polling based on user activity
  void _scheduleNextPoll() {
    // Cancel existing timer
    _pollingTimer?.cancel();

    // Determine appropriate polling interval based on user activity
    final now = DateTime.now();
    final sinceLastHeatActivity = now.difference(_lastHeatActivity);
    final sinceLastTensActivity = now.difference(_lastTensActivity);

    // Use rapid polling if there was recent activity
    final bool isRecentActivity = sinceLastHeatActivity < _activityThreshold ||
        sinceLastTensActivity < _activityThreshold;

    final interval =
        isRecentActivity ? _rapidPollInterval : _normalPollInterval;

    // Create new timer with the calculated interval
    _pollingTimer = Timer(interval, () {
      _executePoll(isRapidPolling: isRecentActivity);
    });
  }

  /// Execute the scheduled poll operations
  Future<void> _executePoll({bool isRapidPolling = false}) async {
    // Check if polling is stopped before executing
    if (_pollingTimer == null) {
      debugPrint('Polling stopped, skipping execution');
      return;
    }

    // Check if we have too many consecutive errors
    if (_consecutiveErrors >= _maxConsecutiveErrors) {
      debugPrint(
          'Too many consecutive polling errors ($_consecutiveErrors), stopping polling');
      stopAllPolling();
      return;
    }

    bool hasError = false;

    try {
      // Run appropriate polls based on activity and need
      if (!_isHeatPolling) {
        _isHeatPolling = true;
        try {
          await _onPollHeat();
        } catch (e) {
          debugPrint('Error in heat polling: $e');
          hasError = true;
        } finally {
          _isHeatPolling = false;
        }
      }

      if (!_isTensPolling) {
        _isTensPolling = true;
        try {
          await _onPollTens();
        } catch (e) {
          debugPrint('Error in TENS polling: $e');
          hasError = true;
        } finally {
          _isTensPolling = false;
        }
      }

      // Only check device status during normal polling to save resources
      if (!isRapidPolling && !_isDeviceStatusPolling) {
        _isDeviceStatusPolling = true;
        try {
          await _onPollDeviceStatus();
        } catch (e) {
          debugPrint('Error in device status polling: $e');
          hasError = true;
        } finally {
          _isDeviceStatusPolling = false;
        }
      }

      // Reset error count on successful poll
      if (!hasError) {
        _consecutiveErrors = 0;
      } else {
        _consecutiveErrors++;
      }
    } catch (e) {
      debugPrint('Error during polling execution: $e');
      _consecutiveErrors++;
    } finally {
      // Only schedule next poll if polling hasn't been stopped
      if (_pollingTimer != null) {
        _scheduleNextPoll();
      }
    }
  }

  /// Record heat-related user activity to trigger rapid polling
  void markHeatActivity() {
    _lastHeatActivity = DateTime.now();
    // Reschedule polling immediately when there's user activity
    _scheduleNextPoll();
  }

  /// Record TENS-related user activity to trigger rapid polling
  void markTensActivity() {
    _lastTensActivity = DateTime.now();
    // Reschedule polling immediately when there's user activity
    _scheduleNextPoll();
  }

  /// Request an immediate poll for all features
  void requestImmediatePoll() {
    _pollingTimer?.cancel();
    _executePoll(isRapidPolling: true);
  }

  /// Stop all polling activities immediately
  ///
  /// This should be called when a device is disconnected to prevent
  /// unnecessary polling attempts to an unavailable device
  void stopAllPolling() {
    debugPrint('Stopping all polling activities');
    _pollingTimer?.cancel();
    _pollingTimer = null;

    // Reset polling flags to ensure clean state
    _isHeatPolling = false;
    _isTensPolling = false;
    _isDeviceStatusPolling = false;
  }

  /// Reset error count and restart polling
  ///
  /// This should be called when device connection is restored
  void resetAndRestart() {
    debugPrint('Resetting polling error count and restarting');
    _consecutiveErrors = 0;
    if (_pollingTimer == null) {
      _scheduleNextPoll();
    }
  }

  /// Clean up resources
  void dispose() {
    _pollingTimer?.cancel();
    _pollingTimer = null;
  }
}
