import 'package:freezed_annotation/freezed_annotation.dart';
part 'remote_failures.freezed.dart';

@freezed
abstract class RemoteFailure with _$RemoteFailure {
  const factory RemoteFailure.serverError(String failureMessage) = ServerError;
  const factory RemoteFailure.noInternetConnection(String failureMessage) =
      NoInternetConnection;
  const factory RemoteFailure.heatLevelFailure(String failureMessage) =
      HeatLevelFailure;
  const factory RemoteFailure.tensLevelFailure(String failureMessage) =
      TensLevelFailure;
  const factory RemoteFailure.tensModeFailure(String failureMessage) =
      TensModeFailure;
  const factory RemoteFailure.unexpectedFailure(String failureMessage) =
      UnexpectedFailure;

}
