// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'remote_failures.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$RemoteFailure {
  String get failureMessage;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RemoteFailureCopyWith<RemoteFailure> get copyWith =>
      _$RemoteFailureCopyWithImpl<RemoteFailure>(
          this as RemoteFailure, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RemoteFailure &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  @override
  String toString() {
    return 'RemoteFailure(failureMessage: $failureMessage)';
  }
}

/// @nodoc
abstract mixin class $RemoteFailureCopyWith<$Res> {
  factory $RemoteFailureCopyWith(
          RemoteFailure value, $Res Function(RemoteFailure) _then) =
      _$RemoteFailureCopyWithImpl;
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$RemoteFailureCopyWithImpl<$Res>
    implements $RemoteFailureCopyWith<$Res> {
  _$RemoteFailureCopyWithImpl(this._self, this._then);

  final RemoteFailure _self;
  final $Res Function(RemoteFailure) _then;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(_self.copyWith(
      failureMessage: null == failureMessage
          ? _self.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [RemoteFailure].
extension RemoteFailurePatterns on RemoteFailure {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(ServerError value)? serverError,
    TResult Function(NoInternetConnection value)? noInternetConnection,
    TResult Function(HeatLevelFailure value)? heatLevelFailure,
    TResult Function(TensLevelFailure value)? tensLevelFailure,
    TResult Function(TensModeFailure value)? tensModeFailure,
    TResult Function(UnexpectedFailure value)? unexpectedFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case ServerError() when serverError != null:
        return serverError(_that);
      case NoInternetConnection() when noInternetConnection != null:
        return noInternetConnection(_that);
      case HeatLevelFailure() when heatLevelFailure != null:
        return heatLevelFailure(_that);
      case TensLevelFailure() when tensLevelFailure != null:
        return tensLevelFailure(_that);
      case TensModeFailure() when tensModeFailure != null:
        return tensModeFailure(_that);
      case UnexpectedFailure() when unexpectedFailure != null:
        return unexpectedFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(ServerError value) serverError,
    required TResult Function(NoInternetConnection value) noInternetConnection,
    required TResult Function(HeatLevelFailure value) heatLevelFailure,
    required TResult Function(TensLevelFailure value) tensLevelFailure,
    required TResult Function(TensModeFailure value) tensModeFailure,
    required TResult Function(UnexpectedFailure value) unexpectedFailure,
  }) {
    final _that = this;
    switch (_that) {
      case ServerError():
        return serverError(_that);
      case NoInternetConnection():
        return noInternetConnection(_that);
      case HeatLevelFailure():
        return heatLevelFailure(_that);
      case TensLevelFailure():
        return tensLevelFailure(_that);
      case TensModeFailure():
        return tensModeFailure(_that);
      case UnexpectedFailure():
        return unexpectedFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(ServerError value)? serverError,
    TResult? Function(NoInternetConnection value)? noInternetConnection,
    TResult? Function(HeatLevelFailure value)? heatLevelFailure,
    TResult? Function(TensLevelFailure value)? tensLevelFailure,
    TResult? Function(TensModeFailure value)? tensModeFailure,
    TResult? Function(UnexpectedFailure value)? unexpectedFailure,
  }) {
    final _that = this;
    switch (_that) {
      case ServerError() when serverError != null:
        return serverError(_that);
      case NoInternetConnection() when noInternetConnection != null:
        return noInternetConnection(_that);
      case HeatLevelFailure() when heatLevelFailure != null:
        return heatLevelFailure(_that);
      case TensLevelFailure() when tensLevelFailure != null:
        return tensLevelFailure(_that);
      case TensModeFailure() when tensModeFailure != null:
        return tensModeFailure(_that);
      case UnexpectedFailure() when unexpectedFailure != null:
        return unexpectedFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failureMessage)? serverError,
    TResult Function(String failureMessage)? noInternetConnection,
    TResult Function(String failureMessage)? heatLevelFailure,
    TResult Function(String failureMessage)? tensLevelFailure,
    TResult Function(String failureMessage)? tensModeFailure,
    TResult Function(String failureMessage)? unexpectedFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case ServerError() when serverError != null:
        return serverError(_that.failureMessage);
      case NoInternetConnection() when noInternetConnection != null:
        return noInternetConnection(_that.failureMessage);
      case HeatLevelFailure() when heatLevelFailure != null:
        return heatLevelFailure(_that.failureMessage);
      case TensLevelFailure() when tensLevelFailure != null:
        return tensLevelFailure(_that.failureMessage);
      case TensModeFailure() when tensModeFailure != null:
        return tensModeFailure(_that.failureMessage);
      case UnexpectedFailure() when unexpectedFailure != null:
        return unexpectedFailure(_that.failureMessage);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failureMessage) serverError,
    required TResult Function(String failureMessage) noInternetConnection,
    required TResult Function(String failureMessage) heatLevelFailure,
    required TResult Function(String failureMessage) tensLevelFailure,
    required TResult Function(String failureMessage) tensModeFailure,
    required TResult Function(String failureMessage) unexpectedFailure,
  }) {
    final _that = this;
    switch (_that) {
      case ServerError():
        return serverError(_that.failureMessage);
      case NoInternetConnection():
        return noInternetConnection(_that.failureMessage);
      case HeatLevelFailure():
        return heatLevelFailure(_that.failureMessage);
      case TensLevelFailure():
        return tensLevelFailure(_that.failureMessage);
      case TensModeFailure():
        return tensModeFailure(_that.failureMessage);
      case UnexpectedFailure():
        return unexpectedFailure(_that.failureMessage);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failureMessage)? serverError,
    TResult? Function(String failureMessage)? noInternetConnection,
    TResult? Function(String failureMessage)? heatLevelFailure,
    TResult? Function(String failureMessage)? tensLevelFailure,
    TResult? Function(String failureMessage)? tensModeFailure,
    TResult? Function(String failureMessage)? unexpectedFailure,
  }) {
    final _that = this;
    switch (_that) {
      case ServerError() when serverError != null:
        return serverError(_that.failureMessage);
      case NoInternetConnection() when noInternetConnection != null:
        return noInternetConnection(_that.failureMessage);
      case HeatLevelFailure() when heatLevelFailure != null:
        return heatLevelFailure(_that.failureMessage);
      case TensLevelFailure() when tensLevelFailure != null:
        return tensLevelFailure(_that.failureMessage);
      case TensModeFailure() when tensModeFailure != null:
        return tensModeFailure(_that.failureMessage);
      case UnexpectedFailure() when unexpectedFailure != null:
        return unexpectedFailure(_that.failureMessage);
      case _:
        return null;
    }
  }
}

/// @nodoc

class ServerError implements RemoteFailure {
  const ServerError(this.failureMessage);

  @override
  final String failureMessage;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ServerErrorCopyWith<ServerError> get copyWith =>
      _$ServerErrorCopyWithImpl<ServerError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ServerError &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  @override
  String toString() {
    return 'RemoteFailure.serverError(failureMessage: $failureMessage)';
  }
}

/// @nodoc
abstract mixin class $ServerErrorCopyWith<$Res>
    implements $RemoteFailureCopyWith<$Res> {
  factory $ServerErrorCopyWith(
          ServerError value, $Res Function(ServerError) _then) =
      _$ServerErrorCopyWithImpl;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$ServerErrorCopyWithImpl<$Res> implements $ServerErrorCopyWith<$Res> {
  _$ServerErrorCopyWithImpl(this._self, this._then);

  final ServerError _self;
  final $Res Function(ServerError) _then;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(ServerError(
      null == failureMessage
          ? _self.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class NoInternetConnection implements RemoteFailure {
  const NoInternetConnection(this.failureMessage);

  @override
  final String failureMessage;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $NoInternetConnectionCopyWith<NoInternetConnection> get copyWith =>
      _$NoInternetConnectionCopyWithImpl<NoInternetConnection>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is NoInternetConnection &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  @override
  String toString() {
    return 'RemoteFailure.noInternetConnection(failureMessage: $failureMessage)';
  }
}

/// @nodoc
abstract mixin class $NoInternetConnectionCopyWith<$Res>
    implements $RemoteFailureCopyWith<$Res> {
  factory $NoInternetConnectionCopyWith(NoInternetConnection value,
          $Res Function(NoInternetConnection) _then) =
      _$NoInternetConnectionCopyWithImpl;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$NoInternetConnectionCopyWithImpl<$Res>
    implements $NoInternetConnectionCopyWith<$Res> {
  _$NoInternetConnectionCopyWithImpl(this._self, this._then);

  final NoInternetConnection _self;
  final $Res Function(NoInternetConnection) _then;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(NoInternetConnection(
      null == failureMessage
          ? _self.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class HeatLevelFailure implements RemoteFailure {
  const HeatLevelFailure(this.failureMessage);

  @override
  final String failureMessage;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $HeatLevelFailureCopyWith<HeatLevelFailure> get copyWith =>
      _$HeatLevelFailureCopyWithImpl<HeatLevelFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is HeatLevelFailure &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  @override
  String toString() {
    return 'RemoteFailure.heatLevelFailure(failureMessage: $failureMessage)';
  }
}

/// @nodoc
abstract mixin class $HeatLevelFailureCopyWith<$Res>
    implements $RemoteFailureCopyWith<$Res> {
  factory $HeatLevelFailureCopyWith(
          HeatLevelFailure value, $Res Function(HeatLevelFailure) _then) =
      _$HeatLevelFailureCopyWithImpl;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$HeatLevelFailureCopyWithImpl<$Res>
    implements $HeatLevelFailureCopyWith<$Res> {
  _$HeatLevelFailureCopyWithImpl(this._self, this._then);

  final HeatLevelFailure _self;
  final $Res Function(HeatLevelFailure) _then;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(HeatLevelFailure(
      null == failureMessage
          ? _self.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class TensLevelFailure implements RemoteFailure {
  const TensLevelFailure(this.failureMessage);

  @override
  final String failureMessage;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TensLevelFailureCopyWith<TensLevelFailure> get copyWith =>
      _$TensLevelFailureCopyWithImpl<TensLevelFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TensLevelFailure &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  @override
  String toString() {
    return 'RemoteFailure.tensLevelFailure(failureMessage: $failureMessage)';
  }
}

/// @nodoc
abstract mixin class $TensLevelFailureCopyWith<$Res>
    implements $RemoteFailureCopyWith<$Res> {
  factory $TensLevelFailureCopyWith(
          TensLevelFailure value, $Res Function(TensLevelFailure) _then) =
      _$TensLevelFailureCopyWithImpl;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$TensLevelFailureCopyWithImpl<$Res>
    implements $TensLevelFailureCopyWith<$Res> {
  _$TensLevelFailureCopyWithImpl(this._self, this._then);

  final TensLevelFailure _self;
  final $Res Function(TensLevelFailure) _then;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(TensLevelFailure(
      null == failureMessage
          ? _self.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class TensModeFailure implements RemoteFailure {
  const TensModeFailure(this.failureMessage);

  @override
  final String failureMessage;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TensModeFailureCopyWith<TensModeFailure> get copyWith =>
      _$TensModeFailureCopyWithImpl<TensModeFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TensModeFailure &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  @override
  String toString() {
    return 'RemoteFailure.tensModeFailure(failureMessage: $failureMessage)';
  }
}

/// @nodoc
abstract mixin class $TensModeFailureCopyWith<$Res>
    implements $RemoteFailureCopyWith<$Res> {
  factory $TensModeFailureCopyWith(
          TensModeFailure value, $Res Function(TensModeFailure) _then) =
      _$TensModeFailureCopyWithImpl;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$TensModeFailureCopyWithImpl<$Res>
    implements $TensModeFailureCopyWith<$Res> {
  _$TensModeFailureCopyWithImpl(this._self, this._then);

  final TensModeFailure _self;
  final $Res Function(TensModeFailure) _then;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(TensModeFailure(
      null == failureMessage
          ? _self.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class UnexpectedFailure implements RemoteFailure {
  const UnexpectedFailure(this.failureMessage);

  @override
  final String failureMessage;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UnexpectedFailureCopyWith<UnexpectedFailure> get copyWith =>
      _$UnexpectedFailureCopyWithImpl<UnexpectedFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UnexpectedFailure &&
            (identical(other.failureMessage, failureMessage) ||
                other.failureMessage == failureMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureMessage);

  @override
  String toString() {
    return 'RemoteFailure.unexpectedFailure(failureMessage: $failureMessage)';
  }
}

/// @nodoc
abstract mixin class $UnexpectedFailureCopyWith<$Res>
    implements $RemoteFailureCopyWith<$Res> {
  factory $UnexpectedFailureCopyWith(
          UnexpectedFailure value, $Res Function(UnexpectedFailure) _then) =
      _$UnexpectedFailureCopyWithImpl;
  @override
  @useResult
  $Res call({String failureMessage});
}

/// @nodoc
class _$UnexpectedFailureCopyWithImpl<$Res>
    implements $UnexpectedFailureCopyWith<$Res> {
  _$UnexpectedFailureCopyWithImpl(this._self, this._then);

  final UnexpectedFailure _self;
  final $Res Function(UnexpectedFailure) _then;

  /// Create a copy of RemoteFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureMessage = null,
  }) {
    return _then(UnexpectedFailure(
      null == failureMessage
          ? _self.failureMessage
          : failureMessage // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
