// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_status_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DeviceStatusWatcherEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeviceStatusWatcherEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceStatusWatcherEvent()';
  }
}

/// @nodoc
class $DeviceStatusWatcherEventCopyWith<$Res> {
  $DeviceStatusWatcherEventCopyWith(
      DeviceStatusWatcherEvent _, $Res Function(DeviceStatusWatcherEvent) __);
}

/// Adds pattern-matching-related methods to [DeviceStatusWatcherEvent].
extension DeviceStatusWatcherEventPatterns on DeviceStatusWatcherEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_DataReceived value)? dataReceived,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted(_that);
      case _DataReceived() when dataReceived != null:
        return dataReceived(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_DataReceived value) dataReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted():
        return watchAllStarted(_that);
      case _DataReceived():
        return dataReceived(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_DataReceived value)? dataReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted(_that);
      case _DataReceived() when dataReceived != null:
        return dataReceived(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchAllStarted,
    TResult Function(Either<RemoteFailure, DeviceModel> failureOrDeviceModel)?
        dataReceived,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted();
      case _DataReceived() when dataReceived != null:
        return dataReceived(_that.failureOrDeviceModel);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchAllStarted,
    required TResult Function(
            Either<RemoteFailure, DeviceModel> failureOrDeviceModel)
        dataReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted():
        return watchAllStarted();
      case _DataReceived():
        return dataReceived(_that.failureOrDeviceModel);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchAllStarted,
    TResult? Function(Either<RemoteFailure, DeviceModel> failureOrDeviceModel)?
        dataReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted();
      case _DataReceived() when dataReceived != null:
        return dataReceived(_that.failureOrDeviceModel);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _WatchAllStarted implements DeviceStatusWatcherEvent {
  const _WatchAllStarted();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _WatchAllStarted);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceStatusWatcherEvent.watchAllStarted()';
  }
}

/// @nodoc

class _DataReceived implements DeviceStatusWatcherEvent {
  const _DataReceived(this.failureOrDeviceModel);

  final Either<RemoteFailure, DeviceModel> failureOrDeviceModel;

  /// Create a copy of DeviceStatusWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DataReceivedCopyWith<_DataReceived> get copyWith =>
      __$DataReceivedCopyWithImpl<_DataReceived>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DataReceived &&
            (identical(other.failureOrDeviceModel, failureOrDeviceModel) ||
                other.failureOrDeviceModel == failureOrDeviceModel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrDeviceModel);

  @override
  String toString() {
    return 'DeviceStatusWatcherEvent.dataReceived(failureOrDeviceModel: $failureOrDeviceModel)';
  }
}

/// @nodoc
abstract mixin class _$DataReceivedCopyWith<$Res>
    implements $DeviceStatusWatcherEventCopyWith<$Res> {
  factory _$DataReceivedCopyWith(
          _DataReceived value, $Res Function(_DataReceived) _then) =
      __$DataReceivedCopyWithImpl;
  @useResult
  $Res call({Either<RemoteFailure, DeviceModel> failureOrDeviceModel});
}

/// @nodoc
class __$DataReceivedCopyWithImpl<$Res>
    implements _$DataReceivedCopyWith<$Res> {
  __$DataReceivedCopyWithImpl(this._self, this._then);

  final _DataReceived _self;
  final $Res Function(_DataReceived) _then;

  /// Create a copy of DeviceStatusWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureOrDeviceModel = null,
  }) {
    return _then(_DataReceived(
      null == failureOrDeviceModel
          ? _self.failureOrDeviceModel
          : failureOrDeviceModel // ignore: cast_nullable_to_non_nullable
              as Either<RemoteFailure, DeviceModel>,
    ));
  }
}

/// @nodoc
mixin _$DeviceStatusWatcherState {
  DeviceInfoModel get deviceInformation;
  bool get isDeviceConnected;
  bool get isDeviceOn;
  bool get isDeviceReady;
  bool get isDeviceError;
  bool get isDeviceBusy;
  bool get isDeviceOff;
  bool get isDeviceDisconnected;
  Option<Either<RemoteFailure, Unit>> get failureOrSuccessOption;

  /// Create a copy of DeviceStatusWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DeviceStatusWatcherStateCopyWith<DeviceStatusWatcherState> get copyWith =>
      _$DeviceStatusWatcherStateCopyWithImpl<DeviceStatusWatcherState>(
          this as DeviceStatusWatcherState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DeviceStatusWatcherState &&
            (identical(other.deviceInformation, deviceInformation) ||
                other.deviceInformation == deviceInformation) &&
            (identical(other.isDeviceConnected, isDeviceConnected) ||
                other.isDeviceConnected == isDeviceConnected) &&
            (identical(other.isDeviceOn, isDeviceOn) ||
                other.isDeviceOn == isDeviceOn) &&
            (identical(other.isDeviceReady, isDeviceReady) ||
                other.isDeviceReady == isDeviceReady) &&
            (identical(other.isDeviceError, isDeviceError) ||
                other.isDeviceError == isDeviceError) &&
            (identical(other.isDeviceBusy, isDeviceBusy) ||
                other.isDeviceBusy == isDeviceBusy) &&
            (identical(other.isDeviceOff, isDeviceOff) ||
                other.isDeviceOff == isDeviceOff) &&
            (identical(other.isDeviceDisconnected, isDeviceDisconnected) ||
                other.isDeviceDisconnected == isDeviceDisconnected) &&
            (identical(other.failureOrSuccessOption, failureOrSuccessOption) ||
                other.failureOrSuccessOption == failureOrSuccessOption));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      deviceInformation,
      isDeviceConnected,
      isDeviceOn,
      isDeviceReady,
      isDeviceError,
      isDeviceBusy,
      isDeviceOff,
      isDeviceDisconnected,
      failureOrSuccessOption);

  @override
  String toString() {
    return 'DeviceStatusWatcherState(deviceInformation: $deviceInformation, isDeviceConnected: $isDeviceConnected, isDeviceOn: $isDeviceOn, isDeviceReady: $isDeviceReady, isDeviceError: $isDeviceError, isDeviceBusy: $isDeviceBusy, isDeviceOff: $isDeviceOff, isDeviceDisconnected: $isDeviceDisconnected, failureOrSuccessOption: $failureOrSuccessOption)';
  }
}

/// @nodoc
abstract mixin class $DeviceStatusWatcherStateCopyWith<$Res> {
  factory $DeviceStatusWatcherStateCopyWith(DeviceStatusWatcherState value,
          $Res Function(DeviceStatusWatcherState) _then) =
      _$DeviceStatusWatcherStateCopyWithImpl;
  @useResult
  $Res call(
      {DeviceInfoModel deviceInformation,
      bool isDeviceConnected,
      bool isDeviceOn,
      bool isDeviceReady,
      bool isDeviceError,
      bool isDeviceBusy,
      bool isDeviceOff,
      bool isDeviceDisconnected,
      Option<Either<RemoteFailure, Unit>> failureOrSuccessOption});
}

/// @nodoc
class _$DeviceStatusWatcherStateCopyWithImpl<$Res>
    implements $DeviceStatusWatcherStateCopyWith<$Res> {
  _$DeviceStatusWatcherStateCopyWithImpl(this._self, this._then);

  final DeviceStatusWatcherState _self;
  final $Res Function(DeviceStatusWatcherState) _then;

  /// Create a copy of DeviceStatusWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? deviceInformation = null,
    Object? isDeviceConnected = null,
    Object? isDeviceOn = null,
    Object? isDeviceReady = null,
    Object? isDeviceError = null,
    Object? isDeviceBusy = null,
    Object? isDeviceOff = null,
    Object? isDeviceDisconnected = null,
    Object? failureOrSuccessOption = null,
  }) {
    return _then(_self.copyWith(
      deviceInformation: null == deviceInformation
          ? _self.deviceInformation
          : deviceInformation // ignore: cast_nullable_to_non_nullable
              as DeviceInfoModel,
      isDeviceConnected: null == isDeviceConnected
          ? _self.isDeviceConnected
          : isDeviceConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceOn: null == isDeviceOn
          ? _self.isDeviceOn
          : isDeviceOn // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceReady: null == isDeviceReady
          ? _self.isDeviceReady
          : isDeviceReady // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceError: null == isDeviceError
          ? _self.isDeviceError
          : isDeviceError // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceBusy: null == isDeviceBusy
          ? _self.isDeviceBusy
          : isDeviceBusy // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceOff: null == isDeviceOff
          ? _self.isDeviceOff
          : isDeviceOff // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceDisconnected: null == isDeviceDisconnected
          ? _self.isDeviceDisconnected
          : isDeviceDisconnected // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessOption: null == failureOrSuccessOption
          ? _self.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<RemoteFailure, Unit>>,
    ));
  }
}

/// Adds pattern-matching-related methods to [DeviceStatusWatcherState].
extension DeviceStatusWatcherStatePatterns on DeviceStatusWatcherState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DeviceStatusWatcherState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DeviceStatusWatcherState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DeviceStatusWatcherState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceStatusWatcherState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DeviceStatusWatcherState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceStatusWatcherState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            DeviceInfoModel deviceInformation,
            bool isDeviceConnected,
            bool isDeviceOn,
            bool isDeviceReady,
            bool isDeviceError,
            bool isDeviceBusy,
            bool isDeviceOff,
            bool isDeviceDisconnected,
            Option<Either<RemoteFailure, Unit>> failureOrSuccessOption)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DeviceStatusWatcherState() when $default != null:
        return $default(
            _that.deviceInformation,
            _that.isDeviceConnected,
            _that.isDeviceOn,
            _that.isDeviceReady,
            _that.isDeviceError,
            _that.isDeviceBusy,
            _that.isDeviceOff,
            _that.isDeviceDisconnected,
            _that.failureOrSuccessOption);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            DeviceInfoModel deviceInformation,
            bool isDeviceConnected,
            bool isDeviceOn,
            bool isDeviceReady,
            bool isDeviceError,
            bool isDeviceBusy,
            bool isDeviceOff,
            bool isDeviceDisconnected,
            Option<Either<RemoteFailure, Unit>> failureOrSuccessOption)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceStatusWatcherState():
        return $default(
            _that.deviceInformation,
            _that.isDeviceConnected,
            _that.isDeviceOn,
            _that.isDeviceReady,
            _that.isDeviceError,
            _that.isDeviceBusy,
            _that.isDeviceOff,
            _that.isDeviceDisconnected,
            _that.failureOrSuccessOption);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            DeviceInfoModel deviceInformation,
            bool isDeviceConnected,
            bool isDeviceOn,
            bool isDeviceReady,
            bool isDeviceError,
            bool isDeviceBusy,
            bool isDeviceOff,
            bool isDeviceDisconnected,
            Option<Either<RemoteFailure, Unit>> failureOrSuccessOption)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DeviceStatusWatcherState() when $default != null:
        return $default(
            _that.deviceInformation,
            _that.isDeviceConnected,
            _that.isDeviceOn,
            _that.isDeviceReady,
            _that.isDeviceError,
            _that.isDeviceBusy,
            _that.isDeviceOff,
            _that.isDeviceDisconnected,
            _that.failureOrSuccessOption);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _DeviceStatusWatcherState implements DeviceStatusWatcherState {
  const _DeviceStatusWatcherState(
      {required this.deviceInformation,
      required this.isDeviceConnected,
      required this.isDeviceOn,
      required this.isDeviceReady,
      required this.isDeviceError,
      required this.isDeviceBusy,
      required this.isDeviceOff,
      required this.isDeviceDisconnected,
      required this.failureOrSuccessOption});

  @override
  final DeviceInfoModel deviceInformation;
  @override
  final bool isDeviceConnected;
  @override
  final bool isDeviceOn;
  @override
  final bool isDeviceReady;
  @override
  final bool isDeviceError;
  @override
  final bool isDeviceBusy;
  @override
  final bool isDeviceOff;
  @override
  final bool isDeviceDisconnected;
  @override
  final Option<Either<RemoteFailure, Unit>> failureOrSuccessOption;

  /// Create a copy of DeviceStatusWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DeviceStatusWatcherStateCopyWith<_DeviceStatusWatcherState> get copyWith =>
      __$DeviceStatusWatcherStateCopyWithImpl<_DeviceStatusWatcherState>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DeviceStatusWatcherState &&
            (identical(other.deviceInformation, deviceInformation) ||
                other.deviceInformation == deviceInformation) &&
            (identical(other.isDeviceConnected, isDeviceConnected) ||
                other.isDeviceConnected == isDeviceConnected) &&
            (identical(other.isDeviceOn, isDeviceOn) ||
                other.isDeviceOn == isDeviceOn) &&
            (identical(other.isDeviceReady, isDeviceReady) ||
                other.isDeviceReady == isDeviceReady) &&
            (identical(other.isDeviceError, isDeviceError) ||
                other.isDeviceError == isDeviceError) &&
            (identical(other.isDeviceBusy, isDeviceBusy) ||
                other.isDeviceBusy == isDeviceBusy) &&
            (identical(other.isDeviceOff, isDeviceOff) ||
                other.isDeviceOff == isDeviceOff) &&
            (identical(other.isDeviceDisconnected, isDeviceDisconnected) ||
                other.isDeviceDisconnected == isDeviceDisconnected) &&
            (identical(other.failureOrSuccessOption, failureOrSuccessOption) ||
                other.failureOrSuccessOption == failureOrSuccessOption));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      deviceInformation,
      isDeviceConnected,
      isDeviceOn,
      isDeviceReady,
      isDeviceError,
      isDeviceBusy,
      isDeviceOff,
      isDeviceDisconnected,
      failureOrSuccessOption);

  @override
  String toString() {
    return 'DeviceStatusWatcherState(deviceInformation: $deviceInformation, isDeviceConnected: $isDeviceConnected, isDeviceOn: $isDeviceOn, isDeviceReady: $isDeviceReady, isDeviceError: $isDeviceError, isDeviceBusy: $isDeviceBusy, isDeviceOff: $isDeviceOff, isDeviceDisconnected: $isDeviceDisconnected, failureOrSuccessOption: $failureOrSuccessOption)';
  }
}

/// @nodoc
abstract mixin class _$DeviceStatusWatcherStateCopyWith<$Res>
    implements $DeviceStatusWatcherStateCopyWith<$Res> {
  factory _$DeviceStatusWatcherStateCopyWith(_DeviceStatusWatcherState value,
          $Res Function(_DeviceStatusWatcherState) _then) =
      __$DeviceStatusWatcherStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {DeviceInfoModel deviceInformation,
      bool isDeviceConnected,
      bool isDeviceOn,
      bool isDeviceReady,
      bool isDeviceError,
      bool isDeviceBusy,
      bool isDeviceOff,
      bool isDeviceDisconnected,
      Option<Either<RemoteFailure, Unit>> failureOrSuccessOption});
}

/// @nodoc
class __$DeviceStatusWatcherStateCopyWithImpl<$Res>
    implements _$DeviceStatusWatcherStateCopyWith<$Res> {
  __$DeviceStatusWatcherStateCopyWithImpl(this._self, this._then);

  final _DeviceStatusWatcherState _self;
  final $Res Function(_DeviceStatusWatcherState) _then;

  /// Create a copy of DeviceStatusWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? deviceInformation = null,
    Object? isDeviceConnected = null,
    Object? isDeviceOn = null,
    Object? isDeviceReady = null,
    Object? isDeviceError = null,
    Object? isDeviceBusy = null,
    Object? isDeviceOff = null,
    Object? isDeviceDisconnected = null,
    Object? failureOrSuccessOption = null,
  }) {
    return _then(_DeviceStatusWatcherState(
      deviceInformation: null == deviceInformation
          ? _self.deviceInformation
          : deviceInformation // ignore: cast_nullable_to_non_nullable
              as DeviceInfoModel,
      isDeviceConnected: null == isDeviceConnected
          ? _self.isDeviceConnected
          : isDeviceConnected // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceOn: null == isDeviceOn
          ? _self.isDeviceOn
          : isDeviceOn // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceReady: null == isDeviceReady
          ? _self.isDeviceReady
          : isDeviceReady // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceError: null == isDeviceError
          ? _self.isDeviceError
          : isDeviceError // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceBusy: null == isDeviceBusy
          ? _self.isDeviceBusy
          : isDeviceBusy // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceOff: null == isDeviceOff
          ? _self.isDeviceOff
          : isDeviceOff // ignore: cast_nullable_to_non_nullable
              as bool,
      isDeviceDisconnected: null == isDeviceDisconnected
          ? _self.isDeviceDisconnected
          : isDeviceDisconnected // ignore: cast_nullable_to_non_nullable
              as bool,
      failureOrSuccessOption: null == failureOrSuccessOption
          ? _self.failureOrSuccessOption
          : failureOrSuccessOption // ignore: cast_nullable_to_non_nullable
              as Option<Either<RemoteFailure, Unit>>,
    ));
  }
}

// dart format on
