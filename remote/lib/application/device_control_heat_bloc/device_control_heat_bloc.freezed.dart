// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_control_heat_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DeviceControlHeatEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeviceControlHeatEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlHeatEvent()';
  }
}

/// @nodoc
class $DeviceControlHeatEventCopyWith<$Res> {
  $DeviceControlHeatEventCopyWith(
      DeviceControlHeatEvent _, $Res Function(DeviceControlHeatEvent) __);
}

/// Adds pattern-matching-related methods to [DeviceControlHeatEvent].
extension DeviceControlHeatEventPatterns on DeviceControlHeatEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(IncreaseHeat value)? increaseHeat,
    TResult Function(DecreaseHeat value)? decreaseHeat,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case IncreaseHeat() when increaseHeat != null:
        return increaseHeat(_that);
      case DecreaseHeat() when decreaseHeat != null:
        return decreaseHeat(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(IncreaseHeat value) increaseHeat,
    required TResult Function(DecreaseHeat value) decreaseHeat,
  }) {
    final _that = this;
    switch (_that) {
      case IncreaseHeat():
        return increaseHeat(_that);
      case DecreaseHeat():
        return decreaseHeat(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(IncreaseHeat value)? increaseHeat,
    TResult? Function(DecreaseHeat value)? decreaseHeat,
  }) {
    final _that = this;
    switch (_that) {
      case IncreaseHeat() when increaseHeat != null:
        return increaseHeat(_that);
      case DecreaseHeat() when decreaseHeat != null:
        return decreaseHeat(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? increaseHeat,
    TResult Function()? decreaseHeat,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case IncreaseHeat() when increaseHeat != null:
        return increaseHeat();
      case DecreaseHeat() when decreaseHeat != null:
        return decreaseHeat();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() increaseHeat,
    required TResult Function() decreaseHeat,
  }) {
    final _that = this;
    switch (_that) {
      case IncreaseHeat():
        return increaseHeat();
      case DecreaseHeat():
        return decreaseHeat();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? increaseHeat,
    TResult? Function()? decreaseHeat,
  }) {
    final _that = this;
    switch (_that) {
      case IncreaseHeat() when increaseHeat != null:
        return increaseHeat();
      case DecreaseHeat() when decreaseHeat != null:
        return decreaseHeat();
      case _:
        return null;
    }
  }
}

/// @nodoc

class IncreaseHeat implements DeviceControlHeatEvent {
  const IncreaseHeat();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is IncreaseHeat);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlHeatEvent.increaseHeat()';
  }
}

/// @nodoc

class DecreaseHeat implements DeviceControlHeatEvent {
  const DecreaseHeat();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DecreaseHeat);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlHeatEvent.decreaseHeat()';
  }
}

/// @nodoc
mixin _$DeviceControlHeatState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeviceControlHeatState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlHeatState()';
  }
}

/// @nodoc
class $DeviceControlHeatStateCopyWith<$Res> {
  $DeviceControlHeatStateCopyWith(
      DeviceControlHeatState _, $Res Function(DeviceControlHeatState) __);
}

/// Adds pattern-matching-related methods to [DeviceControlHeatState].
extension DeviceControlHeatStatePatterns on DeviceControlHeatState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ChangeHeatLevelSuccess value)? changeHeatLevelSuccess,
    TResult Function(ChangeHeatLevelFailure value)? changeHeatLevelFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial(_that);
      case ChangeHeatLevelSuccess() when changeHeatLevelSuccess != null:
        return changeHeatLevelSuccess(_that);
      case ChangeHeatLevelFailure() when changeHeatLevelFailure != null:
        return changeHeatLevelFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ChangeHeatLevelSuccess value)
        changeHeatLevelSuccess,
    required TResult Function(ChangeHeatLevelFailure value)
        changeHeatLevelFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial():
        return initial(_that);
      case ChangeHeatLevelSuccess():
        return changeHeatLevelSuccess(_that);
      case ChangeHeatLevelFailure():
        return changeHeatLevelFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ChangeHeatLevelSuccess value)? changeHeatLevelSuccess,
    TResult? Function(ChangeHeatLevelFailure value)? changeHeatLevelFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial(_that);
      case ChangeHeatLevelSuccess() when changeHeatLevelSuccess != null:
        return changeHeatLevelSuccess(_that);
      case ChangeHeatLevelFailure() when changeHeatLevelFailure != null:
        return changeHeatLevelFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? changeHeatLevelSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeHeatLevelFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial();
      case ChangeHeatLevelSuccess() when changeHeatLevelSuccess != null:
        return changeHeatLevelSuccess();
      case ChangeHeatLevelFailure() when changeHeatLevelFailure != null:
        return changeHeatLevelFailure(_that.remoteFailure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() changeHeatLevelSuccess,
    required TResult Function(RemoteFailure remoteFailure)
        changeHeatLevelFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial():
        return initial();
      case ChangeHeatLevelSuccess():
        return changeHeatLevelSuccess();
      case ChangeHeatLevelFailure():
        return changeHeatLevelFailure(_that.remoteFailure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? changeHeatLevelSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeHeatLevelFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial();
      case ChangeHeatLevelSuccess() when changeHeatLevelSuccess != null:
        return changeHeatLevelSuccess();
      case ChangeHeatLevelFailure() when changeHeatLevelFailure != null:
        return changeHeatLevelFailure(_that.remoteFailure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class Initial implements DeviceControlHeatState {
  const Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlHeatState.initial()';
  }
}

/// @nodoc

class ChangeHeatLevelSuccess implements DeviceControlHeatState {
  const ChangeHeatLevelSuccess();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ChangeHeatLevelSuccess);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlHeatState.changeHeatLevelSuccess()';
  }
}

/// @nodoc

class ChangeHeatLevelFailure implements DeviceControlHeatState {
  const ChangeHeatLevelFailure(this.remoteFailure);

  final RemoteFailure remoteFailure;

  /// Create a copy of DeviceControlHeatState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ChangeHeatLevelFailureCopyWith<ChangeHeatLevelFailure> get copyWith =>
      _$ChangeHeatLevelFailureCopyWithImpl<ChangeHeatLevelFailure>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ChangeHeatLevelFailure &&
            (identical(other.remoteFailure, remoteFailure) ||
                other.remoteFailure == remoteFailure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, remoteFailure);

  @override
  String toString() {
    return 'DeviceControlHeatState.changeHeatLevelFailure(remoteFailure: $remoteFailure)';
  }
}

/// @nodoc
abstract mixin class $ChangeHeatLevelFailureCopyWith<$Res>
    implements $DeviceControlHeatStateCopyWith<$Res> {
  factory $ChangeHeatLevelFailureCopyWith(ChangeHeatLevelFailure value,
          $Res Function(ChangeHeatLevelFailure) _then) =
      _$ChangeHeatLevelFailureCopyWithImpl;
  @useResult
  $Res call({RemoteFailure remoteFailure});

  $RemoteFailureCopyWith<$Res> get remoteFailure;
}

/// @nodoc
class _$ChangeHeatLevelFailureCopyWithImpl<$Res>
    implements $ChangeHeatLevelFailureCopyWith<$Res> {
  _$ChangeHeatLevelFailureCopyWithImpl(this._self, this._then);

  final ChangeHeatLevelFailure _self;
  final $Res Function(ChangeHeatLevelFailure) _then;

  /// Create a copy of DeviceControlHeatState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? remoteFailure = null,
  }) {
    return _then(ChangeHeatLevelFailure(
      null == remoteFailure
          ? _self.remoteFailure
          : remoteFailure // ignore: cast_nullable_to_non_nullable
              as RemoteFailure,
    ));
  }

  /// Create a copy of DeviceControlHeatState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RemoteFailureCopyWith<$Res> get remoteFailure {
    return $RemoteFailureCopyWith<$Res>(_self.remoteFailure, (value) {
      return _then(_self.copyWith(remoteFailure: value));
    });
  }
}

// dart format on
