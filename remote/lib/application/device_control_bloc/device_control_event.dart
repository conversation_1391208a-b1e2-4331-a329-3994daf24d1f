part of 'device_control_bloc.dart';

@freezed
class DeviceControlEvent with _$DeviceControlEvent {
  const factory DeviceControlEvent.powerOff() = PowerOff;
  const factory DeviceControlEvent.toggleTherapy() = ToggleTherapy;
  const factory DeviceControlEvent.unpairDevice() = UnpairDeviceEvent;
  const factory DeviceControlEvent.actionFailureEvent(RemoteFailure failure) =
      ActionFailureEvent;
  const factory DeviceControlEvent.therapyStateChangedEvent(bool isActive) =
      TherapyStateChangedEvent;
}
