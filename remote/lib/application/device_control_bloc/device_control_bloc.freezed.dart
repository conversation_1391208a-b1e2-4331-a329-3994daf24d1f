// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_control_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DeviceControlEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeviceControlEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlEvent()';
  }
}

/// @nodoc
class $DeviceControlEventCopyWith<$Res> {
  $DeviceControlEventCopyWith(
      DeviceControlEvent _, $Res Function(DeviceControlEvent) __);
}

/// Adds pattern-matching-related methods to [DeviceControlEvent].
extension DeviceControlEventPatterns on DeviceControlEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PowerOff value)? powerOff,
    TResult Function(ToggleTherapy value)? toggleTherapy,
    TResult Function(UnpairDeviceEvent value)? unpairDevice,
    TResult Function(ActionFailureEvent value)? actionFailureEvent,
    TResult Function(TherapyStateChangedEvent value)? therapyStateChangedEvent,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case PowerOff() when powerOff != null:
        return powerOff(_that);
      case ToggleTherapy() when toggleTherapy != null:
        return toggleTherapy(_that);
      case UnpairDeviceEvent() when unpairDevice != null:
        return unpairDevice(_that);
      case ActionFailureEvent() when actionFailureEvent != null:
        return actionFailureEvent(_that);
      case TherapyStateChangedEvent() when therapyStateChangedEvent != null:
        return therapyStateChangedEvent(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PowerOff value) powerOff,
    required TResult Function(ToggleTherapy value) toggleTherapy,
    required TResult Function(UnpairDeviceEvent value) unpairDevice,
    required TResult Function(ActionFailureEvent value) actionFailureEvent,
    required TResult Function(TherapyStateChangedEvent value)
        therapyStateChangedEvent,
  }) {
    final _that = this;
    switch (_that) {
      case PowerOff():
        return powerOff(_that);
      case ToggleTherapy():
        return toggleTherapy(_that);
      case UnpairDeviceEvent():
        return unpairDevice(_that);
      case ActionFailureEvent():
        return actionFailureEvent(_that);
      case TherapyStateChangedEvent():
        return therapyStateChangedEvent(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PowerOff value)? powerOff,
    TResult? Function(ToggleTherapy value)? toggleTherapy,
    TResult? Function(UnpairDeviceEvent value)? unpairDevice,
    TResult? Function(ActionFailureEvent value)? actionFailureEvent,
    TResult? Function(TherapyStateChangedEvent value)? therapyStateChangedEvent,
  }) {
    final _that = this;
    switch (_that) {
      case PowerOff() when powerOff != null:
        return powerOff(_that);
      case ToggleTherapy() when toggleTherapy != null:
        return toggleTherapy(_that);
      case UnpairDeviceEvent() when unpairDevice != null:
        return unpairDevice(_that);
      case ActionFailureEvent() when actionFailureEvent != null:
        return actionFailureEvent(_that);
      case TherapyStateChangedEvent() when therapyStateChangedEvent != null:
        return therapyStateChangedEvent(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? powerOff,
    TResult Function()? toggleTherapy,
    TResult Function()? unpairDevice,
    TResult Function(RemoteFailure failure)? actionFailureEvent,
    TResult Function(bool isActive)? therapyStateChangedEvent,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case PowerOff() when powerOff != null:
        return powerOff();
      case ToggleTherapy() when toggleTherapy != null:
        return toggleTherapy();
      case UnpairDeviceEvent() when unpairDevice != null:
        return unpairDevice();
      case ActionFailureEvent() when actionFailureEvent != null:
        return actionFailureEvent(_that.failure);
      case TherapyStateChangedEvent() when therapyStateChangedEvent != null:
        return therapyStateChangedEvent(_that.isActive);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() powerOff,
    required TResult Function() toggleTherapy,
    required TResult Function() unpairDevice,
    required TResult Function(RemoteFailure failure) actionFailureEvent,
    required TResult Function(bool isActive) therapyStateChangedEvent,
  }) {
    final _that = this;
    switch (_that) {
      case PowerOff():
        return powerOff();
      case ToggleTherapy():
        return toggleTherapy();
      case UnpairDeviceEvent():
        return unpairDevice();
      case ActionFailureEvent():
        return actionFailureEvent(_that.failure);
      case TherapyStateChangedEvent():
        return therapyStateChangedEvent(_that.isActive);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? powerOff,
    TResult? Function()? toggleTherapy,
    TResult? Function()? unpairDevice,
    TResult? Function(RemoteFailure failure)? actionFailureEvent,
    TResult? Function(bool isActive)? therapyStateChangedEvent,
  }) {
    final _that = this;
    switch (_that) {
      case PowerOff() when powerOff != null:
        return powerOff();
      case ToggleTherapy() when toggleTherapy != null:
        return toggleTherapy();
      case UnpairDeviceEvent() when unpairDevice != null:
        return unpairDevice();
      case ActionFailureEvent() when actionFailureEvent != null:
        return actionFailureEvent(_that.failure);
      case TherapyStateChangedEvent() when therapyStateChangedEvent != null:
        return therapyStateChangedEvent(_that.isActive);
      case _:
        return null;
    }
  }
}

/// @nodoc

class PowerOff implements DeviceControlEvent {
  const PowerOff();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is PowerOff);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlEvent.powerOff()';
  }
}

/// @nodoc

class ToggleTherapy implements DeviceControlEvent {
  const ToggleTherapy();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ToggleTherapy);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlEvent.toggleTherapy()';
  }
}

/// @nodoc

class UnpairDeviceEvent implements DeviceControlEvent {
  const UnpairDeviceEvent();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is UnpairDeviceEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlEvent.unpairDevice()';
  }
}

/// @nodoc

class ActionFailureEvent implements DeviceControlEvent {
  const ActionFailureEvent(this.failure);

  final RemoteFailure failure;

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ActionFailureEventCopyWith<ActionFailureEvent> get copyWith =>
      _$ActionFailureEventCopyWithImpl<ActionFailureEvent>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ActionFailureEvent &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'DeviceControlEvent.actionFailureEvent(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class $ActionFailureEventCopyWith<$Res>
    implements $DeviceControlEventCopyWith<$Res> {
  factory $ActionFailureEventCopyWith(
          ActionFailureEvent value, $Res Function(ActionFailureEvent) _then) =
      _$ActionFailureEventCopyWithImpl;
  @useResult
  $Res call({RemoteFailure failure});

  $RemoteFailureCopyWith<$Res> get failure;
}

/// @nodoc
class _$ActionFailureEventCopyWithImpl<$Res>
    implements $ActionFailureEventCopyWith<$Res> {
  _$ActionFailureEventCopyWithImpl(this._self, this._then);

  final ActionFailureEvent _self;
  final $Res Function(ActionFailureEvent) _then;

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(ActionFailureEvent(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as RemoteFailure,
    ));
  }

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RemoteFailureCopyWith<$Res> get failure {
    return $RemoteFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

/// @nodoc

class TherapyStateChangedEvent implements DeviceControlEvent {
  const TherapyStateChangedEvent(this.isActive);

  final bool isActive;

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TherapyStateChangedEventCopyWith<TherapyStateChangedEvent> get copyWith =>
      _$TherapyStateChangedEventCopyWithImpl<TherapyStateChangedEvent>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TherapyStateChangedEvent &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isActive);

  @override
  String toString() {
    return 'DeviceControlEvent.therapyStateChangedEvent(isActive: $isActive)';
  }
}

/// @nodoc
abstract mixin class $TherapyStateChangedEventCopyWith<$Res>
    implements $DeviceControlEventCopyWith<$Res> {
  factory $TherapyStateChangedEventCopyWith(TherapyStateChangedEvent value,
          $Res Function(TherapyStateChangedEvent) _then) =
      _$TherapyStateChangedEventCopyWithImpl;
  @useResult
  $Res call({bool isActive});
}

/// @nodoc
class _$TherapyStateChangedEventCopyWithImpl<$Res>
    implements $TherapyStateChangedEventCopyWith<$Res> {
  _$TherapyStateChangedEventCopyWithImpl(this._self, this._then);

  final TherapyStateChangedEvent _self;
  final $Res Function(TherapyStateChangedEvent) _then;

  /// Create a copy of DeviceControlEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isActive = null,
  }) {
    return _then(TherapyStateChangedEvent(
      null == isActive
          ? _self.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
mixin _$DeviceControlState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeviceControlState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlState()';
  }
}

/// @nodoc
class $DeviceControlStateCopyWith<$Res> {
  $DeviceControlStateCopyWith(
      DeviceControlState _, $Res Function(DeviceControlState) __);
}

/// Adds pattern-matching-related methods to [DeviceControlState].
extension DeviceControlStatePatterns on DeviceControlState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ActionSuccess value)? actionSuccess,
    TResult Function(ActionFailure value)? actionFailure,
    TResult Function(TherapyStateChanged value)? therapyStateChanged,
    TResult Function(UnpairingDevice value)? unpairingDevice,
    TResult Function(DeviceUnpaired value)? deviceUnpaired,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial(_that);
      case ActionSuccess() when actionSuccess != null:
        return actionSuccess(_that);
      case ActionFailure() when actionFailure != null:
        return actionFailure(_that);
      case TherapyStateChanged() when therapyStateChanged != null:
        return therapyStateChanged(_that);
      case UnpairingDevice() when unpairingDevice != null:
        return unpairingDevice(_that);
      case DeviceUnpaired() when deviceUnpaired != null:
        return deviceUnpaired(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ActionSuccess value) actionSuccess,
    required TResult Function(ActionFailure value) actionFailure,
    required TResult Function(TherapyStateChanged value) therapyStateChanged,
    required TResult Function(UnpairingDevice value) unpairingDevice,
    required TResult Function(DeviceUnpaired value) deviceUnpaired,
  }) {
    final _that = this;
    switch (_that) {
      case Initial():
        return initial(_that);
      case ActionSuccess():
        return actionSuccess(_that);
      case ActionFailure():
        return actionFailure(_that);
      case TherapyStateChanged():
        return therapyStateChanged(_that);
      case UnpairingDevice():
        return unpairingDevice(_that);
      case DeviceUnpaired():
        return deviceUnpaired(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ActionSuccess value)? actionSuccess,
    TResult? Function(ActionFailure value)? actionFailure,
    TResult? Function(TherapyStateChanged value)? therapyStateChanged,
    TResult? Function(UnpairingDevice value)? unpairingDevice,
    TResult? Function(DeviceUnpaired value)? deviceUnpaired,
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial(_that);
      case ActionSuccess() when actionSuccess != null:
        return actionSuccess(_that);
      case ActionFailure() when actionFailure != null:
        return actionFailure(_that);
      case TherapyStateChanged() when therapyStateChanged != null:
        return therapyStateChanged(_that);
      case UnpairingDevice() when unpairingDevice != null:
        return unpairingDevice(_that);
      case DeviceUnpaired() when deviceUnpaired != null:
        return deviceUnpaired(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? actionSuccess,
    TResult Function(RemoteFailure remoteFailure)? actionFailure,
    TResult Function(bool isActive)? therapyStateChanged,
    TResult Function()? unpairingDevice,
    TResult Function()? deviceUnpaired,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial();
      case ActionSuccess() when actionSuccess != null:
        return actionSuccess();
      case ActionFailure() when actionFailure != null:
        return actionFailure(_that.remoteFailure);
      case TherapyStateChanged() when therapyStateChanged != null:
        return therapyStateChanged(_that.isActive);
      case UnpairingDevice() when unpairingDevice != null:
        return unpairingDevice();
      case DeviceUnpaired() when deviceUnpaired != null:
        return deviceUnpaired();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() actionSuccess,
    required TResult Function(RemoteFailure remoteFailure) actionFailure,
    required TResult Function(bool isActive) therapyStateChanged,
    required TResult Function() unpairingDevice,
    required TResult Function() deviceUnpaired,
  }) {
    final _that = this;
    switch (_that) {
      case Initial():
        return initial();
      case ActionSuccess():
        return actionSuccess();
      case ActionFailure():
        return actionFailure(_that.remoteFailure);
      case TherapyStateChanged():
        return therapyStateChanged(_that.isActive);
      case UnpairingDevice():
        return unpairingDevice();
      case DeviceUnpaired():
        return deviceUnpaired();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? actionSuccess,
    TResult? Function(RemoteFailure remoteFailure)? actionFailure,
    TResult? Function(bool isActive)? therapyStateChanged,
    TResult? Function()? unpairingDevice,
    TResult? Function()? deviceUnpaired,
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial();
      case ActionSuccess() when actionSuccess != null:
        return actionSuccess();
      case ActionFailure() when actionFailure != null:
        return actionFailure(_that.remoteFailure);
      case TherapyStateChanged() when therapyStateChanged != null:
        return therapyStateChanged(_that.isActive);
      case UnpairingDevice() when unpairingDevice != null:
        return unpairingDevice();
      case DeviceUnpaired() when deviceUnpaired != null:
        return deviceUnpaired();
      case _:
        return null;
    }
  }
}

/// @nodoc

class Initial implements DeviceControlState {
  const Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlState.initial()';
  }
}

/// @nodoc

class ActionSuccess implements DeviceControlState {
  const ActionSuccess();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ActionSuccess);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlState.actionSuccess()';
  }
}

/// @nodoc

class ActionFailure implements DeviceControlState {
  const ActionFailure(this.remoteFailure);

  final RemoteFailure remoteFailure;

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ActionFailureCopyWith<ActionFailure> get copyWith =>
      _$ActionFailureCopyWithImpl<ActionFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ActionFailure &&
            (identical(other.remoteFailure, remoteFailure) ||
                other.remoteFailure == remoteFailure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, remoteFailure);

  @override
  String toString() {
    return 'DeviceControlState.actionFailure(remoteFailure: $remoteFailure)';
  }
}

/// @nodoc
abstract mixin class $ActionFailureCopyWith<$Res>
    implements $DeviceControlStateCopyWith<$Res> {
  factory $ActionFailureCopyWith(
          ActionFailure value, $Res Function(ActionFailure) _then) =
      _$ActionFailureCopyWithImpl;
  @useResult
  $Res call({RemoteFailure remoteFailure});

  $RemoteFailureCopyWith<$Res> get remoteFailure;
}

/// @nodoc
class _$ActionFailureCopyWithImpl<$Res>
    implements $ActionFailureCopyWith<$Res> {
  _$ActionFailureCopyWithImpl(this._self, this._then);

  final ActionFailure _self;
  final $Res Function(ActionFailure) _then;

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? remoteFailure = null,
  }) {
    return _then(ActionFailure(
      null == remoteFailure
          ? _self.remoteFailure
          : remoteFailure // ignore: cast_nullable_to_non_nullable
              as RemoteFailure,
    ));
  }

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RemoteFailureCopyWith<$Res> get remoteFailure {
    return $RemoteFailureCopyWith<$Res>(_self.remoteFailure, (value) {
      return _then(_self.copyWith(remoteFailure: value));
    });
  }
}

/// @nodoc

class TherapyStateChanged implements DeviceControlState {
  const TherapyStateChanged(this.isActive);

  final bool isActive;

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TherapyStateChangedCopyWith<TherapyStateChanged> get copyWith =>
      _$TherapyStateChangedCopyWithImpl<TherapyStateChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TherapyStateChanged &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isActive);

  @override
  String toString() {
    return 'DeviceControlState.therapyStateChanged(isActive: $isActive)';
  }
}

/// @nodoc
abstract mixin class $TherapyStateChangedCopyWith<$Res>
    implements $DeviceControlStateCopyWith<$Res> {
  factory $TherapyStateChangedCopyWith(
          TherapyStateChanged value, $Res Function(TherapyStateChanged) _then) =
      _$TherapyStateChangedCopyWithImpl;
  @useResult
  $Res call({bool isActive});
}

/// @nodoc
class _$TherapyStateChangedCopyWithImpl<$Res>
    implements $TherapyStateChangedCopyWith<$Res> {
  _$TherapyStateChangedCopyWithImpl(this._self, this._then);

  final TherapyStateChanged _self;
  final $Res Function(TherapyStateChanged) _then;

  /// Create a copy of DeviceControlState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isActive = null,
  }) {
    return _then(TherapyStateChanged(
      null == isActive
          ? _self.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class UnpairingDevice implements DeviceControlState {
  const UnpairingDevice();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is UnpairingDevice);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlState.unpairingDevice()';
  }
}

/// @nodoc

class DeviceUnpaired implements DeviceControlState {
  const DeviceUnpaired();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeviceUnpaired);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlState.deviceUnpaired()';
  }
}

// dart format on
