// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_control_tens_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DeviceControlTensEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeviceControlTensEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlTensEvent()';
  }
}

/// @nodoc
class $DeviceControlTensEventCopyWith<$Res> {
  $DeviceControlTensEventCopyWith(
      DeviceControlTensEvent _, $Res Function(DeviceControlTensEvent) __);
}

/// Adds pattern-matching-related methods to [DeviceControlTensEvent].
extension DeviceControlTensEventPatterns on DeviceControlTensEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(IncreaseTens value)? increaseTens,
    TResult Function(DecreaseTens value)? decreaseTens,
    TResult Function(ChangeMode value)? changeMode,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case IncreaseTens() when increaseTens != null:
        return increaseTens(_that);
      case DecreaseTens() when decreaseTens != null:
        return decreaseTens(_that);
      case ChangeMode() when changeMode != null:
        return changeMode(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(IncreaseTens value) increaseTens,
    required TResult Function(DecreaseTens value) decreaseTens,
    required TResult Function(ChangeMode value) changeMode,
  }) {
    final _that = this;
    switch (_that) {
      case IncreaseTens():
        return increaseTens(_that);
      case DecreaseTens():
        return decreaseTens(_that);
      case ChangeMode():
        return changeMode(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(IncreaseTens value)? increaseTens,
    TResult? Function(DecreaseTens value)? decreaseTens,
    TResult? Function(ChangeMode value)? changeMode,
  }) {
    final _that = this;
    switch (_that) {
      case IncreaseTens() when increaseTens != null:
        return increaseTens(_that);
      case DecreaseTens() when decreaseTens != null:
        return decreaseTens(_that);
      case ChangeMode() when changeMode != null:
        return changeMode(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? increaseTens,
    TResult Function()? decreaseTens,
    TResult Function(int mode)? changeMode,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case IncreaseTens() when increaseTens != null:
        return increaseTens();
      case DecreaseTens() when decreaseTens != null:
        return decreaseTens();
      case ChangeMode() when changeMode != null:
        return changeMode(_that.mode);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() increaseTens,
    required TResult Function() decreaseTens,
    required TResult Function(int mode) changeMode,
  }) {
    final _that = this;
    switch (_that) {
      case IncreaseTens():
        return increaseTens();
      case DecreaseTens():
        return decreaseTens();
      case ChangeMode():
        return changeMode(_that.mode);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? increaseTens,
    TResult? Function()? decreaseTens,
    TResult? Function(int mode)? changeMode,
  }) {
    final _that = this;
    switch (_that) {
      case IncreaseTens() when increaseTens != null:
        return increaseTens();
      case DecreaseTens() when decreaseTens != null:
        return decreaseTens();
      case ChangeMode() when changeMode != null:
        return changeMode(_that.mode);
      case _:
        return null;
    }
  }
}

/// @nodoc

class IncreaseTens implements DeviceControlTensEvent {
  const IncreaseTens();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is IncreaseTens);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlTensEvent.increaseTens()';
  }
}

/// @nodoc

class DecreaseTens implements DeviceControlTensEvent {
  const DecreaseTens();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DecreaseTens);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlTensEvent.decreaseTens()';
  }
}

/// @nodoc

class ChangeMode implements DeviceControlTensEvent {
  const ChangeMode(this.mode);

  final int mode;

  /// Create a copy of DeviceControlTensEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ChangeModeCopyWith<ChangeMode> get copyWith =>
      _$ChangeModeCopyWithImpl<ChangeMode>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ChangeMode &&
            (identical(other.mode, mode) || other.mode == mode));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mode);

  @override
  String toString() {
    return 'DeviceControlTensEvent.changeMode(mode: $mode)';
  }
}

/// @nodoc
abstract mixin class $ChangeModeCopyWith<$Res>
    implements $DeviceControlTensEventCopyWith<$Res> {
  factory $ChangeModeCopyWith(
          ChangeMode value, $Res Function(ChangeMode) _then) =
      _$ChangeModeCopyWithImpl;
  @useResult
  $Res call({int mode});
}

/// @nodoc
class _$ChangeModeCopyWithImpl<$Res> implements $ChangeModeCopyWith<$Res> {
  _$ChangeModeCopyWithImpl(this._self, this._then);

  final ChangeMode _self;
  final $Res Function(ChangeMode) _then;

  /// Create a copy of DeviceControlTensEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? mode = null,
  }) {
    return _then(ChangeMode(
      null == mode
          ? _self.mode
          : mode // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
mixin _$DeviceControlTensState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeviceControlTensState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlTensState()';
  }
}

/// @nodoc
class $DeviceControlTensStateCopyWith<$Res> {
  $DeviceControlTensStateCopyWith(
      DeviceControlTensState _, $Res Function(DeviceControlTensState) __);
}

/// Adds pattern-matching-related methods to [DeviceControlTensState].
extension DeviceControlTensStatePatterns on DeviceControlTensState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult Function(ChangeModeFailure value)? changeModeFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial(_that);
      case ChangeTensLevelSuccess() when changeTensLevelSuccess != null:
        return changeTensLevelSuccess(_that);
      case ChangeTensLevelFailure() when changeTensLevelFailure != null:
        return changeTensLevelFailure(_that);
      case ChangeModeSuccess() when changeModeSuccess != null:
        return changeModeSuccess(_that);
      case ChangeModeFailure() when changeModeFailure != null:
        return changeModeFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(ChangeTensLevelSuccess value)
        changeTensLevelSuccess,
    required TResult Function(ChangeTensLevelFailure value)
        changeTensLevelFailure,
    required TResult Function(ChangeModeSuccess value) changeModeSuccess,
    required TResult Function(ChangeModeFailure value) changeModeFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial():
        return initial(_that);
      case ChangeTensLevelSuccess():
        return changeTensLevelSuccess(_that);
      case ChangeTensLevelFailure():
        return changeTensLevelFailure(_that);
      case ChangeModeSuccess():
        return changeModeSuccess(_that);
      case ChangeModeFailure():
        return changeModeFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(ChangeTensLevelSuccess value)? changeTensLevelSuccess,
    TResult? Function(ChangeTensLevelFailure value)? changeTensLevelFailure,
    TResult? Function(ChangeModeSuccess value)? changeModeSuccess,
    TResult? Function(ChangeModeFailure value)? changeModeFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial(_that);
      case ChangeTensLevelSuccess() when changeTensLevelSuccess != null:
        return changeTensLevelSuccess(_that);
      case ChangeTensLevelFailure() when changeTensLevelFailure != null:
        return changeTensLevelFailure(_that);
      case ChangeModeSuccess() when changeModeSuccess != null:
        return changeModeSuccess(_that);
      case ChangeModeFailure() when changeModeFailure != null:
        return changeModeFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? changeTensLevelSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult Function()? changeModeSuccess,
    TResult Function(RemoteFailure remoteFailure)? changeModeFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial();
      case ChangeTensLevelSuccess() when changeTensLevelSuccess != null:
        return changeTensLevelSuccess();
      case ChangeTensLevelFailure() when changeTensLevelFailure != null:
        return changeTensLevelFailure(_that.remoteFailure);
      case ChangeModeSuccess() when changeModeSuccess != null:
        return changeModeSuccess();
      case ChangeModeFailure() when changeModeFailure != null:
        return changeModeFailure(_that.remoteFailure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() changeTensLevelSuccess,
    required TResult Function(RemoteFailure remoteFailure)
        changeTensLevelFailure,
    required TResult Function() changeModeSuccess,
    required TResult Function(RemoteFailure remoteFailure) changeModeFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial():
        return initial();
      case ChangeTensLevelSuccess():
        return changeTensLevelSuccess();
      case ChangeTensLevelFailure():
        return changeTensLevelFailure(_that.remoteFailure);
      case ChangeModeSuccess():
        return changeModeSuccess();
      case ChangeModeFailure():
        return changeModeFailure(_that.remoteFailure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? changeTensLevelSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeTensLevelFailure,
    TResult? Function()? changeModeSuccess,
    TResult? Function(RemoteFailure remoteFailure)? changeModeFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial();
      case ChangeTensLevelSuccess() when changeTensLevelSuccess != null:
        return changeTensLevelSuccess();
      case ChangeTensLevelFailure() when changeTensLevelFailure != null:
        return changeTensLevelFailure(_that.remoteFailure);
      case ChangeModeSuccess() when changeModeSuccess != null:
        return changeModeSuccess();
      case ChangeModeFailure() when changeModeFailure != null:
        return changeModeFailure(_that.remoteFailure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class Initial implements DeviceControlTensState {
  const Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlTensState.initial()';
  }
}

/// @nodoc

class ChangeTensLevelSuccess implements DeviceControlTensState {
  const ChangeTensLevelSuccess();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ChangeTensLevelSuccess);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlTensState.changeTensLevelSuccess()';
  }
}

/// @nodoc

class ChangeTensLevelFailure implements DeviceControlTensState {
  const ChangeTensLevelFailure(this.remoteFailure);

  final RemoteFailure remoteFailure;

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ChangeTensLevelFailureCopyWith<ChangeTensLevelFailure> get copyWith =>
      _$ChangeTensLevelFailureCopyWithImpl<ChangeTensLevelFailure>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ChangeTensLevelFailure &&
            (identical(other.remoteFailure, remoteFailure) ||
                other.remoteFailure == remoteFailure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, remoteFailure);

  @override
  String toString() {
    return 'DeviceControlTensState.changeTensLevelFailure(remoteFailure: $remoteFailure)';
  }
}

/// @nodoc
abstract mixin class $ChangeTensLevelFailureCopyWith<$Res>
    implements $DeviceControlTensStateCopyWith<$Res> {
  factory $ChangeTensLevelFailureCopyWith(ChangeTensLevelFailure value,
          $Res Function(ChangeTensLevelFailure) _then) =
      _$ChangeTensLevelFailureCopyWithImpl;
  @useResult
  $Res call({RemoteFailure remoteFailure});

  $RemoteFailureCopyWith<$Res> get remoteFailure;
}

/// @nodoc
class _$ChangeTensLevelFailureCopyWithImpl<$Res>
    implements $ChangeTensLevelFailureCopyWith<$Res> {
  _$ChangeTensLevelFailureCopyWithImpl(this._self, this._then);

  final ChangeTensLevelFailure _self;
  final $Res Function(ChangeTensLevelFailure) _then;

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? remoteFailure = null,
  }) {
    return _then(ChangeTensLevelFailure(
      null == remoteFailure
          ? _self.remoteFailure
          : remoteFailure // ignore: cast_nullable_to_non_nullable
              as RemoteFailure,
    ));
  }

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RemoteFailureCopyWith<$Res> get remoteFailure {
    return $RemoteFailureCopyWith<$Res>(_self.remoteFailure, (value) {
      return _then(_self.copyWith(remoteFailure: value));
    });
  }
}

/// @nodoc

class ChangeModeSuccess implements DeviceControlTensState {
  const ChangeModeSuccess();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ChangeModeSuccess);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DeviceControlTensState.changeModeSuccess()';
  }
}

/// @nodoc

class ChangeModeFailure implements DeviceControlTensState {
  const ChangeModeFailure(this.remoteFailure);

  final RemoteFailure remoteFailure;

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ChangeModeFailureCopyWith<ChangeModeFailure> get copyWith =>
      _$ChangeModeFailureCopyWithImpl<ChangeModeFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ChangeModeFailure &&
            (identical(other.remoteFailure, remoteFailure) ||
                other.remoteFailure == remoteFailure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, remoteFailure);

  @override
  String toString() {
    return 'DeviceControlTensState.changeModeFailure(remoteFailure: $remoteFailure)';
  }
}

/// @nodoc
abstract mixin class $ChangeModeFailureCopyWith<$Res>
    implements $DeviceControlTensStateCopyWith<$Res> {
  factory $ChangeModeFailureCopyWith(
          ChangeModeFailure value, $Res Function(ChangeModeFailure) _then) =
      _$ChangeModeFailureCopyWithImpl;
  @useResult
  $Res call({RemoteFailure remoteFailure});

  $RemoteFailureCopyWith<$Res> get remoteFailure;
}

/// @nodoc
class _$ChangeModeFailureCopyWithImpl<$Res>
    implements $ChangeModeFailureCopyWith<$Res> {
  _$ChangeModeFailureCopyWithImpl(this._self, this._then);

  final ChangeModeFailure _self;
  final $Res Function(ChangeModeFailure) _then;

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? remoteFailure = null,
  }) {
    return _then(ChangeModeFailure(
      null == remoteFailure
          ? _self.remoteFailure
          : remoteFailure // ignore: cast_nullable_to_non_nullable
              as RemoteFailure,
    ));
  }

  /// Create a copy of DeviceControlTensState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RemoteFailureCopyWith<$Res> get remoteFailure {
    return $RemoteFailureCopyWith<$Res>(_self.remoteFailure, (value) {
      return _then(_self.copyWith(remoteFailure: value));
    });
  }
}

// dart format on
