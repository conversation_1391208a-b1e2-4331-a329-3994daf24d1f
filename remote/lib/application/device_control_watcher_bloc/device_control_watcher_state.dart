part of 'device_control_watcher_bloc.dart';
@freezed
abstract class DeviceControlWatcherState with _$DeviceControlWatcherState {
  const factory DeviceControlWatcherState({
    required bool isTherapyActive,
    required Option<Either<RemoteFailure, Unit>> failureOrSuccessOption,
  }) = _DeviceControlWatcherState;

  factory DeviceControlWatcherState.initial() => DeviceControlWatcherState(
        isTherapyActive: false,
        failureOrSuccessOption: None(),
      );
}