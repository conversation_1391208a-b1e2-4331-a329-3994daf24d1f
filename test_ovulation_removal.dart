// Test script to verify ovulation removal logic
// This demonstrates the improved ovulation removal workflow

void main() {
  print('=== Ovulation Removal Test ===\n');

  // Simulate initial period dates
  final initialPeriodDates = {
    DateTime(2024, 1, 1), // Period start
    DateTime(2024, 1, 2), // Period day 2
    DateTime(2024, 1, 3), // Period day 3
    DateTime(2024, 1, 29), // Next period start (28-day cycle)
    DateTime(2024, 1, 30), // Next period day 2
  };

  // Simulate calculated ovulation dates (would be ~14 days before next period)
  final initialOvulationDates = {
    DateTime(2024, 1, 15), // Ovulation for first cycle
    DateTime(2024, 1, 16), // Ovulation window
    DateTime(2024, 1, 17), // Ovulation window
  };

  print('📅 Initial Period Dates: ${formatDates(initialPeriodDates)}');
  print('🥚 Initial Ovulation Dates: ${formatDates(initialOvulationDates)}');
  print('');

  // User removes some period dates
  final removedPeriodDates = {
    DateTime(2024, 1, 1), // Remove first period start
    DateTime(2024, 1, 2), // Remove period day 2
  };

  final remainingPeriodDates = initialPeriodDates.difference(removedPeriodDates);

  print('❌ Removed Period Dates: ${formatDates(removedPeriodDates)}');
  print('✅ Remaining Period Dates: ${formatDates(remainingPeriodDates)}');
  print('');

  // Test the wider ovulation range calculation
  final cycleLength = 28;
  final ovulationRange = calculateWiderOvulationRange(removedPeriodDates, cycleLength);
  
  print('🔍 Ovulation Removal Range:');
  print('   Start: ${ovulationRange.start}');
  print('   End: ${ovulationRange.end}');
  print('');

  // Check which ovulation dates would be removed
  final ovulationDatesToRemove = initialOvulationDates.where((date) =>
      date.isAfter(ovulationRange.start.subtract(const Duration(days: 1))) &&
      date.isBefore(ovulationRange.end.add(const Duration(days: 1)))).toSet();

  final remainingOvulationDates = initialOvulationDates.difference(ovulationDatesToRemove);

  print('🗑️ Ovulation Dates to Remove: ${formatDates(ovulationDatesToRemove)}');
  print('🥚 Remaining Ovulation Dates: ${formatDates(remainingOvulationDates)}');
  print('');

  // Recalculate ovulation for remaining period dates
  final newOvulationDates = calculateOvulationForPeriods(remainingPeriodDates, cycleLength);
  
  print('🔄 Recalculated Ovulation Dates: ${formatDates(newOvulationDates)}');
  print('');

  print('=== Expected Workflow ===');
  print('1. ❌ Remove period dates: ${formatDates(removedPeriodDates)}');
  print('2. 🗑️ Remove related ovulation dates: ${formatDates(ovulationDatesToRemove)}');
  print('3. 🔄 Recalculate ovulation for remaining periods: ${formatDates(remainingPeriodDates)}');
  print('4. ✅ Final ovulation dates: ${formatDates(newOvulationDates)}');
  print('');

  // Verify the fix
  final hasOrphanedOvulation = remainingOvulationDates.isNotEmpty && 
                               !newOvulationDates.containsAll(remainingOvulationDates);
  
  if (hasOrphanedOvulation) {
    print('⚠️ WARNING: Some ovulation dates may remain orphaned!');
    print('   Orphaned dates: ${formatDates(remainingOvulationDates.difference(newOvulationDates))}');
  } else {
    print('✅ SUCCESS: No orphaned ovulation dates detected!');
  }
}

// Calculate a wider ovulation range for more aggressive removal
({DateTime start, DateTime end}) calculateWiderOvulationRange(
    Set<DateTime> periodDates, int cycleLength) {
  if (periodDates.isEmpty) {
    final now = DateTime.now();
    return (start: now, end: now);
  }

  final earliestPeriod = periodDates.reduce((a, b) => a.isBefore(b) ? a : b);
  final latestPeriod = periodDates.reduce((a, b) => a.isAfter(b) ? a : b);

  // Use a wider range to catch all potentially related ovulation dates
  // Look from 5 days before the earliest period to 1.5 cycle lengths after the latest period
  final rangeStart = earliestPeriod.subtract(const Duration(days: 5));
  final rangeEnd = latestPeriod.add(Duration(days: (cycleLength * 1.5).round()));

  return (start: rangeStart, end: rangeEnd);
}

// Simple ovulation calculation for testing
Set<DateTime> calculateOvulationForPeriods(Set<DateTime> periodDates, int cycleLength) {
  if (periodDates.isEmpty) return {};
  
  final ovulationDates = <DateTime>{};
  
  // Group periods into cycles (simplified)
  final sortedPeriods = periodDates.toList()..sort();
  
  for (final periodStart in sortedPeriods) {
    // Calculate ovulation ~14 days before next expected period
    final nextPeriodExpected = periodStart.add(Duration(days: cycleLength));
    final ovulationDate = nextPeriodExpected.subtract(const Duration(days: 14));
    
    // Add ovulation window (±2 days)
    ovulationDates.add(ovulationDate.subtract(const Duration(days: 2)));
    ovulationDates.add(ovulationDate.subtract(const Duration(days: 1)));
    ovulationDates.add(ovulationDate);
    ovulationDates.add(ovulationDate.add(const Duration(days: 1)));
    ovulationDates.add(ovulationDate.add(const Duration(days: 2)));
  }
  
  return ovulationDates;
}

String formatDates(Set<DateTime> dates) {
  if (dates.isEmpty) return 'None';
  final sortedDates = dates.toList()..sort();
  return sortedDates.map((d) => '${d.month}/${d.day}').join(', ');
}
