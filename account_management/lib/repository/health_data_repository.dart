import 'package:account_management/domain/failure/health_data_failure.dart';
import 'package:account_management/domain/model/health_data.dart';
import 'package:account_management/domain/model/period_prediction_metadata.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:doso/doso.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fpdart/src/effect.dart';
import 'package:fpdart/src/unit.dart';
import 'package:injectable/injectable.dart';
import 'package:account_management/domain/facade/health_data_facade.dart';

@LazySingleton(as: HealthDataFacade)
class HealthDataRepository implements HealthDataFacade {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  @override
  Future<Do<HealthDataFailure, Unit>> updateContraceptionType(
      String contraceptionType) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      return userDoc
          .update({'healthData.contraceptionType': contraceptionType}).then(
              (_) => const Do.success(unit));
    } on FirebaseException catch (e) {
      return Do.failure(HealthDataFailure.contraceptionTypeFailure());
    }
  }

  @override
  Future<Do<HealthDataFailure, Unit>> updateCycleLength(int cycleLength) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      return userDoc.update({'healthData.cycleLength': cycleLength}).then(
          (_) => const Do.success(unit));
    } on FirebaseException catch (e) {
      return Do.failure(HealthDataFailure.cycleLengthFailure());
    }
  }

  @override
  Future<Do<HealthDataFailure, Unit>> updateOvulationDate(
      DateTime ovulationDate) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      return userDoc.update({
        'healthData.ovulationDate': Timestamp.fromDate(ovulationDate)
      }).then((_) => const Do.success(unit));
    } on FirebaseException catch (e) {
      return Do.failure(HealthDataFailure.ovulationDateFailure());
    }
  }

  @override
  Future<Do<HealthDataFailure, Unit>> updatePeriodLength(
      int periodLength) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      return userDoc.update({'healthData.periodLength': periodLength}).then(
          (_) => const Do.success(unit));
    } on FirebaseException catch (e) {
      return Do.failure(HealthDataFailure.periodLengthFailure());
    }
  }

  @override
  Future<Do<HealthDataFailure, Unit>> updateNextPeriodStartDate(
      DateTime nextPeriodStartDate) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      return userDoc.update({
        'healthData.nextPeriodStartDate':
            Timestamp.fromDate(nextPeriodStartDate)
      }).then((_) => const Do.success(unit));
    } on FirebaseException catch (e) {
      return Do.failure(HealthDataFailure.ovulationDateFailure());
    }
  }

  @override
  Future<Do<HealthDataFailure, Unit>> updateNextOvulationDate(
      DateTime nextOvulationDate) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      return userDoc.update({
        'healthData.nextOvulationDate': Timestamp.fromDate(nextOvulationDate)
      }).then((_) => const Do.success(unit));
    } on FirebaseException catch (e) {
      return Do.failure(HealthDataFailure.ovulationDateFailure());
    }
  }

  @override
  Future<Do<HealthDataFailure, Unit>> updatePredictionMetadata(
      PeriodPredictionMetadata metadata) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);

      // Update both the prediction metadata and the basic cycle/period lengths
      final updates = <String, dynamic>{
        'healthData.predictionMetadata': metadata.toJson(),
        // Also update the basic fields for backward compatibility
        'healthData.cycleLength': metadata.averageCycleLength?.round() ?? 28,
        'healthData.periodLength': metadata.averagePeriodLength?.round() ?? 5,
      };

      return userDoc.update(updates).then((_) => const Do.success(unit));
    } on FirebaseException catch (e) {
      return Do.failure(HealthDataFailure.cycleLengthFailure());
    }
  }

  @override
  Stream<Do<HealthDataFailure, HealthDataModel?>> watchHealthData() async* {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        yield const Do.failure(HealthDataFailure.unexpected());
        return;
      }

      final userDoc = _firestore.collection('users').doc(user.uid);
      yield* userDoc.snapshots().map((snapshot) {
        try {
          if (snapshot.exists) {
            final data = snapshot.data();
            final healthData = data?['healthData'] as Map<String, dynamic>?;

            if (healthData != null) {
              return Do.success(HealthDataModel.fromJson(healthData));
            } else {
              return const Do.success(null);
            }
          } else {
            return const Do.success(null);
          }
        } catch (e) {
          return const Do.failure(HealthDataFailure.unexpected());
        }
      });
    } catch (e) {
      yield const Do.failure(HealthDataFailure.unexpected());
    }
  }
}
