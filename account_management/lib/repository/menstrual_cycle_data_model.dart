import 'package:freezed_annotation/freezed_annotation.dart';
part 'menstrual_cycle_data_model.freezed.dart';

@freezed
class MenstrualCycleData with _$MenstrualCycleData {
  final int currentCycleDay;
  final int periodDays;
  final int cycleLength;
  final int ovulationDayStart;
  final int ovulationDaysLength;

  const MenstrualCycleData({
    required this.currentCycleDay,
    required this.periodDays,
    required this.cycleLength,
    required this.ovulationDayStart,
    required this.ovulationDaysLength,
  });



}