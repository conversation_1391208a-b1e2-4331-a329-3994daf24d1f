import 'package:doso/doso.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import '../domain/core/unit.dart';
import '../domain/facade/symptom_tracking_facade.dart';
import '../domain/failure/period_tracking_failure.dart';
import '../domain/model/period_tracking_model.dart';
import '../domain/model/symptom_model.dart';
import '../infrastructure/services/firestore_service.dart';

/// Repository for managing symptom tracking functionality
@LazySingleton(as: SymptomTrackingFacade)
class SymptomTrackingRepository implements SymptomTrackingFacade {
  final FirestoreService _firestoreService;

  SymptomTrackingRepository(this._firestoreService);

  @override
  Future<Do<PeriodTrackingFailure, PeriodTrackingModel?>> getSymptomData({
    required DateTime date,
  }) async {
    try {
      debugPrint('📊 Getting symptom data for ${date.toString()}');

      final result = await _firestoreService.getSymptomData(date: date);

      return result.fold(
        onFailure: (failure) {
          debugPrint('❌ Failed to get symptom data: $failure');
          return Do.failure(failure);
        },
        onSuccess: (model) {
          debugPrint(
              '✅ Retrieved symptom data: ${model != null ? 'found' : 'not found'}');
          return Do.success(model);
        },
      );
    } catch (e) {
      debugPrint('❌ Error getting symptom data: $e');
      return const Do.failure(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<Do<PeriodTrackingFailure, Unit>> saveSymptomData({
    required DateTime date,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
  }) async {
    try {
      debugPrint('📊 Saving symptom data for ${date.toString()}');
      debugPrint(
          '📊 Symptoms: ${symptoms?.length ?? 0}, Pain: $painLevel, Flow: $flowLevel');

      // Prepare symptom data
      final symptomData = <String, dynamic>{};

      if (symptoms != null) {
        symptomData['symptoms'] = symptoms.map((s) => s.toJson()).toList();
      }

      if (painLevel != null) {
        symptomData['painLevel'] = painLevel;
      }

      if (flowLevel != null) {
        symptomData['flowLevel'] = flowLevel;
      }

      // Only save if there's actual data to save
      if (symptomData.isEmpty) {
        debugPrint('ℹ️ No symptom data to save');
        return const Do.success(unit);
      }

      final result = await _firestoreService.saveSymptomData(
        date: date,
        symptomData: symptomData,
      );

      return result.fold(
        onFailure: (failure) {
          debugPrint('❌ Failed to save symptom data: $failure');
          return Do.failure(failure);
        },
        onSuccess: (_) {
          debugPrint('✅ Successfully saved symptom data');
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('❌ Error saving symptom data: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  @override
  Future<Do<PeriodTrackingFailure, Unit>> saveFlowLevel({
    required DateTime date,
    required int flowLevel,
  }) async {
    return saveSymptomData(date: date, flowLevel: flowLevel);
  }

  @override
  Future<Do<PeriodTrackingFailure, Unit>> savePainLevel({
    required DateTime date,
    required int painLevel,
  }) async {
    return saveSymptomData(date: date, painLevel: painLevel);
  }

  @override
  Future<int?> getFlowLevel(DateTime date) async {
    try {
      final result = await getSymptomData(date: date);
      return result.fold(
        onFailure: (_) => null,
        onSuccess: (data) => data?.flowLevel,
      );
    } catch (e) {
      debugPrint('❌ Error getting flow level: $e');
      return null;
    }
  }

  @override
  Future<int?> getPainLevel(DateTime date) async {
    try {
      final result = await getSymptomData(date: date);
      return result.fold(
        onFailure: (_) => null,
        onSuccess: (data) => data?.painLevel,
      );
    } catch (e) {
      debugPrint('❌ Error getting pain level: $e');
      return null;
    }
  }

  @override
  Future<List<SymptomModel>?> getSymptoms(DateTime date) async {
    try {
      final result = await getSymptomData(date: date);
      return result.fold(
        onFailure: (_) => null,
        onSuccess: (data) => data?.symptoms,
      );
    } catch (e) {
      debugPrint('❌ Error getting symptoms: $e');
      return null;
    }
  }

  @override
  Future<bool> hasSymptomData(DateTime date) async {
    try {
      final result = await getSymptomData(date: date);
      return result.fold(
        onFailure: (_) => false,
        onSuccess: (data) => data != null,
      );
    } catch (e) {
      debugPrint('❌ Error checking symptom data: $e');
      return false;
    }
  }

  @override
  Future<Do<PeriodTrackingFailure, Unit>> deleteSymptomData({
    required DateTime date,
  }) async {
    try {
      debugPrint('🗑️ Deleting symptom data for ${date.toString()}');

      // Save empty data to effectively clear the symptoms
      final result = await _firestoreService.saveSymptomData(
        date: date,
        symptomData: {
          'symptoms': null,
          'painLevel': null,
          'flowLevel': null,
        },
      );

      return result.fold(
        onFailure: (failure) {
          debugPrint('❌ Failed to delete symptom data: $failure');
          return Do.failure(failure);
        },
        onSuccess: (_) {
          debugPrint('✅ Successfully deleted symptom data');
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('❌ Error deleting symptom data: $e');
      return const Do.failure(PeriodTrackingFailure.deleteFailure());
    }
  }

  @override
  Future<Do<PeriodTrackingFailure, Unit>> updateSymptomData({
    required DateTime date,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
  }) async {
    try {
      debugPrint('� Updating symptom data for ${date.toString()}');

      // First, get existing data
      final existingResult = await getSymptomData(date: date);

      return existingResult.fold(
        onFailure: (failure) {
          debugPrint('❌ Failed to get existing symptom data: $failure');
          return Do.failure(failure);
        },
        onSuccess: (existingData) async {
          // Merge with existing data
          final mergedSymptoms = symptoms ?? existingData?.symptoms;
          final mergedPainLevel = painLevel ?? existingData?.painLevel;
          final mergedFlowLevel = flowLevel ?? existingData?.flowLevel;

          // Save the merged data
          return await saveSymptomData(
            date: date,
            symptoms: mergedSymptoms,
            painLevel: mergedPainLevel,
            flowLevel: mergedFlowLevel,
          );
        },
      );
    } catch (e) {
      debugPrint('❌ Error updating symptom data: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }
}
