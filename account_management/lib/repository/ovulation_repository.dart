import 'package:doso/doso.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import '../domain/core/unit.dart';
import '../domain/facade/ovulation_facade.dart';
import '../domain/facade/health_data_facade.dart';
import '../domain/failure/ovulation_failure.dart';
import '../domain/failure/period_tracking_failure.dart';
import '../infrastructure/services/period_data_service.dart';
import '../infrastructure/services/firestore_service.dart';

@LazySingleton(as: OvulationFacade)
class OvulationRepository implements OvulationFacade {
  final PeriodDataService _periodDataService;
  final FirestoreService _firestoreService;
  final HealthDataFacade _healthDataFacade;

  OvulationRepository(
    this._periodDataService,
    this._firestoreService,
    this._healthDataFacade,
  );

  @override
  Future<Do<OvulationFailure, Unit>> handlePeriodTrackingChanges({
    required Set<DateTime> newlySelected,
    required Set<DateTime> newlyDeselected,
    required Set<DateTime> allPeriodDates,
  }) async {
    debugPrint('🥚 OvulationRepository.handlePeriodTrackingChanges called');
    debugPrint(
        '🥚 Newly selected: ${newlySelected.length}, newly deselected: ${newlyDeselected.length}');
    debugPrint('🥚 All period dates: ${allPeriodDates.length}');

    try {
      // Get user's health data for cycle calculations
      final healthDataResult = await _healthDataFacade.watchHealthData().first;
      int cycleLength = 28;
      int periodLength = 5;

      healthDataResult.fold(
        onFailure: (failure) {
          debugPrint('🥚 Using default cycle/period lengths: $failure');
        },
        onSuccess: (healthData) {
          if (healthData != null) {
            // Use prediction metadata averages if available, otherwise fall back to basic fields
            if (healthData.predictionMetadata != null) {
              cycleLength =
                  healthData.predictionMetadata!.averageCycleLength?.round() ??
                      28;
              periodLength =
                  healthData.predictionMetadata!.averagePeriodLength?.round() ??
                      5;
              debugPrint(
                  '🥚 Using prediction metadata: cycle=$cycleLength, period=$periodLength');
            } else {
              cycleLength = healthData.cycleLength ?? 28;
              periodLength = healthData.periodLength ?? 5;
              debugPrint(
                  '🥚 Using basic health data: cycle=$cycleLength, period=$periodLength');
            }
          }
        },
      );

      // Step 1: Remove ovulation dates for deselected period cycles
      if (newlyDeselected.isNotEmpty) {
        debugPrint('🥚 Step 1: Removing ovulation dates for deselected cycles');
        final ovulationDatesToRemove =
            await _getOvulationDatesForAffectedCycles(
          newlyDeselected,
          cycleLength,
        );

        if (ovulationDatesToRemove.isNotEmpty) {
          final removeResult =
              await _firestoreService.batchUpdateOvulationDates(
            ovulationDates: ovulationDatesToRemove,
            isOvulationDate: false,
          );

          if (removeResult.isFailure) {
            final failure = removeResult.fold(
              onFailure: (f) => _mapPeriodTrackingFailureToOvulationFailure(f),
              onSuccess: (_) => const OvulationFailure.unexpected(),
            );
            debugPrint('🥚 Failed to remove ovulation dates: $failure');
            return Do.failure(failure);
          }
        }
      }

      // Step 2: Calculate and save ovulation dates for all period dates
      if (allPeriodDates.isNotEmpty) {
        debugPrint(
            '🥚 Step 2: Calculating ovulation dates for all period data');
        final ovulationDates =
            _calculateOvulationDates(allPeriodDates, cycleLength, periodLength);

        if (ovulationDates.isNotEmpty) {
          final saveResult = await _firestoreService.batchUpdateOvulationDates(
            ovulationDates: ovulationDates,
            isOvulationDate: true,
          );

          if (saveResult.isFailure) {
            final failure = saveResult.fold(
              onFailure: (f) => _mapPeriodTrackingFailureToOvulationFailure(f),
              onSuccess: (_) => const OvulationFailure.unexpected(),
            );
            debugPrint('🥚 Failed to save ovulation dates: $failure');
            return Do.failure(failure);
          }
        }

        // Step 3: Update next ovulation date in health data
        debugPrint('🥚 Step 3: Updating next ovulation date in health data');
        final updateResult = await updateNextOvulationDate(ovulationDates);
        if (updateResult.isFailure) {
          debugPrint('🥚 Failed to update next ovulation date (non-critical)');
          // Don't fail the entire operation for this
        }
      }

      debugPrint('🥚 Successfully handled period tracking changes');
      return const Do.success(unit);
    } catch (e) {
      debugPrint('🥚 Error handling period tracking changes: $e');
      return const Do.failure(OvulationFailure.unexpected());
    }
  }

  @override
  Future<Do<OvulationFailure, Set<DateTime>>>
      getAllExistingOvulationDates() async {
    debugPrint('🥚 OvulationRepository.getAllExistingOvulationDates called');

    try {
      final ovulationDates = await _getOvulationDatesFromFirestore();
      debugPrint('🥚 Found ${ovulationDates.length} existing ovulation dates');
      return Do.success(ovulationDates);
    } catch (e) {
      debugPrint('🥚 Error getting all existing ovulation dates: $e');
      return const Do.failure(OvulationFailure.getOvulationDatesFailure());
    }
  }

  @override
  Future<Do<OvulationFailure, Unit>> updateNextOvulationDate(
    Set<DateTime> calculatedOvulationDates,
  ) async {
    debugPrint(
        '🥚 OvulationRepository.updateNextOvulationDate called with ${calculatedOvulationDates.length} dates');

    try {
      if (calculatedOvulationDates.isEmpty) {
        debugPrint('🥚 No ovulation dates to update in health data');
        return Do.success(unit);
      }

      final now = DateTime.now();
      final futureOvulationDates = calculatedOvulationDates
          .where((date) => date.isAfter(now))
          .toList()
        ..sort();

      if (futureOvulationDates.isEmpty) {
        debugPrint('🥚 No future ovulation dates to update in health data');
        return Do.success(unit);
      }

      final nextOvulationDate = futureOvulationDates.first;
      debugPrint('🥚 Updating next ovulation date to: $nextOvulationDate');

      // Update health data with next ovulation date
      final healthDataResult =
          await _healthDataFacade.updateNextOvulationDate(nextOvulationDate);

      return healthDataResult.fold(
        onFailure: (failure) {
          debugPrint(
              '🥚 Failed to update next ovulation date in health data: $failure');
          return const Do.failure(OvulationFailure.updateFailure());
        },
        onSuccess: (_) {
          debugPrint(
              '🥚 Successfully updated next ovulation date in health data');
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('🥚 Error updating next ovulation date: $e');
      return const Do.failure(OvulationFailure.unexpected());
    }
  }

  @override
  Future<Do<OvulationFailure, Unit>> clearAllOvulationDates() async {
    debugPrint('🥚 OvulationRepository.clearAllOvulationDates called');

    try {
      final existingOvulationDates = await _getOvulationDatesFromFirestore();

      if (existingOvulationDates.isEmpty) {
        debugPrint('🥚 No ovulation dates to clear');
        return Do.success(unit);
      }

      final result = await _firestoreService.batchUpdateOvulationDates(
        ovulationDates: existingOvulationDates,
        isOvulationDate: false,
      );

      return result.fold(
        onFailure: (failure) {
          debugPrint('🥚 Failed to clear all ovulation dates: $failure');
          return Do.failure(
              _mapPeriodTrackingFailureToOvulationFailure(failure));
        },
        onSuccess: (_) {
          debugPrint('🥚 Successfully cleared all ovulation dates');
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('🥚 Error clearing all ovulation dates: $e');
      return const Do.failure(OvulationFailure.unexpected());
    }
  }

  /// Helper method to get all ovulation dates from Firestore
  Future<Set<DateTime>> _getOvulationDatesFromFirestore() async {
    final allDates = <DateTime>{};
    final now = DateTime.now();

    // Look back 2 years and forward 1 year for comprehensive data
    final startYear = now.year - 2;
    final endYear = now.year + 1;

    for (int year = startYear; year <= endYear; year++) {
      try {
        final yearDataResult =
            await _firestoreService.watchYearData(year).first;

        yearDataResult.fold(
          onFailure: (failure) {
            debugPrint('🥚 Failed to get year data for $year: $failure');
          },
          onSuccess: (yearData) {
            final ovulationDates =
                _periodDataService.extractOvulationDatesFromYearData(yearData);
            allDates.addAll(ovulationDates);
          },
        );
      } catch (e) {
        debugPrint('🥚 Error getting ovulation dates for year $year: $e');
      }
    }

    return allDates;
  }

  /// Helper method to map PeriodTrackingFailure to OvulationFailure
  OvulationFailure _mapPeriodTrackingFailureToOvulationFailure(
      dynamic failure) {
    // Map common failure types
    final failureString = failure.toString();

    if (failureString.contains('updateFailure') ||
        failureString.contains('UpdateFailure')) {
      return const OvulationFailure.updateFailure();
    } else if (failureString.contains('deleteFailure') ||
        failureString.contains('DeleteFailure')) {
      return const OvulationFailure.deleteFailure();
    } else if (failureString.contains('createFailure') ||
        failureString.contains('CreateFailure')) {
      return const OvulationFailure.createFailure();
    } else if (failureString.contains('unauthenticated') ||
        failureString.contains('Unauthenticated')) {
      return const OvulationFailure.unauthenticated();
    } else if (failureString.contains('invalidData') ||
        failureString.contains('InvalidData')) {
      return const OvulationFailure.invalidData();
    } else if (failureString.contains('notFound') ||
        failureString.contains('NotFound')) {
      return const OvulationFailure.notFound();
    } else {
      return const OvulationFailure.unexpected();
    }
  }

  /// Calculate ovulation dates from period dates using simple cycle logic
  Set<DateTime> _calculateOvulationDates(
    Set<DateTime> periodDates,
    int cycleLength,
    int periodLength,
  ) {
    if (periodDates.isEmpty) return {};

    final ovulationDates = <DateTime>{};

    // Group consecutive period dates into cycles
    final periodCycles = _periodDataService.calculatePeriodCycles(periodDates);

    debugPrint(
        '🥚 Calculating ovulation for ${periodCycles.length} period cycles');

    for (final cycle in periodCycles) {
      final cycleStart = cycle.first;

      // Calculate ovulation date: 14 days before expected next period
      final expectedNextPeriod = cycleStart.add(Duration(days: cycleLength));
      final ovulationPeakDate =
          expectedNextPeriod.subtract(const Duration(days: 14));

      // Only calculate if ovulation would be after the period ends
      final periodEndDate = cycleStart.add(Duration(days: periodLength));

      if (ovulationPeakDate.isAfter(periodEndDate)) {
        // Create 5-day ovulation window: ±2 days from peak
        for (int j = -2; j <= 2; j++) {
          final ovulationDate = ovulationPeakDate.add(Duration(days: j));

          // Ensure ovulation date is after period ends
          if (ovulationDate.isAfter(periodEndDate)) {
            // Normalize to midnight
            final normalizedDate = DateTime(
                ovulationDate.year, ovulationDate.month, ovulationDate.day);
            ovulationDates.add(normalizedDate);
          }
        }
      }
    }

    debugPrint(
        '🥚 Calculated ${ovulationDates.length} ovulation dates from ${periodCycles.length} cycles');
    return ovulationDates;
  }

  /// Get ovulation dates that could be affected by changes to the given period dates
  Future<Set<DateTime>> _getOvulationDatesForAffectedCycles(
    Set<DateTime> affectedPeriodDates,
    int cycleLength,
  ) async {
    if (affectedPeriodDates.isEmpty) return {};

    final earliestDate =
        affectedPeriodDates.reduce((a, b) => a.isBefore(b) ? a : b);
    final latestDate =
        affectedPeriodDates.reduce((a, b) => a.isAfter(b) ? a : b);

    // Calculate a wider range to catch all potentially affected ovulation dates
    final rangeStart = earliestDate.subtract(Duration(days: cycleLength));
    final rangeEnd = latestDate.add(Duration(days: cycleLength));

    debugPrint(
        '🥚 Searching for ovulation dates to remove in range: $rangeStart to $rangeEnd');

    // Get existing ovulation dates in that range
    final ovulationDates = await _firestoreService
        .getExistingOvulationDatesInRange(rangeStart, rangeEnd);

    debugPrint(
        '🥚 Found ${ovulationDates.length} ovulation dates to potentially remove');
    return ovulationDates;
  }
}
