import 'package:account_management/domain/model/daily_medication_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import '../domain/facade/medication_facade.dart';
import '../domain/failure/medication_failure.dart';
import '../domain/model/medication_model.dart';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart'; // for TimeOfDay


@LazySingleton(as: MedicationFacade)
class MedicationRepository implements MedicationFacade {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;


  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  // Retrieves a stream of medication data for the current user.
  @override
  Stream<Either<MedicationFailure,
      List<MedicationModel>>> getMedications() async* {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('medications').doc(user!.uid);

      // Maps each document snapshot to a list of MedicationModel.
      yield* userDoc.snapshots().map((doc) {
        if (doc.exists) {
          final List<MedicationModel> medications = [];
          for (final med in doc.data()!['medications']) {
            medications.add(MedicationModel.fromJson(med));
          }
          return Right(medications);
        } else {
          return Right(<MedicationModel>[]);
        }
      });
    } on FirebaseException catch (e) {
      yield const Left(MedicationFailure.getMedicationsFailure());
    }
  }

  // Creates a new medication document or updates an existing one with new medication data.
  @override
  Future<Either<MedicationFailure, Unit>> create(
      MedicationModel medication) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) return const Left(MedicationFailure.createFailure());

      final userDocSnapshot = await _firestore.collection('medications').doc(
          user.uid).get();

      if (!userDocSnapshot.exists) {
        _firestore.collection('medications').doc(user.uid).set(
            {'medications': [medication.toJson()]});
      } else {
        _firestore.collection('medications').doc(user.uid).update(
            {'medications': FieldValue.arrayUnion([medication.toJson()])});
      }

      return const Right(unit);
    } on FirebaseException catch (e) {
      return const Left(MedicationFailure.createFailure());
    }
  }

  // Deletes a specific medication from the user's document.
  @override
  Future<Either<MedicationFailure, Unit>> delete(
      MedicationModel medication) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('medications').doc(user!.uid);
      return userDoc.update({
        'medications': FieldValue.arrayRemove([medication.toJson()])
      }).then((_) => const Right(unit));
    } on FirebaseException catch (e) {
     debugPrint(e.message);
      return const Left(MedicationFailure.deleteFailure());
    }
  }

  @override
  Future<Either<MedicationFailure, Unit>> update(
      MedicationModel medication) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) return const Left(MedicationFailure.updateFailure());

      final userDocRef = _firestore.collection('medications').doc(user.uid);
      final docSnapshot = await userDocRef.get();

      if (!docSnapshot.exists) {
        return const Left(MedicationFailure.updateFailure());
      }

      final List<dynamic> medications = docSnapshot.data()!['medications'];
      final int index = medications.indexWhere((m) => m['id'] == medication.id);

      if (index != -1) {
        medications[index] = medication.toJson();
      } else {
        // Handle the case where the medication doesn't exist. For now, let's just add it.
        medications.add(medication.toJson());
      }

      await userDocRef.update({'medications': medications});
      return const Right(unit);
    } on FirebaseException catch (e) {
      return const Left(MedicationFailure.updateFailure());
    }
  }


  @override
  Stream<Either<MedicationFailure, List<DailyMedicationModel>>> getDailyMedications() {
    final today = DateTime.now();
    final todayString = DateFormat('EEE').format(today); // e.g., "Mon"


    return getMedications().map((either) {
      return either.map((medications) {
        final List<DailyMedicationModel> doses = [];

        for (final med in medications) {
          final freq = med.frequencyUnit?.toLowerCase();
          final shouldInclude = switch (freq) {
            'daily' => true,
            'weekly' => med.daystoBeNotified.contains(todayString),
            'monthly' => med.monthlyDateToBeNotified?.day == today.day,
            _ => false,
          };

          if (!shouldInclude) continue;
          if (med.timeofDay.isEmpty) {
            doses.add(DailyMedicationModel(
              medName: med.name,
              time: null,
              medFrequency: freq,
              medDosage: med.dosage,
              medDosageUnit: med.dosageUnit,
              medNotes: med.notes,
              loggedTime: null,
            ));
          } else {
            for (final timeStr in med.timeofDay) {
              try {
                final parsedTime = DateFormat("HH:mm").parse(timeStr);
                final timeOfDay = TimeOfDay.fromDateTime(parsedTime);

                doses.add(DailyMedicationModel(
                  medName: med.name,
                  time: timeOfDay,
                  medFrequency: freq,
                  medDosage: med.dosage,
                  medDosageUnit: med.dosageUnit,
                  medNotes: med.notes,
                  loggedTime: null,
                ));
              } catch (_) {
                continue; // skip bad format
              }
            }
          }
        }

        // Sort by TimeOfDay
        doses.sort((a, b) {
          if (a.time == null && b.time == null) return 0;
          if (a.time == null) return 1;
          if (b.time == null) return -1;
          final aMinutes = a.time!.hour * 60 + a.time!.minute;
          final bMinutes = b.time!.hour * 60 + b.time!.minute;
          return aMinutes.compareTo(bMinutes);
        });


        return doses;
      });
    });
  }



}