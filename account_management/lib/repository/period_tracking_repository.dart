import 'dart:async';
import 'package:doso/doso.dart';
import 'package:injectable/injectable.dart';
import '../domain/core/unit.dart';
import '../domain/facade/period_tracking_facade.dart';
import '../domain/facade/health_data_facade.dart';
import '../domain/failure/period_tracking_failure.dart';
import '../domain/model/period_tracking_model.dart';
import '../domain/model/period_prediction_metadata.dart';
import '../domain/service/period_prediction_engine.dart';

import '../infrastructure/services/firestore_service.dart';
import '../infrastructure/services/period_data_service.dart';
import '../infrastructure/services/prediction_service.dart';

/// Modular Period Tracking Repository using service-based architecture
@LazySingleton(as: PeriodTrackingFacade)
class PeriodTrackingRepository implements PeriodTrackingFacade {
  final FirestoreService _firestoreService;
  final PeriodDataService _periodDataService;
  final PredictionService _predictionService;
  final HealthDataFacade _healthDataFacade;

  PeriodTrackingRepository(
    this._firestoreService,
    this._periodDataService,
    this._predictionService,
    this._healthDataFacade,
  );

  // ===== DATA WATCHING =====

  @override
  Stream<
      Do<PeriodTrackingFailure,
          Map<String, Map<String, PeriodTrackingModel>>>> watchYearData(
      int year) {
    print('🔍 PeriodTrackingRepository.watchYearData called for year: $year');

    // Actually call the FirestoreService to watch year data
    return _firestoreService.watchYearData(year).map((result) {
      return result.fold(
        onFailure: (failure) {
          print('❌ PeriodTrackingRepository.watchYearData failed: $failure');
          return Do.failure(failure);
        },
        onSuccess: (data) {
          print(
              '✅ PeriodTrackingRepository.watchYearData success: ${data.keys.length} months found');

          // Debug: Print detailed data structure
          for (final monthEntry in data.entries) {
            final monthKey = monthEntry.key;
            final monthData = monthEntry.value;
            print('📅 Month $monthKey: ${monthData.keys.length} days');

            // Print first few days for debugging
            int count = 0;
            for (final dayEntry in monthData.entries) {
              if (count < 3) {
                // Only print first 3 days to avoid spam
                final dayKey = dayEntry.key;
                final model = dayEntry.value;
                print(
                    '   Day $dayKey: isPeriod=${model.isPeriodDate}, isOvulation=${model.isOvulationDate}, date=${model.date}');
                count++;
              }
            }
            if (monthData.length > 3) {
              print('   ... and ${monthData.length - 3} more days');
            }
          }

          return Do.success(data);
        },
      );
    });
  }

  // ===== PERIOD DATE MANAGEMENT =====

  @override
  Future<Do<PeriodTrackingFailure, Unit>> selectPeriodDates(
    Set<DateTime> selectedDates, {
    Map<DateTime, int>? flowLevels,
  }) async {
    print(
        '🔍 PeriodTrackingRepository.selectPeriodDates called with ${selectedDates.length} dates');
    if (flowLevels != null && flowLevels.isNotEmpty) {
      print('💧 With flow levels for ${flowLevels.length} dates');
    }

    // Step 1: Save the period dates
    final result = await _periodDataService.selectPeriodDates(
      selectedDates,
      flowLevels: flowLevels,
    );

    return result.fold(
      onFailure: (failure) {
        print('❌ PeriodTrackingRepository.selectPeriodDates failed: $failure');
        return Do.failure(failure);
      },
      onSuccess: (_) async {
        print('✅ PeriodTrackingRepository.selectPeriodDates success');

        // Step 2: Update cycle averages
        await _updateCycleAverages();

        return Do.success(unit);
      },
    );
  }

  @override
  Future<Do<PeriodTrackingFailure, Unit>> deselectPeriodDates(
      Set<DateTime> datesToDeselect) async {
    print(
        '🔍 PeriodTrackingRepository.deselectPeriodDates called with ${datesToDeselect.length} dates');

    // Step 1: Deselect the period dates
    final result =
        await _periodDataService.deselectPeriodDates(datesToDeselect);

    return result.fold(
      onFailure: (failure) {
        print(
            '❌ PeriodTrackingRepository.deselectPeriodDates failed: $failure');
        return Do.failure(failure);
      },
      onSuccess: (_) async {
        print('✅ PeriodTrackingRepository.deselectPeriodDates success');

        // Step 2: Update cycle averages
        await _updateCycleAverages();

        return const Do.success(unit);
      },
    );
  }

  // ===== PREDICTION METHODS =====

  @override
  Future<Do<PeriodTrackingFailure, Unit>> updatePredictionMetadata(
    Map<String, Map<String, PeriodTrackingModel>> yearData,
  ) async {
    return Do.success(unit);
  }

  @override
  Future<Do<PeriodTrackingFailure, Unit>> handleFlowSymptomUpdate(
    DateTime date,
    int? flowLevel,
    Map<String, Map<String, PeriodTrackingModel>> yearData,
  ) async {
    return Do.success(unit);
  }

  @override
  Future<Do<PeriodTrackingFailure, Map<String, Set<DateTime>>>>
      generatePredictions({
    int monthsAhead = 6,
  }) async {
    print(
        '🔍 PeriodTrackingRepository.generatePredictions called with monthsAhead: $monthsAhead');

    // Actually call the PredictionService
    final result =
        await _predictionService.generatePredictions(monthsAhead: monthsAhead);

    return result.fold(
      onFailure: (failure) {
        print(
            '❌ PeriodTrackingRepository.generatePredictions failed: $failure');
        return Do.failure(failure);
      },
      onSuccess: (predictions) {
        print(
            '✅ PeriodTrackingRepository.generatePredictions success: ${predictions['periods']?.length ?? 0} periods, ${predictions['ovulations']?.length ?? 0} ovulations');
        return Do.success(predictions);
      },
    );
  }

  @override
  Future<Do<PeriodTrackingFailure, Unit>> initializePredictionsFromOnboarding(
    DateTime lastPeriodDate,
    int cycleLength,
    int periodLength,
  ) async {
    return Do.success(unit);
  }

  // ===== CONVENIENCE METHODS =====

  /// Get all existing period dates (convenience method)
  Future<Set<DateTime>> getAllExistingPeriodDates() async {
    return await _periodDataService.getAllExistingPeriodDates();
  }

  /// Calculate period cycles (convenience method)
  List<List<DateTime>> calculatePeriodCycles(Set<DateTime> periodDates) {
    return _periodDataService.calculatePeriodCycles(periodDates);
  }

  /// Extract period dates from year data (convenience method)
  Set<DateTime> extractPeriodDatesFromYearData(
      Map<String, Map<String, PeriodTrackingModel>> yearData) {
    return _periodDataService.extractPeriodDatesFromYearData(yearData);
  }

  /// Extract ovulation dates from year data (convenience method)
  Set<DateTime> extractOvulationDatesFromYearData(
      Map<String, Map<String, PeriodTrackingModel>> yearData) {
    return _periodDataService.extractOvulationDatesFromYearData(yearData);
  }

  /// Update cycle averages based on current period data
  Future<void> _updateCycleAverages() async {
    try {
      print('📊 Updating cycle averages...');

      // Get all existing period dates
      final allPeriodDates =
          await _periodDataService.getAllExistingPeriodDates();

      if (allPeriodDates.length < 2) {
        print('📊 Not enough period data for cycle calculation');
        return;
      }

      // Calculate cycles from period dates
      final cycles = _calculateSimpleCycles(allPeriodDates);

      if (cycles.isEmpty) {
        print('📊 No valid cycles found');
        return;
      }

      // Calculate cycle and period lengths
      final cycleLengths = <int>[];
      final periodLengths = <int>[];

      for (final cycle in cycles) {
        if (cycle.cycleLength != null &&
            cycle.cycleLength! >= 20 &&
            cycle.cycleLength! <= 45) {
          cycleLengths.add(cycle.cycleLength!);
        }
        if (cycle.periodLength != null &&
            cycle.periodLength! >= 1 &&
            cycle.periodLength! <= 10) {
          periodLengths.add(cycle.periodLength!);
        }
      }

      if (cycleLengths.isEmpty && periodLengths.isEmpty) {
        print('📊 No valid cycle/period lengths calculated');
        return;
      }

      // Get current health data to get existing prediction metadata
      final healthDataResult = await _healthDataFacade.watchHealthData().first;

      healthDataResult.fold(
        onFailure: (failure) {
          print('📊 Failed to get health data: $failure');
        },
        onSuccess: (healthData) async {
          // Create or get existing prediction metadata
          var metadata = healthData?.predictionMetadata ??
              PeriodPredictionMetadata.initial(
                initialCycleLength: healthData?.cycleLength?.toDouble() ?? 28.0,
                initialPeriodLength:
                    healthData?.periodLength?.toDouble() ?? 5.0,
              );

          // Update averages using the sophisticated prediction engine
          if (cycleLengths.isNotEmpty || periodLengths.isNotEmpty) {
            metadata = PeriodPredictionEngine.updateAverages(
              metadata,
              cycleLengths,
              periodLengths,
            );

            // Update health data with new prediction metadata
            final updateResult =
                await _healthDataFacade.updatePredictionMetadata(metadata);
            updateResult.fold(
              onFailure: (failure) =>
                  print('📊 Failed to update prediction metadata: $failure'),
              onSuccess: (_) {
                print(
                    '📊 Successfully updated cycle averages using prediction engine');
                print(
                    '📊 Average cycle length: ${metadata.averageCycleLength?.toStringAsFixed(1)} days');
                print(
                    '📊 Average period length: ${metadata.averagePeriodLength?.toStringAsFixed(1)} days');
                print('📊 Total cycles logged: ${metadata.totalCyclesLogged}');
                print(
                    '📊 Cycle variance: ${metadata.cycleVariance?.toStringAsFixed(2)}');
                print(
                    '📊 Has irregular cycles: ${metadata.hasIrregularCycles}');
              },
            );
          }
        },
      );
    } catch (e) {
      print('📊 Error updating cycle averages: $e');
    }
  }

  /// Calculate simple cycles from period dates
  List<DetectedCycle> _calculateSimpleCycles(Set<DateTime> periodDates) {
    if (periodDates.length < 2) return [];

    final cycles = <DetectedCycle>[];

    // Group consecutive dates into period cycles
    final periodCycles = _periodDataService.calculatePeriodCycles(periodDates);

    for (int i = 0; i < periodCycles.length - 1; i++) {
      final currentCycle = periodCycles[i];
      final nextCycle = periodCycles[i + 1];

      final cycleStart = currentCycle.first;
      final nextCycleStart = nextCycle.first;
      final cycleLength = nextCycleStart.difference(cycleStart).inDays;
      final periodLength = currentCycle.length;

      // Only include reasonable cycles
      if (cycleLength >= 20 && cycleLength <= 45) {
        cycles.add(DetectedCycle(
          startDate: cycleStart,
          endDate: nextCycleStart,
          cycleLength: cycleLength,
          periodLength: periodLength,
          isComplete: true,
        ));
      }
    }

    print(
        '📊 Calculated ${cycles.length} valid cycles from ${periodCycles.length} period cycles');
    return cycles;
  }
}
