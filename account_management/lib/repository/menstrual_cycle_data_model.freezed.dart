// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'menstrual_cycle_data_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MenstrualCycleData {
  int get currentCycleDay;
  int get periodDays;
  int get cycleLength;
  int get ovulationDayStart;
  int get ovulationDaysLength;

  /// Create a copy of MenstrualCycleData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MenstrualCycleDataCopyWith<MenstrualCycleData> get copyWith =>
      _$MenstrualCycleDataCopyWithImpl<MenstrualCycleData>(
          this as MenstrualCycleData, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MenstrualCycleData &&
            (identical(other.currentCycleDay, currentCycleDay) ||
                other.currentCycleDay == currentCycleDay) &&
            (identical(other.periodDays, periodDays) ||
                other.periodDays == periodDays) &&
            (identical(other.cycleLength, cycleLength) ||
                other.cycleLength == cycleLength) &&
            (identical(other.ovulationDayStart, ovulationDayStart) ||
                other.ovulationDayStart == ovulationDayStart) &&
            (identical(other.ovulationDaysLength, ovulationDaysLength) ||
                other.ovulationDaysLength == ovulationDaysLength));
  }

  @override
  int get hashCode => Object.hash(runtimeType, currentCycleDay, periodDays,
      cycleLength, ovulationDayStart, ovulationDaysLength);

  @override
  String toString() {
    return 'MenstrualCycleData(currentCycleDay: $currentCycleDay, periodDays: $periodDays, cycleLength: $cycleLength, ovulationDayStart: $ovulationDayStart, ovulationDaysLength: $ovulationDaysLength)';
  }
}

/// @nodoc
abstract mixin class $MenstrualCycleDataCopyWith<$Res> {
  factory $MenstrualCycleDataCopyWith(
          MenstrualCycleData value, $Res Function(MenstrualCycleData) _then) =
      _$MenstrualCycleDataCopyWithImpl;
  @useResult
  $Res call(
      {int currentCycleDay,
      int periodDays,
      int cycleLength,
      int ovulationDayStart,
      int ovulationDaysLength});
}

/// @nodoc
class _$MenstrualCycleDataCopyWithImpl<$Res>
    implements $MenstrualCycleDataCopyWith<$Res> {
  _$MenstrualCycleDataCopyWithImpl(this._self, this._then);

  final MenstrualCycleData _self;
  final $Res Function(MenstrualCycleData) _then;

  /// Create a copy of MenstrualCycleData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentCycleDay = null,
    Object? periodDays = null,
    Object? cycleLength = null,
    Object? ovulationDayStart = null,
    Object? ovulationDaysLength = null,
  }) {
    return _then(MenstrualCycleData(
      currentCycleDay: null == currentCycleDay
          ? _self.currentCycleDay
          : currentCycleDay // ignore: cast_nullable_to_non_nullable
              as int,
      periodDays: null == periodDays
          ? _self.periodDays
          : periodDays // ignore: cast_nullable_to_non_nullable
              as int,
      cycleLength: null == cycleLength
          ? _self.cycleLength
          : cycleLength // ignore: cast_nullable_to_non_nullable
              as int,
      ovulationDayStart: null == ovulationDayStart
          ? _self.ovulationDayStart
          : ovulationDayStart // ignore: cast_nullable_to_non_nullable
              as int,
      ovulationDaysLength: null == ovulationDaysLength
          ? _self.ovulationDaysLength
          : ovulationDaysLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// Adds pattern-matching-related methods to [MenstrualCycleData].
extension MenstrualCycleDataPatterns on MenstrualCycleData {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>() {
    final _that = this;
    switch (_that) {
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>() {
    final _that = this;
    switch (_that) {
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>() {
    final _that = this;
    switch (_that) {
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>() {
    final _that = this;
    switch (_that) {
      case _:
        return null;
    }
  }
}

// dart format on
