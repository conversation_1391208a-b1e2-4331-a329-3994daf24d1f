import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import '../../domain/core/unit.dart';
import '../../domain/facade/health_data_facade.dart';
import '../../domain/failure/period_tracking_failure.dart';
import '../../domain/model/period_tracking_model.dart';
import '../../domain/model/period_prediction_metadata.dart';
import '../../domain/service/period_prediction_engine.dart';
import 'period_data_service.dart';
import 'package:doso/doso.dart';

/// Service for managing period predictions and metadata
@injectable
class PredictionService {
  final HealthDataFacade _healthDataFacade;
  final PeriodDataService _periodDataService;

  PredictionService(
    this._healthDataFacade,
    this._periodDataService,
  );

  /// Updates prediction metadata when new period data is logged
  Future<Do<PeriodTrackingFailure, Unit>> updatePredictionMetadata(
    Map<String, Map<String, PeriodTrackingModel>> yearData,
  ) async {
    try {
      debugPrint('🔮 Updating prediction metadata from year data');

      // Extract period dates from year data
      final periodDates =
          _periodDataService.extractPeriodDatesFromYearData(yearData);

      if (periodDates.isEmpty) {
        debugPrint('ℹ️ No period dates found in year data');
        return const Do.success(unit);
      }

      debugPrint(
          '🔮 Found ${periodDates.length} period dates for metadata update');

      // Detect period starts using the prediction engine
      final periodStarts = PeriodPredictionEngine.detectPeriodStarts(
        _convertYearDataToEngineFormat(yearData),
      );

      // Calculate cycle and period lengths
      final cycleLengths =
          PeriodPredictionEngine.calculateCycleLengths(periodStarts);
      final periodLengths = PeriodPredictionEngine.calculatePeriodLengths(
        _convertYearDataToEngineFormat(yearData),
        periodStarts,
      );

      debugPrint('🔮 Detected ${periodStarts.length} period starts');
      debugPrint('🔮 Calculated ${cycleLengths.length} cycle lengths');
      debugPrint('🔮 Calculated ${periodLengths.length} period lengths');

      // Get current health data to create/update metadata
      final healthDataResult = await _healthDataFacade.watchHealthData().first;

      return await healthDataResult.fold(
        onFailure: (failure) async {
          debugPrint(
              '⚠️ Could not get health data for metadata update: $failure');
          return const Do.failure(PeriodTrackingFailure.unexpected());
        },
        onSuccess: (healthData) async {
          if (healthData == null) {
            debugPrint('⚠️ No health data available for metadata update');
            return const Do.failure(PeriodTrackingFailure.unexpected());
          }

          // Create initial metadata if needed
          var metadata = PeriodPredictionMetadata.initial(
            initialCycleLength: healthData.cycleLength?.toDouble() ?? 28.0,
            initialPeriodLength: healthData.periodLength?.toDouble() ?? 5.0,
            lastPeriodDate: periodStarts.isNotEmpty ? periodStarts.last : null,
          );

          // Update metadata with calculated averages if we have data
          if (cycleLengths.isNotEmpty || periodLengths.isNotEmpty) {
            metadata = PeriodPredictionEngine.updateAverages(
              metadata,
              cycleLengths,
              periodLengths,
            );
          }

          // Save updated metadata to health data by updating individual fields
          final cycleUpdateResult = await _healthDataFacade.updateCycleLength(
            metadata.averageCycleLength?.round() ?? 28,
          );

          // Check if cycle length update failed
          final cycleError = cycleUpdateResult.fold(
            onFailure: (failure) => failure,
            onSuccess: (_) => null,
          );
          if (cycleError != null) {
            debugPrint('❌ Failed to update cycle length: $cycleError');
            return Do.failure(const PeriodTrackingFailure.unexpected());
          }

          final periodUpdateResult = await _healthDataFacade.updatePeriodLength(
            metadata.averagePeriodLength?.round() ?? 5,
          );

          // Check if period length update failed
          final periodError = periodUpdateResult.fold(
            onFailure: (failure) => failure,
            onSuccess: (_) => null,
          );
          if (periodError != null) {
            debugPrint('❌ Failed to update period length: $periodError');
            return Do.failure(const PeriodTrackingFailure.unexpected());
          }

          debugPrint('✅ Successfully updated prediction metadata');
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('❌ Error updating prediction metadata: $e');
      return const Do.failure(PeriodTrackingFailure.unexpected());
    }
  }

  /// Detects new period starts from flow symptoms and triggers prediction updates
  Future<Do<PeriodTrackingFailure, Unit>> handleFlowSymptomUpdate(
    DateTime date,
    int? flowLevel,
    Map<String, Map<String, PeriodTrackingModel>> yearData,
  ) async {
    try {
      debugPrint('🔮 Handling flow symptom update for ${date.toString()}');
      debugPrint('🔮 Flow level: $flowLevel');

      if (flowLevel == null || flowLevel == 0) {
        debugPrint(
            'ℹ️ No flow or zero flow level - no prediction update needed');
        return const Do.success(unit);
      }

      // Check if this could be a new period start
      final isNewPeriodStart = await _isNewPeriodStart(date, yearData);

      if (isNewPeriodStart) {
        debugPrint('🔮 Detected new period start on ${date.toString()}');

        // Update prediction metadata with the new data
        final updateResult = await updatePredictionMetadata(yearData);

        return updateResult.fold(
          onFailure: (failure) {
            debugPrint(
                '❌ Failed to update metadata after new period start: $failure');
            return Do.failure(failure);
          },
          onSuccess: (_) {
            debugPrint(
                '✅ Successfully updated metadata after new period start');
            return const Do.success(unit);
          },
        );
      } else {
        debugPrint('ℹ️ Flow update does not indicate new period start');
        return const Do.success(unit);
      }
    } catch (e) {
      debugPrint('❌ Error handling flow symptom update: $e');
      return const Do.failure(PeriodTrackingFailure.unexpected());
    }
  }

  /// Generates future predictions based on current metadata
  Future<Do<PeriodTrackingFailure, Map<String, Set<DateTime>>>>
      generatePredictions({
    int monthsAhead = 6,
  }) async {
    try {
      debugPrint('🔮 Generating predictions for $monthsAhead months ahead');
      print('🔮 PredictionService: Starting prediction generation...');

      // Get current health data for prediction parameters
      print('🔮 PredictionService: Getting health data...');
      final healthDataResult = await _healthDataFacade.watchHealthData().first;
      print('🔮 PredictionService: Health data retrieved');

      return await healthDataResult.fold(
        onFailure: (failure) async {
          debugPrint('❌ Could not get health data for predictions: $failure');
          print('❌ PredictionService: Health data failure: $failure');
          return const Do.failure(PeriodTrackingFailure.unexpected());
        },
        onSuccess: (healthData) async {
          print('🔮 PredictionService: Health data success: $healthData');

          if (healthData == null) {
            debugPrint('⚠️ No health data available for predictions');
            print('⚠️ PredictionService: Health data is null');
            return const Do.failure(PeriodTrackingFailure.unexpected());
          }

          print('🔮 PredictionService: Getting existing period dates...');
          // Get all existing period dates to find the last period
          final allPeriodDates =
              await _periodDataService.getAllExistingPeriodDates();
          print(
              '🔮 PredictionService: Found ${allPeriodDates.length} existing period dates');

          if (allPeriodDates.isEmpty) {
            debugPrint('ℹ️ No existing period dates found for predictions');
            return const Do.success({
              'periods': <DateTime>{},
              'ovulations': <DateTime>{},
            });
          }

          // Find the most recent period start
          final sortedPeriodDates = allPeriodDates.toList()..sort();
          final lastPeriodDate = sortedPeriodDates.last;

          debugPrint('🔮 Last period date: $lastPeriodDate');
          debugPrint('🔮 Using cycle length: ${healthData.cycleLength}');
          debugPrint('🔮 Using period length: ${healthData.periodLength}');

          // Create metadata for prediction generation
          final metadata = PeriodPredictionMetadata.initial(
            initialCycleLength: healthData.cycleLength?.toDouble() ?? 28.0,
            initialPeriodLength: healthData.periodLength?.toDouble() ?? 5.0,
            lastPeriodDate: lastPeriodDate,
          );

          // Generate future predictions
          final predictions = PeriodPredictionEngine.generateFuturePredictions(
            metadata,
            lastPeriodDate,
            monthsAhead: monthsAhead,
          );

          debugPrint(
              '🔮 Generated ${predictions['periods']?.length ?? 0} period predictions');
          debugPrint(
              '🔮 Generated ${predictions['ovulations']?.length ?? 0} ovulation predictions');

          return Do.success(predictions);
        },
      );
    } catch (e) {
      debugPrint('❌ Error generating predictions: $e');
      return const Do.failure(PeriodTrackingFailure.unexpected());
    }
  }

  /// Initializes predictions from onboarding data
  Future<Do<PeriodTrackingFailure, Unit>> initializePredictionsFromOnboarding(
    DateTime lastPeriodDate,
    int cycleLength,
    int periodLength,
  ) async {
    try {
      debugPrint('🔮 Initializing predictions from onboarding data');
      debugPrint('🔮 Last period: $lastPeriodDate');
      debugPrint('🔮 Cycle length: $cycleLength');
      debugPrint('🔮 Period length: $periodLength');

      // Create initial prediction metadata (will be saved by account management)
      PeriodPredictionMetadata.initial(
        initialCycleLength: cycleLength.toDouble(),
        initialPeriodLength: periodLength.toDouble(),
        lastPeriodDate: lastPeriodDate,
      );

      // Generate initial predictions using the prediction engine
      final predictions =
          PeriodPredictionEngine.initializePredictionsFromOnboarding(
        lastPeriodDate,
        cycleLength,
        periodLength,
      );

      debugPrint('🔮 Successfully initialized predictions from onboarding:');
      debugPrint('🔮 Future periods: ${predictions['periods']?.length ?? 0}');
      debugPrint(
          '🔮 Future ovulations: ${predictions['ovulations']?.length ?? 0}');

      // Predictions will be calculated in real-time by PeriodTrackingWatcherBloc
      // No need to save to Firestore - they are displayed dynamically

      return const Do.success(unit);
    } catch (e) {
      debugPrint('❌ Error initializing predictions from onboarding: $e');
      return const Do.failure(PeriodTrackingFailure.unexpected());
    }
  }

  /// Check if a flow symptom update indicates a new period start
  Future<bool> _isNewPeriodStart(
    DateTime date,
    Map<String, Map<String, PeriodTrackingModel>> yearData,
  ) async {
    try {
      // Look at the previous 7 days to see if there was a gap in flow
      int consecutiveDaysWithoutFlow = 0;

      for (int i = 1; i <= 7; i++) {
        final checkDate = date.subtract(Duration(days: i));
        final monthKey =
            '${checkDate.year}_${checkDate.month.toString().padLeft(2, '0')}';
        final dayKey = checkDate.day.toString().padLeft(2, '0');

        final dayData = yearData[monthKey]?[dayKey];
        final flowLevel = dayData?.flowLevel ?? 0;

        if (flowLevel == 0) {
          consecutiveDaysWithoutFlow++;
        } else {
          break; // Found flow, stop counting
        }
      }

      debugPrint(
          '🔮 Found $consecutiveDaysWithoutFlow consecutive days without flow before $date');

      // If there were 2+ consecutive days without flow, this could be a new period start
      return consecutiveDaysWithoutFlow >= 2;
    } catch (e) {
      debugPrint('❌ Error checking if new period start: $e');
      return false;
    }
  }

  /// Convert year data to format expected by prediction engine
  Map<String, Map<String, PeriodTrackingModel>> _convertYearDataToEngineFormat(
    Map<String, Map<String, PeriodTrackingModel>> yearData,
  ) {
    // The year data is already in the correct format
    return yearData;
  }
}
