import 'package:doso/doso.dart';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import '../../domain/failure/period_tracking_failure.dart';
import '../../domain/model/period_tracking_model.dart';
import '../../domain/model/symptom_model.dart';
import 'firestore_service.dart';

/// Service for managing symptom and flow data
@injectable
class SymptomService {
  final FirestoreService _firestoreService;

  SymptomService(this._firestoreService);

  /// Get symptom data for a specific date
  Future<Do<PeriodTrackingFailure, PeriodTrackingModel?>> getSymptomData({
    required DateTime date,
  }) async {
    try {
      debugPrint('📊 Getting symptom data for ${date.toString()}');

      final result = await _firestoreService.getSymptomData(date: date);

      return result.fold(
        onFailure: (failure) {
          debugPrint('❌ Failed to get symptom data: $failure');
          return Do.failure(failure);
        },
        onSuccess: (model) {
          debugPrint('✅ Retrieved symptom data: ${model != null ? 'found' : 'not found'}');
          return Do.success(model);
        },
      );
    } catch (e) {
      debugPrint('❌ Error getting symptom data: $e');
      return const Do.failure(PeriodTrackingFailure.unexpected());
    }
  }

  /// Save symptom data for a specific date
  Future<Do<PeriodTrackingFailure, Unit>> saveSymptomData({
    required DateTime date,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
  }) async {
    try {
      debugPrint('📊 Saving symptom data for ${date.toString()}');
      debugPrint('📊 Symptoms: ${symptoms?.length ?? 0}, Pain: $painLevel, Flow: $flowLevel');

      // Prepare symptom data
      final symptomData = <String, dynamic>{};

      if (symptoms != null) {
        symptomData['symptoms'] = symptoms.map((s) => s.toJson()).toList();
      }

      if (painLevel != null) {
        symptomData['painLevel'] = painLevel;
      }

      if (flowLevel != null) {
        symptomData['flowLevel'] = flowLevel;
      }

      // Only save if there's actual data to save
      if (symptomData.isEmpty) {
        debugPrint('ℹ️ No symptom data to save');
        return const Do.success(unit);
      }

      final result = await _firestoreService.saveSymptomData(
        date: date,
        symptomData: symptomData,
      );

      return result.fold(
       onFailure: (failure) {
          debugPrint('❌ Failed to save symptom data: $failure');
          return Do.failure(failure);
        },
        onSuccess: (_) {
          debugPrint('✅ Successfully saved symptom data');
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('❌ Error saving symptom data: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Save only flow level for a specific date
  Future<Do<PeriodTrackingFailure, Unit>> saveFlowLevel({
    required DateTime date,
    required int flowLevel,
  }) async {
    try {
      debugPrint('🩸 Saving flow level $flowLevel for ${date.toString()}');

      final result = await saveSymptomData(
        date: date,
        flowLevel: flowLevel,
      );

      return result.fold(
       onFailure: (failure) {
          debugPrint('❌ Failed to save flow level: $failure');
          return Do.failure(failure);
        },
        onSuccess: (_) {
          debugPrint('✅ Successfully saved flow level');
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('❌ Error saving flow level: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Save only pain level for a specific date
  Future<Do<PeriodTrackingFailure, Unit>> savePainLevel({
    required DateTime date,
    required int painLevel,
  }) async {
    try {
      debugPrint('😣 Saving pain level $painLevel for ${date.toString()}');

      final result = await saveSymptomData(
        date: date,
        painLevel: painLevel,
      );

      return result.fold(
       onFailure: (failure) {
          debugPrint('❌ Failed to save pain level: $failure');
          return Do.failure(failure);
        },
        onSuccess: (_) {
          debugPrint('✅ Successfully saved pain level');
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('❌ Error saving pain level: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Save symptoms for a specific date
  Future<Do<PeriodTrackingFailure, Unit>> saveSymptoms({
    required DateTime date,
    required List<SymptomModel> symptoms,
  }) async {
    try {
      debugPrint('🔍 Saving ${symptoms.length} symptoms for ${date.toString()}');

      final result = await saveSymptomData(
        date: date,
        symptoms: symptoms,
      );

      return result.fold(
       onFailure: (failure) {
          debugPrint('❌ Failed to save symptoms: $failure');
          return Do.failure(failure);
        },
        onSuccess: (_) {
          debugPrint('✅ Successfully saved symptoms');
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('❌ Error saving symptoms: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Update existing symptom data by merging with new data
  Future<Do<PeriodTrackingFailure, Unit>> updateSymptomData({
    required DateTime date,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
  }) async {
    try {
      debugPrint('🔄 Updating symptom data for ${date.toString()}');

      // First, get existing data
      final existingResult = await getSymptomData(date: date);

      return await existingResult.fold(
        onFailure: (failure) async => Do.failure(failure),
      onSuccess:   (existingModel) async {
          // Merge with existing data
          final updatedSymptoms = symptoms ?? existingModel?.symptoms;
          final updatedPainLevel = painLevel ?? existingModel?.painLevel;
          final updatedFlowLevel = flowLevel ?? existingModel?.flowLevel;

          // Save the merged data
          return await saveSymptomData(
            date: date,
            symptoms: updatedSymptoms,
            painLevel: updatedPainLevel,
            flowLevel: updatedFlowLevel,
          );
        },
      );
    } catch (e) {
      debugPrint('❌ Error updating symptom data: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Get flow level for a specific date
  Future<int?> getFlowLevel(DateTime date) async {
    try {
      final result = await getSymptomData(date: date);
      
      return result.fold(
       onFailure:  (failure) {
          debugPrint('❌ Failed to get flow level: $failure');
          return null;
        },
       onSuccess:  (model) => model?.flowLevel,
      );
    } catch (e) {
      debugPrint('❌ Error getting flow level: $e');
      return null;
    }
  }

  /// Get pain level for a specific date
  Future<int?> getPainLevel(DateTime date) async {
    try {
      final result = await getSymptomData(date: date);
      
      return result.fold(
       onFailure:  (failure) {
          debugPrint('❌ Failed to get pain level: $failure');
          return null;
        },
       onSuccess:  (model) => model?.painLevel,
      );
    } catch (e) {
      debugPrint('❌ Error getting pain level: $e');
      return null;
    }
  }

  /// Get symptoms for a specific date
  Future<List<SymptomModel>?> getSymptoms(DateTime date) async {
    try {
      final result = await getSymptomData(date: date);
      
      return result.fold(
       onFailure:  (failure) {
          debugPrint('❌ Failed to get symptoms: $failure');
          return null;
        },
       onSuccess:  (model) => model?.symptoms,
      );
    } catch (e) {
      debugPrint('❌ Error getting symptoms: $e');
      return null;
    }
  }

  /// Check if a date has any symptom data
  Future<bool> hasSymptomData(DateTime date) async {
    try {
      final result = await getSymptomData(date: date);
      
      return result.fold(
       onFailure:  (failure) => false,
       onSuccess:  (model) => model != null && (
          (model.symptoms?.isNotEmpty ?? false) ||
          model.painLevel != null ||
          model.flowLevel != null
        ),
      );
    } catch (e) {
      debugPrint('❌ Error checking symptom data: $e');
      return false;
    }
  }

  /// Delete symptom data for a specific date
  Future<Do<PeriodTrackingFailure, Unit>> deleteSymptomData({
    required DateTime date,
  }) async {
    try {
      debugPrint('🗑️ Deleting symptom data for ${date.toString()}');

      // Save empty data to effectively clear the symptoms
      final result = await _firestoreService.saveSymptomData(
        date: date,
        symptomData: {
          'symptoms': null,
          'painLevel': null,
          'flowLevel': null,
        },
      );

      return result.fold(
       onFailure: (failure) {
          debugPrint('❌ Failed to delete symptom data: $failure');
          return Do.failure(failure);
        },
        onSuccess: (_) {
          debugPrint('✅ Successfully deleted symptom data');
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('❌ Error deleting symptom data: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }
}
