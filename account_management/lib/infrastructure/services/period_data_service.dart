import 'package:doso/doso.dart';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import '../../domain/failure/period_tracking_failure.dart';
import '../../domain/model/period_tracking_model.dart';
import 'firestore_service.dart';

/// Service for managing period date selection and deselection
@injectable
class PeriodDataService {
  final FirestoreService _firestoreService;

  PeriodDataService(this._firestoreService);

  /// Select period dates in Firestore (sets isPeriodDate: true) with optional flow levels
  Future<Do<PeriodTrackingFailure, Unit>> selectPeriodDates(
    Set<DateTime> selectedDates, {
    Map<DateTime, int>? flowLevels,
  }) async {
    try {
      if (selectedDates.isEmpty) {
        return const Do.success(unit);
      }

      debugPrint(
          '📅 Selecting ${selectedDates.length} period dates: $selectedDates');
      if (flowLevels != null && flowLevels.isNotEmpty) {
        debugPrint(
            '💧 With flow levels for ${flowLevels.length} dates: $flowLevels');
      }

      // Group dates by month for efficient updates
      final monthlyUpdates = _groupDatesByMonth(selectedDates);

      final result = await _firestoreService.batchUpdatePeriodDates(
        monthlyUpdates: monthlyUpdates,
        isPeriodDate: true,
        flowLevels: flowLevels,
      );

      return result.fold(
        onFailure: (failure) {
          debugPrint('❌ Failed to select period dates: $failure');
          return Do.failure(failure);
        },
        onSuccess: (_) {
          debugPrint(
              '✅ Successfully selected ${selectedDates.length} period dates');
          if (flowLevels != null && flowLevels.isNotEmpty) {
            debugPrint(
                '✅ Successfully saved flow levels for ${flowLevels.length} dates');
          }
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('❌ Error selecting period dates: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Deselect period dates in Firestore (sets isPeriodDate: false)
  Future<Do<PeriodTrackingFailure, Unit>> deselectPeriodDates(
      Set<DateTime> datesToDeselect) async {
    try {
      if (datesToDeselect.isEmpty) {
        return const Do.success(unit);
      }

      debugPrint(
          '📅 Deselecting ${datesToDeselect.length} period dates: $datesToDeselect');

      // Group dates by month for efficient updates
      final monthlyUpdates = _groupDatesByMonth(datesToDeselect);

      final result = await _firestoreService.batchUpdatePeriodDates(
        monthlyUpdates: monthlyUpdates,
        isPeriodDate: false,
      );

      return result.fold(
        onFailure: (failure) {
          debugPrint('❌ Failed to deselect period dates: $failure');
          return Do.failure(failure);
        },
        onSuccess: (_) {
          debugPrint(
              '✅ Successfully deselected ${datesToDeselect.length} period dates');
          return Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('❌ Error deselecting period dates: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Get all existing period dates from Firestore
  Future<Set<DateTime>> getAllExistingPeriodDates() async {
    try {
      final user = _firestoreService.currentUser;
      if (user == null) return {};

      final allDates = <DateTime>{};
      final now = DateTime.now();

      // Look back 2 years and forward 1 year for comprehensive data
      final startYear = now.year - 2;
      final endYear = now.year + 1;

      for (int year = startYear; year <= endYear; year++) {
        final yearDataResult =
            await _firestoreService.watchYearData(year).first;

        yearDataResult.fold(
          onFailure: (failure) {
            debugPrint('Failed to get year data for $year: $failure');
          },
          onSuccess: (yearData) {
            for (final monthEntry in yearData.entries) {
              for (final dayEntry in monthEntry.value.entries) {
                final model = dayEntry.value;
                if (model.isPeriodDate == true && model.date != null) {
                  final normalizedDate = DateTime(
                    model.date!.year,
                    model.date!.month,
                    model.date!.day,
                  );
                  allDates.add(normalizedDate);
                }
              }
            }
          },
        );
      }

      debugPrint('📅 Found ${allDates.length} existing period dates');
      return allDates;
    } catch (e) {
      debugPrint('❌ Error getting all existing period dates: $e');
      return {};
    }
  }

  /// Calculate period cycles from period dates
  List<List<DateTime>> calculatePeriodCycles(Set<DateTime> periodDates) {
    if (periodDates.isEmpty) return [];

    final sortedDates = periodDates.toList()..sort();
    final cycles = <List<DateTime>>[];
    var currentCycle = <DateTime>[];

    for (int i = 0; i < sortedDates.length; i++) {
      final currentDate = sortedDates[i];

      if (currentCycle.isEmpty) {
        currentCycle.add(currentDate);
      } else {
        final lastDate = currentCycle.last;
        final daysDifference = currentDate.difference(lastDate).inDays;

        if (daysDifference <= 2) {
          // Consecutive day or single-day gap - add to current cycle
          currentCycle.add(currentDate);
        } else {
          // Gap of 2+ days found - end current cycle and start new one
          cycles.add(List.from(currentCycle));
          currentCycle = [currentDate];
        }
      }
    }

    // Add the last cycle
    if (currentCycle.isNotEmpty) {
      cycles.add(currentCycle);
    }

    debugPrint(
        '📊 Calculated ${cycles.length} period cycles from ${periodDates.length} dates');
    return cycles;
  }

  /// Check if a cycle is valid for ovulation calculation
  bool isValidCycleForOvulation(
    List<DateTime> currentCycle,
    List<List<DateTime>> allCycles,
    int cycleIndex,
    int userCycleLength,
  ) {
    if (currentCycle.isEmpty) return false;

    // Must have at least 1 day
    if (currentCycle.length < 1) return false;

    // If there's a next cycle, check the gap
    if (cycleIndex + 1 < allCycles.length) {
      final nextCycle = allCycles[cycleIndex + 1];
      final currentCycleEnd = currentCycle.last;
      final nextCycleStart = nextCycle.first;
      final gapDays = nextCycleStart.difference(currentCycleEnd).inDays;

      // Gap should be reasonable (between 7 and 45 days)
      // This allows for cycle lengths from 14 to 50 days
      if (gapDays < 7 || gapDays > 45) {
        debugPrint('⚠️ Invalid cycle gap: $gapDays days between cycles');
        return false;
      }

      // Gap should be somewhat close to user's cycle length (±10 days tolerance)
      final expectedGap = userCycleLength - currentCycle.length;
      if ((gapDays - expectedGap).abs() > 10) {
        debugPrint(
            '⚠️ Cycle gap ($gapDays) differs significantly from expected ($expectedGap)');
        return false;
      }
    }

    return true;
  }

  /// Group dates by month for efficient Firestore operations
  Map<String, Map<String, Set<String>>> _groupDatesByMonth(
      Set<DateTime> dates) {
    final monthlyUpdates = <String, Map<String, Set<String>>>{};

    for (final date in dates) {
      final year = date.year.toString();
      final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
      final dayKey = date.day.toString().padLeft(2, '0');

      monthlyUpdates[year] ??= {};
      monthlyUpdates[year]![monthKey] ??= {};
      monthlyUpdates[year]![monthKey]!.add(dayKey);
    }

    return monthlyUpdates;
  }

  /// Extract period dates from year data
  Set<DateTime> extractPeriodDatesFromYearData(
      Map<String, Map<String, PeriodTrackingModel>> yearData) {
    final periodDates = <DateTime>{};

    for (final monthEntry in yearData.entries) {
      for (final dayEntry in monthEntry.value.entries) {
        final model = dayEntry.value;
        if (model.isPeriodDate == true && model.date != null) {
          final normalizedDate = DateTime(
            model.date!.year,
            model.date!.month,
            model.date!.day,
          );
          periodDates.add(normalizedDate);
        }
      }
    }

    return periodDates;
  }

  /// Extract ovulation dates from year data
  Set<DateTime> extractOvulationDatesFromYearData(
      Map<String, Map<String, PeriodTrackingModel>> yearData) {
    final ovulationDates = <DateTime>{};

    for (final monthEntry in yearData.entries) {
      for (final dayEntry in monthEntry.value.entries) {
        final model = dayEntry.value;
        if (model.isOvulationDate == true && model.date != null) {
          final normalizedDate = DateTime(
            model.date!.year,
            model.date!.month,
            model.date!.day,
          );
          ovulationDates.add(normalizedDate);
        }
      }
    }

    return ovulationDates;
  }

  /// Get all existing period dates from Firestore
  Future<Set<DateTime>> getAllPeriodDates() async {
    try {
      debugPrint('🔍 PeriodDataService.getAllPeriodDates called');

      // Get data for multiple years to ensure we capture all period dates
      final currentYear = DateTime.now().year;
      final Set<DateTime> allPeriodDates = {};

      // Check current year and previous year
      for (int year = currentYear - 1; year <= currentYear + 1; year++) {
        final yearData = await _firestoreService.watchYearData(year).first;

        yearData.fold(
          onFailure: (failure) {
            debugPrint(
                '⚠️ Failed to get period dates for year $year: $failure');
          },
          onSuccess: (monthsData) {
            for (final monthData in monthsData.values) {
              for (final dayData in monthData.values) {
                if (dayData.isPeriodDate == true && dayData.date != null) {
                  final normalizedDate = DateTime(
                    dayData.date!.year,
                    dayData.date!.month,
                    dayData.date!.day,
                  );
                  allPeriodDates.add(normalizedDate);
                }
              }
            }
          },
        );
      }

      debugPrint('🔍 Found ${allPeriodDates.length} total period dates');
      return allPeriodDates;
    } catch (e) {
      debugPrint('❌ Error getting all period dates: $e');
      return <DateTime>{};
    }
  }
}
