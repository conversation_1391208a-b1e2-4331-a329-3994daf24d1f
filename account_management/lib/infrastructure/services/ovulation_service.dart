import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import '../../domain/core/unit.dart';
import '../../domain/facade/health_data_facade.dart';
import '../../domain/failure/period_tracking_failure.dart';
import 'firestore_service.dart';
import 'package:doso/doso.dart';
import 'period_data_service.dart';

/// Service for managing ovulation date calculations and operations
@injectable
class OvulationService {
  final FirestoreService _firestoreService;
  final PeriodDataService _periodDataService;
  final HealthDataFacade _healthDataFacade;

  OvulationService(
    this._firestoreService,
    this._periodDataService,
    this._healthDataFacade,
  );

  /// Calculate and save ovulation dates based on period cycles
  Future<Object> calculateAndSaveOvulationDates(
      Set<DateTime> periodDates) async {
    try {
      if (periodDates.isEmpty) {
        debugPrint('🥚 No period dates provided for ovulation calculation');
        return const Do.success(unit);
      }

      debugPrint(
          '🥚 Calculating ovulation dates for ${periodDates.length} period dates');

      // Get user's health data for cycle and period length
      final healthDataResult = await _healthDataFacade.watchHealthData().first;
      int cycleLength = 28; // Default cycle length
      int periodLength = 5; // Default period length

      healthDataResult.fold(
        onFailure: (failure) {
          debugPrint(
              '⚠️ Using default cycle/period lengths due to health data failure: $failure');
        },
        onSuccess: (healthData) {
          if (healthData != null) {
            cycleLength = healthData.cycleLength ?? 28;
            periodLength = healthData.periodLength ?? 5;
          }
        },
      );

      // Calculate new ovulation dates for valid period cycles
      final newOvulationDates =
          _calculateOvulationDates(periodDates, cycleLength, periodLength);

      debugPrint(
          '🥚 Calculated ${newOvulationDates.length} new ovulation dates');

      // Only clear ovulation dates that would conflict with the new ones
      // Get existing ovulation dates only for the specific dates we're about to set
      final conflictingOvulationDates = <DateTime>{};
      if (newOvulationDates.isNotEmpty) {
        for (final newOvulationDate in newOvulationDates) {
          // Check if there's already an ovulation date on this specific day
          final existingOnThisDate =
              await _firestoreService.getExistingOvulationDatesInRange(
                  newOvulationDate,
                  newOvulationDate.add(const Duration(days: 1)));
          conflictingOvulationDates.addAll(existingOnThisDate);
        }
      }

      debugPrint(
          '🥚 Found ${conflictingOvulationDates.length} conflicting ovulation dates to clear');

      // Clear only conflicting ovulation dates
      if (conflictingOvulationDates.isNotEmpty) {
        final clearResult = await _firestoreService.batchUpdateOvulationDates(
          ovulationDates: conflictingOvulationDates,
          isOvulationDate: false,
        );

        // If clearing failed, return the error
        final clearError = clearResult.fold(
          onFailure: (failure) => failure,
          onSuccess: (_) => null,
        );

        if (clearError != null) {
          debugPrint(
              '❌ Failed to clear conflicting ovulation dates: $clearError');
          return Do.failure(clearError);
        }
      }

      // Set new ovulation dates
      if (newOvulationDates.isNotEmpty) {
        return await _firestoreService.batchUpdateOvulationDates(
          ovulationDates: newOvulationDates,
          isOvulationDate: true,
        );
      }

      debugPrint('✅ Successfully updated ovulation dates');
      return const Do.success(unit);
    } catch (e) {
      debugPrint('❌ Error calculating and saving ovulation dates: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Calculate ovulation for affected cycles only
  Future<Do<PeriodTrackingFailure, Unit>> calculateOvulationForAffectedCycles({
    required Set<DateTime> newlySelected,
    required Set<DateTime> newlyDeselected,
  }) async {
    try {
      debugPrint('🥚 Calculating ovulation for affected cycles...');
      debugPrint('🥚 Newly selected: ${newlySelected.length} dates');
      debugPrint('🥚 Newly deselected: ${newlyDeselected.length} dates');

      // Get user's health data for cycle and period length
      final healthDataResult = await _healthDataFacade.watchHealthData().first;
      int cycleLength = 28;
      int periodLength = 5;

      healthDataResult.fold(
        onFailure: (failure) {
          debugPrint('⚠️ Using default cycle/period lengths: $failure');
        },
        onSuccess: (healthData) {
          if (healthData != null) {
            cycleLength = healthData.cycleLength ?? 28;
            periodLength = healthData.periodLength ?? 5;
          }
        },
      );

      // Get all existing period dates to understand the full context
      final allPeriodDates =
          await _periodDataService.getAllExistingPeriodDates();

      // Calculate which dates are affected by the changes
      final affectedDates = _calculateAffectedDates(
        allPeriodDates,
        newlySelected,
        newlyDeselected,
        cycleLength,
      );

      debugPrint('🥚 Found ${affectedDates.length} affected dates');

      // Calculate ovulation for affected cycles only
      final newOvulationDates =
          _calculateOvulationDates(affectedDates, cycleLength, periodLength);

      // Clear existing ovulation dates that could be calculated from affected cycles
      final existingOvulationDates =
          await _getOvulationDatesForAffectedCycles(affectedDates, cycleLength);

      debugPrint(
          '🥚 Clearing ${existingOvulationDates.length} existing ovulation dates');
      debugPrint('🥚 Setting ${newOvulationDates.length} new ovulation dates');

      // Clear existing ovulation dates
      if (existingOvulationDates.isNotEmpty) {
        final clearResult = await _firestoreService.batchUpdateOvulationDates(
          ovulationDates: existingOvulationDates,
          isOvulationDate: false,
        );

        if (clearResult.isFailure) {
          return clearResult;
        }
      }

      // Set new ovulation dates
      if (newOvulationDates.isNotEmpty) {
        final setResult = await _firestoreService.batchUpdateOvulationDates(
          ovulationDates: newOvulationDates,
          isOvulationDate: true,
        );

        if (setResult.isFailure) {
          return setResult;
        }
      }

      debugPrint('✅ Successfully updated ovulation for affected cycles');
      return const Do.success(unit);
    } catch (e) {
      debugPrint('❌ Error calculating ovulation for affected cycles: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Remove ovulation dates for specific period cycles
  Future<Do<PeriodTrackingFailure, Unit>> removeOvulationDatesForCycles(
      Set<DateTime> periodDatesToRemove) async {
    try {
      if (periodDatesToRemove.isEmpty) {
        return const Do.success(unit);
      }

      debugPrint(
          '🗑️ Removing ovulation dates for ${periodDatesToRemove.length} period dates');

      // Get user's health data for cycle length
      final healthDataResult = await _healthDataFacade.watchHealthData().first;
      int cycleLength = 28;

      healthDataResult.fold(
        onFailure: (failure) {
          debugPrint('⚠️ Using default cycle length: $failure');
        },
        onSuccess: (healthData) {
          if (healthData != null) {
            cycleLength = healthData.cycleLength ?? 28;
          }
        },
      );

      // Calculate a wider potential ovulation range for the deselected period dates
      final potentialOvulationRange =
          _calculateWiderOvulationRange(periodDatesToRemove, cycleLength);

      debugPrint(
          '🔍 Searching for ovulation dates to remove in range: ${potentialOvulationRange.start} to ${potentialOvulationRange.end}');

      // Get existing ovulation dates in that range
      final ovulationDatesToRemove =
          await _firestoreService.getExistingOvulationDatesInRange(
              potentialOvulationRange.start, potentialOvulationRange.end);

      debugPrint(
          '🔍 Found ${ovulationDatesToRemove.length} ovulation dates to remove');

      if (ovulationDatesToRemove.isEmpty) {
        debugPrint('ℹ️ No ovulation dates to remove');
        return const Do.success(unit);
      }

      // Remove the ovulation dates
      final result = await _firestoreService.batchUpdateOvulationDates(
        ovulationDates: ovulationDatesToRemove,
        isOvulationDate: false,
      );

      return result.fold(
        onFailure: (failure) {
          debugPrint('❌ Failed to remove ovulation dates: $failure');
          return Do.failure(failure);
        },
        onSuccess: (_) {
          debugPrint(
              '✅ Successfully removed ${ovulationDatesToRemove.length} ovulation dates');
          return const Do.success(unit);
        },
      );
    } catch (e) {
      debugPrint('❌ Error removing ovulation dates for cycles: $e');
      return const Do.failure(PeriodTrackingFailure.createFailure());
    }
  }

  /// Calculate ovulation dates for valid period cycles
  Set<DateTime> _calculateOvulationDates(
      Set<DateTime> periodDates, int cycleLength, int periodLength) {
    if (periodDates.isEmpty) return {};

    final ovulationDates = <DateTime>{};
    final periodCycles = _periodDataService.calculatePeriodCycles(periodDates);

    debugPrint(
        '🥚 Processing ${periodCycles.length} period cycles for ovulation calculation');

    for (int i = 0; i < periodCycles.length; i++) {
      final currentCycle = periodCycles[i];

      // Check if cycle is valid for ovulation calculation
      if (!_periodDataService.isValidCycleForOvulation(
          currentCycle, periodCycles, i, cycleLength)) {
        debugPrint(
            '⚠️ Skipping invalid cycle: ${currentCycle.first} to ${currentCycle.last}');
        continue;
      }

      final cycleStartDate = currentCycle.first;
      DateTime? nextCycleStartDate;

      // Find next cycle start date
      if (i + 1 < periodCycles.length) {
        nextCycleStartDate = periodCycles[i + 1].first;
      } else {
        // For the last cycle, estimate based on user's cycle length
        nextCycleStartDate = cycleStartDate.add(Duration(days: cycleLength));
      }

      // Normalize dates to midnight to avoid timezone issues
      final normalizedCycleStart = DateTime(
          cycleStartDate.year, cycleStartDate.month, cycleStartDate.day);
      final normalizedNextCycleStart = DateTime(nextCycleStartDate.year,
          nextCycleStartDate.month, nextCycleStartDate.day);

      // Calculate ovulation window (5 days: 2 days before peak + peak + 2 days after)
      final ovulationPeakDate =
          normalizedNextCycleStart.subtract(const Duration(days: 14));

      // Only add if ovulation peak date is reasonable (not in the period cycle itself)
      final periodEndDate =
          normalizedCycleStart.add(Duration(days: periodLength));
      if (ovulationPeakDate.isAfter(periodEndDate)) {
        // Create 5-day ovulation window: 2 days before peak, peak day, 2 days after peak
        for (int j = -2; j <= 2; j++) {
          final ovulationDate = ovulationPeakDate.add(Duration(days: j));

          // Ensure ovulation date is not in the period cycle itself
          if (ovulationDate.isAfter(periodEndDate)) {
            // Normalize ovulation date to midnight
            final normalizedOvulationDate = DateTime(
                ovulationDate.year, ovulationDate.month, ovulationDate.day);
            ovulationDates.add(normalizedOvulationDate);
          }
        }
      }
    }

    debugPrint('🥚 Calculated ${ovulationDates.length} ovulation dates');
    return ovulationDates;
  }

  /// Calculate potential ovulation date range based on user's cycle length
  ({DateTime start, DateTime end}) _calculatePotentialOvulationRange(
      Set<DateTime> periodDates, int cycleLength) {
    if (periodDates.isEmpty) {
      final now = DateTime.now();
      return (start: now, end: now);
    }

    final earliestPeriod = periodDates.reduce((a, b) => a.isBefore(b) ? a : b);
    final latestPeriod = periodDates.reduce((a, b) => a.isAfter(b) ? a : b);

    // Ovulation can occur 7 days after period start to cycle length days after period start
    final rangeStart = earliestPeriod.add(const Duration(days: 7));
    final rangeEnd = latestPeriod.add(Duration(days: cycleLength));

    return (start: rangeStart, end: rangeEnd);
  }

  /// Calculate a wider ovulation range for more aggressive removal
  ({DateTime start, DateTime end}) _calculateWiderOvulationRange(
      Set<DateTime> periodDates, int cycleLength) {
    if (periodDates.isEmpty) {
      final now = DateTime.now();
      return (start: now, end: now);
    }

    final earliestPeriod = periodDates.reduce((a, b) => a.isBefore(b) ? a : b);
    final latestPeriod = periodDates.reduce((a, b) => a.isAfter(b) ? a : b);

    // Use a wider range to catch all potentially related ovulation dates
    // Look from 5 days before the earliest period to 1.5 cycle lengths after the latest period
    final rangeStart = earliestPeriod.subtract(const Duration(days: 5));
    final rangeEnd =
        latestPeriod.add(Duration(days: (cycleLength * 1.5).round()));

    return (start: rangeStart, end: rangeEnd);
  }

  /// Calculate which dates are affected by period date changes
  Set<DateTime> _calculateAffectedDates(
    Set<DateTime> allPeriodDates,
    Set<DateTime> newlySelected,
    Set<DateTime> newlyDeselected,
    int cycleLength,
  ) {
    // Start with all changed dates
    final affectedDates = <DateTime>{};
    affectedDates.addAll(newlySelected);
    affectedDates.addAll(newlyDeselected);

    // Calculate period cycles to understand which cycles are affected
    final periodCycles =
        _periodDataService.calculatePeriodCycles(allPeriodDates);
    final affectedCycleIndices = <int>{};

    // Find which cycles contain the changed dates
    for (int i = 0; i < periodCycles.length; i++) {
      final cycle = periodCycles[i];
      for (final changedDate in [...newlySelected, ...newlyDeselected]) {
        if (cycle.any((cycleDate) => _isSameDate(cycleDate, changedDate))) {
          affectedCycleIndices.add(i);
          break;
        }
      }
    }

    // Add all dates from affected cycles
    for (final cycleIndex in affectedCycleIndices) {
      affectedDates.addAll(periodCycles[cycleIndex]);
    }

    return affectedDates;
  }

  /// Get existing ovulation dates that could be calculated from affected cycles
  Future<Set<DateTime>> _getOvulationDatesForAffectedCycles(
      Set<DateTime> affectedDates, int cycleLength) async {
    if (affectedDates.isEmpty) return {};

    // Calculate what ovulation dates COULD be generated from these cycles
    final potentialOvulationDates =
        _calculateOvulationDates(affectedDates, cycleLength, 5);

    // Only get existing ovulation dates for the exact dates we're about to set
    final existingOvulationDates = <DateTime>{};
    for (final potentialDate in potentialOvulationDates) {
      final existingOnThisDate =
          await _firestoreService.getExistingOvulationDatesInRange(
              potentialDate, potentialDate.add(const Duration(days: 1)));
      existingOvulationDates.addAll(existingOnThisDate);
    }

    debugPrint(
        '🥚 Found ${existingOvulationDates.length} existing ovulation dates for affected cycles');
    return existingOvulationDates;
  }

  /// Check if two dates are the same (ignoring time)
  bool _isSameDate(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
