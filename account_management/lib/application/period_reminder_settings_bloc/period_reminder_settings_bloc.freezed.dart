// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'period_reminder_settings_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PeriodReminderSettingsEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PeriodReminderSettingsEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodReminderSettingsEvent()';
  }
}

/// @nodoc
class $PeriodReminderSettingsEventCopyWith<$Res> {
  $PeriodReminderSettingsEventCopyWith(PeriodReminderSettingsEvent _,
      $Res Function(PeriodReminderSettingsEvent) __);
}

/// Adds pattern-matching-related methods to [PeriodReminderSettingsEvent].
extension PeriodReminderSettingsEventPatterns on PeriodReminderSettingsEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSettings value)? loadSettings,
    TResult Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult Function(_UpdatePeriodReminderDays value)? updatePeriodReminderDays,
    TResult Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult Function(_SaveSettings value)? saveSettings,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LoadSettings() when loadSettings != null:
        return loadSettings(_that);
      case _TogglePeriodReminder() when togglePeriodReminder != null:
        return togglePeriodReminder(_that);
      case _UpdatePeriodReminderDays() when updatePeriodReminderDays != null:
        return updatePeriodReminderDays(_that);
      case _ToggleOvulationReminder() when toggleOvulationReminder != null:
        return toggleOvulationReminder(_that);
      case _UpdateOvulationReminderDays()
          when updateOvulationReminderDays != null:
        return updateOvulationReminderDays(_that);
      case _SaveSettings() when saveSettings != null:
        return saveSettings(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSettings value) loadSettings,
    required TResult Function(_TogglePeriodReminder value) togglePeriodReminder,
    required TResult Function(_UpdatePeriodReminderDays value)
        updatePeriodReminderDays,
    required TResult Function(_ToggleOvulationReminder value)
        toggleOvulationReminder,
    required TResult Function(_UpdateOvulationReminderDays value)
        updateOvulationReminderDays,
    required TResult Function(_SaveSettings value) saveSettings,
  }) {
    final _that = this;
    switch (_that) {
      case _LoadSettings():
        return loadSettings(_that);
      case _TogglePeriodReminder():
        return togglePeriodReminder(_that);
      case _UpdatePeriodReminderDays():
        return updatePeriodReminderDays(_that);
      case _ToggleOvulationReminder():
        return toggleOvulationReminder(_that);
      case _UpdateOvulationReminderDays():
        return updateOvulationReminderDays(_that);
      case _SaveSettings():
        return saveSettings(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSettings value)? loadSettings,
    TResult? Function(_TogglePeriodReminder value)? togglePeriodReminder,
    TResult? Function(_UpdatePeriodReminderDays value)?
        updatePeriodReminderDays,
    TResult? Function(_ToggleOvulationReminder value)? toggleOvulationReminder,
    TResult? Function(_UpdateOvulationReminderDays value)?
        updateOvulationReminderDays,
    TResult? Function(_SaveSettings value)? saveSettings,
  }) {
    final _that = this;
    switch (_that) {
      case _LoadSettings() when loadSettings != null:
        return loadSettings(_that);
      case _TogglePeriodReminder() when togglePeriodReminder != null:
        return togglePeriodReminder(_that);
      case _UpdatePeriodReminderDays() when updatePeriodReminderDays != null:
        return updatePeriodReminderDays(_that);
      case _ToggleOvulationReminder() when toggleOvulationReminder != null:
        return toggleOvulationReminder(_that);
      case _UpdateOvulationReminderDays()
          when updateOvulationReminderDays != null:
        return updateOvulationReminderDays(_that);
      case _SaveSettings() when saveSettings != null:
        return saveSettings(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadSettings,
    TResult Function(bool enabled)? togglePeriodReminder,
    TResult Function(int daysBefore)? updatePeriodReminderDays,
    TResult Function(bool enabled)? toggleOvulationReminder,
    TResult Function(int daysBefore)? updateOvulationReminderDays,
    TResult Function()? saveSettings,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LoadSettings() when loadSettings != null:
        return loadSettings();
      case _TogglePeriodReminder() when togglePeriodReminder != null:
        return togglePeriodReminder(_that.enabled);
      case _UpdatePeriodReminderDays() when updatePeriodReminderDays != null:
        return updatePeriodReminderDays(_that.daysBefore);
      case _ToggleOvulationReminder() when toggleOvulationReminder != null:
        return toggleOvulationReminder(_that.enabled);
      case _UpdateOvulationReminderDays()
          when updateOvulationReminderDays != null:
        return updateOvulationReminderDays(_that.daysBefore);
      case _SaveSettings() when saveSettings != null:
        return saveSettings();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadSettings,
    required TResult Function(bool enabled) togglePeriodReminder,
    required TResult Function(int daysBefore) updatePeriodReminderDays,
    required TResult Function(bool enabled) toggleOvulationReminder,
    required TResult Function(int daysBefore) updateOvulationReminderDays,
    required TResult Function() saveSettings,
  }) {
    final _that = this;
    switch (_that) {
      case _LoadSettings():
        return loadSettings();
      case _TogglePeriodReminder():
        return togglePeriodReminder(_that.enabled);
      case _UpdatePeriodReminderDays():
        return updatePeriodReminderDays(_that.daysBefore);
      case _ToggleOvulationReminder():
        return toggleOvulationReminder(_that.enabled);
      case _UpdateOvulationReminderDays():
        return updateOvulationReminderDays(_that.daysBefore);
      case _SaveSettings():
        return saveSettings();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadSettings,
    TResult? Function(bool enabled)? togglePeriodReminder,
    TResult? Function(int daysBefore)? updatePeriodReminderDays,
    TResult? Function(bool enabled)? toggleOvulationReminder,
    TResult? Function(int daysBefore)? updateOvulationReminderDays,
    TResult? Function()? saveSettings,
  }) {
    final _that = this;
    switch (_that) {
      case _LoadSettings() when loadSettings != null:
        return loadSettings();
      case _TogglePeriodReminder() when togglePeriodReminder != null:
        return togglePeriodReminder(_that.enabled);
      case _UpdatePeriodReminderDays() when updatePeriodReminderDays != null:
        return updatePeriodReminderDays(_that.daysBefore);
      case _ToggleOvulationReminder() when toggleOvulationReminder != null:
        return toggleOvulationReminder(_that.enabled);
      case _UpdateOvulationReminderDays()
          when updateOvulationReminderDays != null:
        return updateOvulationReminderDays(_that.daysBefore);
      case _SaveSettings() when saveSettings != null:
        return saveSettings();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _LoadSettings implements PeriodReminderSettingsEvent {
  const _LoadSettings();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _LoadSettings);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodReminderSettingsEvent.loadSettings()';
  }
}

/// @nodoc

class _TogglePeriodReminder implements PeriodReminderSettingsEvent {
  const _TogglePeriodReminder(this.enabled);

  final bool enabled;

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TogglePeriodReminderCopyWith<_TogglePeriodReminder> get copyWith =>
      __$TogglePeriodReminderCopyWithImpl<_TogglePeriodReminder>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TogglePeriodReminder &&
            (identical(other.enabled, enabled) || other.enabled == enabled));
  }

  @override
  int get hashCode => Object.hash(runtimeType, enabled);

  @override
  String toString() {
    return 'PeriodReminderSettingsEvent.togglePeriodReminder(enabled: $enabled)';
  }
}

/// @nodoc
abstract mixin class _$TogglePeriodReminderCopyWith<$Res>
    implements $PeriodReminderSettingsEventCopyWith<$Res> {
  factory _$TogglePeriodReminderCopyWith(_TogglePeriodReminder value,
          $Res Function(_TogglePeriodReminder) _then) =
      __$TogglePeriodReminderCopyWithImpl;
  @useResult
  $Res call({bool enabled});
}

/// @nodoc
class __$TogglePeriodReminderCopyWithImpl<$Res>
    implements _$TogglePeriodReminderCopyWith<$Res> {
  __$TogglePeriodReminderCopyWithImpl(this._self, this._then);

  final _TogglePeriodReminder _self;
  final $Res Function(_TogglePeriodReminder) _then;

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? enabled = null,
  }) {
    return _then(_TogglePeriodReminder(
      null == enabled
          ? _self.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _UpdatePeriodReminderDays implements PeriodReminderSettingsEvent {
  const _UpdatePeriodReminderDays(this.daysBefore);

  final int daysBefore;

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdatePeriodReminderDaysCopyWith<_UpdatePeriodReminderDays> get copyWith =>
      __$UpdatePeriodReminderDaysCopyWithImpl<_UpdatePeriodReminderDays>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdatePeriodReminderDays &&
            (identical(other.daysBefore, daysBefore) ||
                other.daysBefore == daysBefore));
  }

  @override
  int get hashCode => Object.hash(runtimeType, daysBefore);

  @override
  String toString() {
    return 'PeriodReminderSettingsEvent.updatePeriodReminderDays(daysBefore: $daysBefore)';
  }
}

/// @nodoc
abstract mixin class _$UpdatePeriodReminderDaysCopyWith<$Res>
    implements $PeriodReminderSettingsEventCopyWith<$Res> {
  factory _$UpdatePeriodReminderDaysCopyWith(_UpdatePeriodReminderDays value,
          $Res Function(_UpdatePeriodReminderDays) _then) =
      __$UpdatePeriodReminderDaysCopyWithImpl;
  @useResult
  $Res call({int daysBefore});
}

/// @nodoc
class __$UpdatePeriodReminderDaysCopyWithImpl<$Res>
    implements _$UpdatePeriodReminderDaysCopyWith<$Res> {
  __$UpdatePeriodReminderDaysCopyWithImpl(this._self, this._then);

  final _UpdatePeriodReminderDays _self;
  final $Res Function(_UpdatePeriodReminderDays) _then;

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? daysBefore = null,
  }) {
    return _then(_UpdatePeriodReminderDays(
      null == daysBefore
          ? _self.daysBefore
          : daysBefore // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _ToggleOvulationReminder implements PeriodReminderSettingsEvent {
  const _ToggleOvulationReminder(this.enabled);

  final bool enabled;

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ToggleOvulationReminderCopyWith<_ToggleOvulationReminder> get copyWith =>
      __$ToggleOvulationReminderCopyWithImpl<_ToggleOvulationReminder>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ToggleOvulationReminder &&
            (identical(other.enabled, enabled) || other.enabled == enabled));
  }

  @override
  int get hashCode => Object.hash(runtimeType, enabled);

  @override
  String toString() {
    return 'PeriodReminderSettingsEvent.toggleOvulationReminder(enabled: $enabled)';
  }
}

/// @nodoc
abstract mixin class _$ToggleOvulationReminderCopyWith<$Res>
    implements $PeriodReminderSettingsEventCopyWith<$Res> {
  factory _$ToggleOvulationReminderCopyWith(_ToggleOvulationReminder value,
          $Res Function(_ToggleOvulationReminder) _then) =
      __$ToggleOvulationReminderCopyWithImpl;
  @useResult
  $Res call({bool enabled});
}

/// @nodoc
class __$ToggleOvulationReminderCopyWithImpl<$Res>
    implements _$ToggleOvulationReminderCopyWith<$Res> {
  __$ToggleOvulationReminderCopyWithImpl(this._self, this._then);

  final _ToggleOvulationReminder _self;
  final $Res Function(_ToggleOvulationReminder) _then;

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? enabled = null,
  }) {
    return _then(_ToggleOvulationReminder(
      null == enabled
          ? _self.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _UpdateOvulationReminderDays implements PeriodReminderSettingsEvent {
  const _UpdateOvulationReminderDays(this.daysBefore);

  final int daysBefore;

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdateOvulationReminderDaysCopyWith<_UpdateOvulationReminderDays>
      get copyWith => __$UpdateOvulationReminderDaysCopyWithImpl<
          _UpdateOvulationReminderDays>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateOvulationReminderDays &&
            (identical(other.daysBefore, daysBefore) ||
                other.daysBefore == daysBefore));
  }

  @override
  int get hashCode => Object.hash(runtimeType, daysBefore);

  @override
  String toString() {
    return 'PeriodReminderSettingsEvent.updateOvulationReminderDays(daysBefore: $daysBefore)';
  }
}

/// @nodoc
abstract mixin class _$UpdateOvulationReminderDaysCopyWith<$Res>
    implements $PeriodReminderSettingsEventCopyWith<$Res> {
  factory _$UpdateOvulationReminderDaysCopyWith(
          _UpdateOvulationReminderDays value,
          $Res Function(_UpdateOvulationReminderDays) _then) =
      __$UpdateOvulationReminderDaysCopyWithImpl;
  @useResult
  $Res call({int daysBefore});
}

/// @nodoc
class __$UpdateOvulationReminderDaysCopyWithImpl<$Res>
    implements _$UpdateOvulationReminderDaysCopyWith<$Res> {
  __$UpdateOvulationReminderDaysCopyWithImpl(this._self, this._then);

  final _UpdateOvulationReminderDays _self;
  final $Res Function(_UpdateOvulationReminderDays) _then;

  /// Create a copy of PeriodReminderSettingsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? daysBefore = null,
  }) {
    return _then(_UpdateOvulationReminderDays(
      null == daysBefore
          ? _self.daysBefore
          : daysBefore // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _SaveSettings implements PeriodReminderSettingsEvent {
  const _SaveSettings();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _SaveSettings);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodReminderSettingsEvent.saveSettings()';
  }
}

/// @nodoc
mixin _$PeriodReminderSettingsState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PeriodReminderSettingsState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodReminderSettingsState()';
  }
}

/// @nodoc
class $PeriodReminderSettingsStateCopyWith<$Res> {
  $PeriodReminderSettingsStateCopyWith(PeriodReminderSettingsState _,
      $Res Function(PeriodReminderSettingsState) __);
}

/// Adds pattern-matching-related methods to [PeriodReminderSettingsState].
extension PeriodReminderSettingsStatePatterns on PeriodReminderSettingsState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Saving value)? saving,
    TResult Function(_Saved value)? saved,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Loaded() when loaded != null:
        return loaded(_that);
      case _Saving() when saving != null:
        return saving(_that);
      case _Saved() when saved != null:
        return saved(_that);
      case _Failure() when failure != null:
        return failure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Saving value) saving,
    required TResult Function(_Saved value) saved,
    required TResult Function(_Failure value) failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _Loading():
        return loading(_that);
      case _Loaded():
        return loaded(_that);
      case _Saving():
        return saving(_that);
      case _Saved():
        return saved(_that);
      case _Failure():
        return failure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Saving value)? saving,
    TResult? Function(_Saved value)? saved,
    TResult? Function(_Failure value)? failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Loaded() when loaded != null:
        return loaded(_that);
      case _Saving() when saving != null:
        return saving(_that);
      case _Saved() when saved != null:
        return saved(_that);
      case _Failure() when failure != null:
        return failure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(PeriodReminderSettings settings)? loaded,
    TResult Function()? saving,
    TResult Function(PeriodReminderSettings settings)? saved,
    TResult Function(String message)? failure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _Loaded() when loaded != null:
        return loaded(_that.settings);
      case _Saving() when saving != null:
        return saving();
      case _Saved() when saved != null:
        return saved(_that.settings);
      case _Failure() when failure != null:
        return failure(_that.message);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(PeriodReminderSettings settings) loaded,
    required TResult Function() saving,
    required TResult Function(PeriodReminderSettings settings) saved,
    required TResult Function(String message) failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _Loading():
        return loading();
      case _Loaded():
        return loaded(_that.settings);
      case _Saving():
        return saving();
      case _Saved():
        return saved(_that.settings);
      case _Failure():
        return failure(_that.message);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(PeriodReminderSettings settings)? loaded,
    TResult? Function()? saving,
    TResult? Function(PeriodReminderSettings settings)? saved,
    TResult? Function(String message)? failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _Loaded() when loaded != null:
        return loaded(_that.settings);
      case _Saving() when saving != null:
        return saving();
      case _Saved() when saved != null:
        return saved(_that.settings);
      case _Failure() when failure != null:
        return failure(_that.message);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements PeriodReminderSettingsState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodReminderSettingsState.initial()';
  }
}

/// @nodoc

class _Loading implements PeriodReminderSettingsState {
  const _Loading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Loading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodReminderSettingsState.loading()';
  }
}

/// @nodoc

class _Loaded implements PeriodReminderSettingsState {
  const _Loaded({required this.settings});

  final PeriodReminderSettings settings;

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadedCopyWith<_Loaded> get copyWith =>
      __$LoadedCopyWithImpl<_Loaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Loaded &&
            (identical(other.settings, settings) ||
                other.settings == settings));
  }

  @override
  int get hashCode => Object.hash(runtimeType, settings);

  @override
  String toString() {
    return 'PeriodReminderSettingsState.loaded(settings: $settings)';
  }
}

/// @nodoc
abstract mixin class _$LoadedCopyWith<$Res>
    implements $PeriodReminderSettingsStateCopyWith<$Res> {
  factory _$LoadedCopyWith(_Loaded value, $Res Function(_Loaded) _then) =
      __$LoadedCopyWithImpl;
  @useResult
  $Res call({PeriodReminderSettings settings});
}

/// @nodoc
class __$LoadedCopyWithImpl<$Res> implements _$LoadedCopyWith<$Res> {
  __$LoadedCopyWithImpl(this._self, this._then);

  final _Loaded _self;
  final $Res Function(_Loaded) _then;

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? settings = null,
  }) {
    return _then(_Loaded(
      settings: null == settings
          ? _self.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as PeriodReminderSettings,
    ));
  }
}

/// @nodoc

class _Saving implements PeriodReminderSettingsState {
  const _Saving();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Saving);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodReminderSettingsState.saving()';
  }
}

/// @nodoc

class _Saved implements PeriodReminderSettingsState {
  const _Saved({required this.settings});

  final PeriodReminderSettings settings;

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SavedCopyWith<_Saved> get copyWith =>
      __$SavedCopyWithImpl<_Saved>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Saved &&
            (identical(other.settings, settings) ||
                other.settings == settings));
  }

  @override
  int get hashCode => Object.hash(runtimeType, settings);

  @override
  String toString() {
    return 'PeriodReminderSettingsState.saved(settings: $settings)';
  }
}

/// @nodoc
abstract mixin class _$SavedCopyWith<$Res>
    implements $PeriodReminderSettingsStateCopyWith<$Res> {
  factory _$SavedCopyWith(_Saved value, $Res Function(_Saved) _then) =
      __$SavedCopyWithImpl;
  @useResult
  $Res call({PeriodReminderSettings settings});
}

/// @nodoc
class __$SavedCopyWithImpl<$Res> implements _$SavedCopyWith<$Res> {
  __$SavedCopyWithImpl(this._self, this._then);

  final _Saved _self;
  final $Res Function(_Saved) _then;

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? settings = null,
  }) {
    return _then(_Saved(
      settings: null == settings
          ? _self.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as PeriodReminderSettings,
    ));
  }
}

/// @nodoc

class _Failure implements PeriodReminderSettingsState {
  const _Failure({required this.message});

  final String message;

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FailureCopyWith<_Failure> get copyWith =>
      __$FailureCopyWithImpl<_Failure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Failure &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @override
  String toString() {
    return 'PeriodReminderSettingsState.failure(message: $message)';
  }
}

/// @nodoc
abstract mixin class _$FailureCopyWith<$Res>
    implements $PeriodReminderSettingsStateCopyWith<$Res> {
  factory _$FailureCopyWith(_Failure value, $Res Function(_Failure) _then) =
      __$FailureCopyWithImpl;
  @useResult
  $Res call({String message});
}

/// @nodoc
class __$FailureCopyWithImpl<$Res> implements _$FailureCopyWith<$Res> {
  __$FailureCopyWithImpl(this._self, this._then);

  final _Failure _self;
  final $Res Function(_Failure) _then;

  /// Create a copy of PeriodReminderSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
  }) {
    return _then(_Failure(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
