// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'daily_medication_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$DailyMedicationEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DailyMedicationEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DailyMedicationEvent()';
  }
}

/// @nodoc
class $DailyMedicationEventCopyWith<$Res> {
  $DailyMedicationEventCopyWith(
      DailyMedicationEvent _, $Res Function(DailyMedicationEvent) __);
}

/// Adds pattern-matching-related methods to [DailyMedicationEvent].
extension DailyMedicationEventPatterns on DailyMedicationEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchStarted value)? watchStarted,
    TResult Function(_DailyMedicationsReceived value)? dailyMedicationsReceived,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchStarted() when watchStarted != null:
        return watchStarted(_that);
      case _DailyMedicationsReceived() when dailyMedicationsReceived != null:
        return dailyMedicationsReceived(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchStarted value) watchStarted,
    required TResult Function(_DailyMedicationsReceived value)
        dailyMedicationsReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchStarted():
        return watchStarted(_that);
      case _DailyMedicationsReceived():
        return dailyMedicationsReceived(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchStarted value)? watchStarted,
    TResult? Function(_DailyMedicationsReceived value)?
        dailyMedicationsReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchStarted() when watchStarted != null:
        return watchStarted(_that);
      case _DailyMedicationsReceived() when dailyMedicationsReceived != null:
        return dailyMedicationsReceived(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchStarted,
    TResult Function(
            Either<MedicationFailure, List<DailyMedicationModel>>
                failureOrMeds)?
        dailyMedicationsReceived,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchStarted() when watchStarted != null:
        return watchStarted();
      case _DailyMedicationsReceived() when dailyMedicationsReceived != null:
        return dailyMedicationsReceived(_that.failureOrMeds);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchStarted,
    required TResult Function(
            Either<MedicationFailure, List<DailyMedicationModel>> failureOrMeds)
        dailyMedicationsReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchStarted():
        return watchStarted();
      case _DailyMedicationsReceived():
        return dailyMedicationsReceived(_that.failureOrMeds);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchStarted,
    TResult? Function(
            Either<MedicationFailure, List<DailyMedicationModel>>
                failureOrMeds)?
        dailyMedicationsReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchStarted() when watchStarted != null:
        return watchStarted();
      case _DailyMedicationsReceived() when dailyMedicationsReceived != null:
        return dailyMedicationsReceived(_that.failureOrMeds);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _WatchStarted implements DailyMedicationEvent {
  const _WatchStarted();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _WatchStarted);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DailyMedicationEvent.watchStarted()';
  }
}

/// @nodoc

class _DailyMedicationsReceived implements DailyMedicationEvent {
  const _DailyMedicationsReceived(this.failureOrMeds);

  final Either<MedicationFailure, List<DailyMedicationModel>> failureOrMeds;

  /// Create a copy of DailyMedicationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DailyMedicationsReceivedCopyWith<_DailyMedicationsReceived> get copyWith =>
      __$DailyMedicationsReceivedCopyWithImpl<_DailyMedicationsReceived>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DailyMedicationsReceived &&
            (identical(other.failureOrMeds, failureOrMeds) ||
                other.failureOrMeds == failureOrMeds));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrMeds);

  @override
  String toString() {
    return 'DailyMedicationEvent.dailyMedicationsReceived(failureOrMeds: $failureOrMeds)';
  }
}

/// @nodoc
abstract mixin class _$DailyMedicationsReceivedCopyWith<$Res>
    implements $DailyMedicationEventCopyWith<$Res> {
  factory _$DailyMedicationsReceivedCopyWith(_DailyMedicationsReceived value,
          $Res Function(_DailyMedicationsReceived) _then) =
      __$DailyMedicationsReceivedCopyWithImpl;
  @useResult
  $Res call(
      {Either<MedicationFailure, List<DailyMedicationModel>> failureOrMeds});
}

/// @nodoc
class __$DailyMedicationsReceivedCopyWithImpl<$Res>
    implements _$DailyMedicationsReceivedCopyWith<$Res> {
  __$DailyMedicationsReceivedCopyWithImpl(this._self, this._then);

  final _DailyMedicationsReceived _self;
  final $Res Function(_DailyMedicationsReceived) _then;

  /// Create a copy of DailyMedicationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureOrMeds = null,
  }) {
    return _then(_DailyMedicationsReceived(
      null == failureOrMeds
          ? _self.failureOrMeds
          : failureOrMeds // ignore: cast_nullable_to_non_nullable
              as Either<MedicationFailure, List<DailyMedicationModel>>,
    ));
  }
}

/// @nodoc
mixin _$DailyMedicationState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DailyMedicationState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DailyMedicationState()';
  }
}

/// @nodoc
class $DailyMedicationStateCopyWith<$Res> {
  $DailyMedicationStateCopyWith(
      DailyMedicationState _, $Res Function(DailyMedicationState) __);
}

/// Adds pattern-matching-related methods to [DailyMedicationState].
extension DailyMedicationStatePatterns on DailyMedicationState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoadInProgress value)? loadInProgress,
    TResult Function(_LoadSuccess value)? loadSuccess,
    TResult Function(_LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _LoadInProgress() when loadInProgress != null:
        return loadInProgress(_that);
      case _LoadSuccess() when loadSuccess != null:
        return loadSuccess(_that);
      case _LoadFailure() when loadFailure != null:
        return loadFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoadInProgress value) loadInProgress,
    required TResult Function(_LoadSuccess value) loadSuccess,
    required TResult Function(_LoadFailure value) loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _LoadInProgress():
        return loadInProgress(_that);
      case _LoadSuccess():
        return loadSuccess(_that);
      case _LoadFailure():
        return loadFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoadInProgress value)? loadInProgress,
    TResult? Function(_LoadSuccess value)? loadSuccess,
    TResult? Function(_LoadFailure value)? loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _LoadInProgress() when loadInProgress != null:
        return loadInProgress(_that);
      case _LoadSuccess() when loadSuccess != null:
        return loadSuccess(_that);
      case _LoadFailure() when loadFailure != null:
        return loadFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadInProgress,
    TResult Function(List<DailyMedicationModel> medications)? loadSuccess,
    TResult Function(MedicationFailure failure)? loadFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _LoadInProgress() when loadInProgress != null:
        return loadInProgress();
      case _LoadSuccess() when loadSuccess != null:
        return loadSuccess(_that.medications);
      case _LoadFailure() when loadFailure != null:
        return loadFailure(_that.failure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadInProgress,
    required TResult Function(List<DailyMedicationModel> medications)
        loadSuccess,
    required TResult Function(MedicationFailure failure) loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _LoadInProgress():
        return loadInProgress();
      case _LoadSuccess():
        return loadSuccess(_that.medications);
      case _LoadFailure():
        return loadFailure(_that.failure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadInProgress,
    TResult? Function(List<DailyMedicationModel> medications)? loadSuccess,
    TResult? Function(MedicationFailure failure)? loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _LoadInProgress() when loadInProgress != null:
        return loadInProgress();
      case _LoadSuccess() when loadSuccess != null:
        return loadSuccess(_that.medications);
      case _LoadFailure() when loadFailure != null:
        return loadFailure(_that.failure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements DailyMedicationState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DailyMedicationState.initial()';
  }
}

/// @nodoc

class _LoadInProgress implements DailyMedicationState {
  const _LoadInProgress();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _LoadInProgress);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'DailyMedicationState.loadInProgress()';
  }
}

/// @nodoc

class _LoadSuccess implements DailyMedicationState {
  const _LoadSuccess(final List<DailyMedicationModel> medications)
      : _medications = medications;

  final List<DailyMedicationModel> _medications;
  List<DailyMedicationModel> get medications {
    if (_medications is EqualUnmodifiableListView) return _medications;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_medications);
  }

  /// Create a copy of DailyMedicationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadSuccessCopyWith<_LoadSuccess> get copyWith =>
      __$LoadSuccessCopyWithImpl<_LoadSuccess>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoadSuccess &&
            const DeepCollectionEquality()
                .equals(other._medications, _medications));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_medications));

  @override
  String toString() {
    return 'DailyMedicationState.loadSuccess(medications: $medications)';
  }
}

/// @nodoc
abstract mixin class _$LoadSuccessCopyWith<$Res>
    implements $DailyMedicationStateCopyWith<$Res> {
  factory _$LoadSuccessCopyWith(
          _LoadSuccess value, $Res Function(_LoadSuccess) _then) =
      __$LoadSuccessCopyWithImpl;
  @useResult
  $Res call({List<DailyMedicationModel> medications});
}

/// @nodoc
class __$LoadSuccessCopyWithImpl<$Res> implements _$LoadSuccessCopyWith<$Res> {
  __$LoadSuccessCopyWithImpl(this._self, this._then);

  final _LoadSuccess _self;
  final $Res Function(_LoadSuccess) _then;

  /// Create a copy of DailyMedicationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? medications = null,
  }) {
    return _then(_LoadSuccess(
      null == medications
          ? _self._medications
          : medications // ignore: cast_nullable_to_non_nullable
              as List<DailyMedicationModel>,
    ));
  }
}

/// @nodoc

class _LoadFailure implements DailyMedicationState {
  const _LoadFailure(this.failure);

  final MedicationFailure failure;

  /// Create a copy of DailyMedicationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadFailureCopyWith<_LoadFailure> get copyWith =>
      __$LoadFailureCopyWithImpl<_LoadFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoadFailure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'DailyMedicationState.loadFailure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class _$LoadFailureCopyWith<$Res>
    implements $DailyMedicationStateCopyWith<$Res> {
  factory _$LoadFailureCopyWith(
          _LoadFailure value, $Res Function(_LoadFailure) _then) =
      __$LoadFailureCopyWithImpl;
  @useResult
  $Res call({MedicationFailure failure});

  $MedicationFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$LoadFailureCopyWithImpl<$Res> implements _$LoadFailureCopyWith<$Res> {
  __$LoadFailureCopyWithImpl(this._self, this._then);

  final _LoadFailure _self;
  final $Res Function(_LoadFailure) _then;

  /// Create a copy of DailyMedicationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(_LoadFailure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as MedicationFailure,
    ));
  }

  /// Create a copy of DailyMedicationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MedicationFailureCopyWith<$Res> get failure {
    return $MedicationFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

// dart format on
