// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'medication_form_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MedicationFormEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is MedicationFormEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MedicationFormEvent()';
  }
}

/// @nodoc
class $MedicationFormEventCopyWith<$Res> {
  $MedicationFormEventCopyWith(
      MedicationFormEvent _, $Res Function(MedicationFormEvent) __);
}

/// Adds pattern-matching-related methods to [MedicationFormEvent].
extension MedicationFormEventPatterns on MedicationFormEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initialized value)? initialized,
    TResult Function(_NameChanged value)? nameChanged,
    TResult Function(_DosageChanged value)? dosageChanged,
    TResult Function(_FrequencyChanged value)? frequencyChanged,
    TResult Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult Function(_NotesChanged value)? notesChanged,
    TResult Function(_StartDateChanged value)? startDateChanged,
    TResult Function(_Save value)? save,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initialized() when initialized != null:
        return initialized(_that);
      case _NameChanged() when nameChanged != null:
        return nameChanged(_that);
      case _DosageChanged() when dosageChanged != null:
        return dosageChanged(_that);
      case _FrequencyChanged() when frequencyChanged != null:
        return frequencyChanged(_that);
      case _FrequencyUnitChanged() when frequencyUnitChanged != null:
        return frequencyUnitChanged(_that);
      case _DosageUnitChanged() when dosageUnitChanged != null:
        return dosageUnitChanged(_that);
      case _DaystoBeNotifiedChanged() when daystoBeNotifiedChanged != null:
        return daystoBeNotifiedChanged(_that);
      case _TimeofDayChanged() when timeofDayChanged != null:
        return timeofDayChanged(_that);
      case _MonthlyDateToBeNotifiedChanged()
          when monthlyDateToBeNotifiedChanged != null:
        return monthlyDateToBeNotifiedChanged(_that);
      case _IsNotificationEnabledChanged()
          when isNotificationEnabledChanged != null:
        return isNotificationEnabledChanged(_that);
      case _NotesChanged() when notesChanged != null:
        return notesChanged(_that);
      case _StartDateChanged() when startDateChanged != null:
        return startDateChanged(_that);
      case _Save() when save != null:
        return save(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initialized value) initialized,
    required TResult Function(_NameChanged value) nameChanged,
    required TResult Function(_DosageChanged value) dosageChanged,
    required TResult Function(_FrequencyChanged value) frequencyChanged,
    required TResult Function(_FrequencyUnitChanged value) frequencyUnitChanged,
    required TResult Function(_DosageUnitChanged value) dosageUnitChanged,
    required TResult Function(_DaystoBeNotifiedChanged value)
        daystoBeNotifiedChanged,
    required TResult Function(_TimeofDayChanged value) timeofDayChanged,
    required TResult Function(_MonthlyDateToBeNotifiedChanged value)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(_IsNotificationEnabledChanged value)
        isNotificationEnabledChanged,
    required TResult Function(_NotesChanged value) notesChanged,
    required TResult Function(_StartDateChanged value) startDateChanged,
    required TResult Function(_Save value) save,
  }) {
    final _that = this;
    switch (_that) {
      case _Initialized():
        return initialized(_that);
      case _NameChanged():
        return nameChanged(_that);
      case _DosageChanged():
        return dosageChanged(_that);
      case _FrequencyChanged():
        return frequencyChanged(_that);
      case _FrequencyUnitChanged():
        return frequencyUnitChanged(_that);
      case _DosageUnitChanged():
        return dosageUnitChanged(_that);
      case _DaystoBeNotifiedChanged():
        return daystoBeNotifiedChanged(_that);
      case _TimeofDayChanged():
        return timeofDayChanged(_that);
      case _MonthlyDateToBeNotifiedChanged():
        return monthlyDateToBeNotifiedChanged(_that);
      case _IsNotificationEnabledChanged():
        return isNotificationEnabledChanged(_that);
      case _NotesChanged():
        return notesChanged(_that);
      case _StartDateChanged():
        return startDateChanged(_that);
      case _Save():
        return save(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initialized value)? initialized,
    TResult? Function(_NameChanged value)? nameChanged,
    TResult? Function(_DosageChanged value)? dosageChanged,
    TResult? Function(_FrequencyChanged value)? frequencyChanged,
    TResult? Function(_FrequencyUnitChanged value)? frequencyUnitChanged,
    TResult? Function(_DosageUnitChanged value)? dosageUnitChanged,
    TResult? Function(_DaystoBeNotifiedChanged value)? daystoBeNotifiedChanged,
    TResult? Function(_TimeofDayChanged value)? timeofDayChanged,
    TResult? Function(_MonthlyDateToBeNotifiedChanged value)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(_IsNotificationEnabledChanged value)?
        isNotificationEnabledChanged,
    TResult? Function(_NotesChanged value)? notesChanged,
    TResult? Function(_StartDateChanged value)? startDateChanged,
    TResult? Function(_Save value)? save,
  }) {
    final _that = this;
    switch (_that) {
      case _Initialized() when initialized != null:
        return initialized(_that);
      case _NameChanged() when nameChanged != null:
        return nameChanged(_that);
      case _DosageChanged() when dosageChanged != null:
        return dosageChanged(_that);
      case _FrequencyChanged() when frequencyChanged != null:
        return frequencyChanged(_that);
      case _FrequencyUnitChanged() when frequencyUnitChanged != null:
        return frequencyUnitChanged(_that);
      case _DosageUnitChanged() when dosageUnitChanged != null:
        return dosageUnitChanged(_that);
      case _DaystoBeNotifiedChanged() when daystoBeNotifiedChanged != null:
        return daystoBeNotifiedChanged(_that);
      case _TimeofDayChanged() when timeofDayChanged != null:
        return timeofDayChanged(_that);
      case _MonthlyDateToBeNotifiedChanged()
          when monthlyDateToBeNotifiedChanged != null:
        return monthlyDateToBeNotifiedChanged(_that);
      case _IsNotificationEnabledChanged()
          when isNotificationEnabledChanged != null:
        return isNotificationEnabledChanged(_that);
      case _NotesChanged() when notesChanged != null:
        return notesChanged(_that);
      case _StartDateChanged() when startDateChanged != null:
        return startDateChanged(_that);
      case _Save() when save != null:
        return save(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult Function(String nameStr)? nameChanged,
    TResult Function(String dosageStr)? dosageChanged,
    TResult Function(String frequencyStr)? frequencyChanged,
    TResult Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult Function(String dosageUnitStr)? dosageUnitChanged,
    TResult Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult Function(List<String> timeofDay)? timeofDayChanged,
    TResult Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult Function(String notesStr)? notesChanged,
    TResult Function(DateTime startDate)? startDateChanged,
    TResult Function()? save,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initialized() when initialized != null:
        return initialized(_that.initialMedicationOption);
      case _NameChanged() when nameChanged != null:
        return nameChanged(_that.nameStr);
      case _DosageChanged() when dosageChanged != null:
        return dosageChanged(_that.dosageStr);
      case _FrequencyChanged() when frequencyChanged != null:
        return frequencyChanged(_that.frequencyStr);
      case _FrequencyUnitChanged() when frequencyUnitChanged != null:
        return frequencyUnitChanged(_that.frequencyUnitStr);
      case _DosageUnitChanged() when dosageUnitChanged != null:
        return dosageUnitChanged(_that.dosageUnitStr);
      case _DaystoBeNotifiedChanged() when daystoBeNotifiedChanged != null:
        return daystoBeNotifiedChanged(_that.daystoBeNotified);
      case _TimeofDayChanged() when timeofDayChanged != null:
        return timeofDayChanged(_that.timeofDay);
      case _MonthlyDateToBeNotifiedChanged()
          when monthlyDateToBeNotifiedChanged != null:
        return monthlyDateToBeNotifiedChanged(_that.monthlyDateToBeNotified);
      case _IsNotificationEnabledChanged()
          when isNotificationEnabledChanged != null:
        return isNotificationEnabledChanged(_that.isNotificationEnabled);
      case _NotesChanged() when notesChanged != null:
        return notesChanged(_that.notesStr);
      case _StartDateChanged() when startDateChanged != null:
        return startDateChanged(_that.startDate);
      case _Save() when save != null:
        return save();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Option<MedicationModel> initialMedicationOption)
        initialized,
    required TResult Function(String nameStr) nameChanged,
    required TResult Function(String dosageStr) dosageChanged,
    required TResult Function(String frequencyStr) frequencyChanged,
    required TResult Function(String frequencyUnitStr) frequencyUnitChanged,
    required TResult Function(String dosageUnitStr) dosageUnitChanged,
    required TResult Function(List<String> daystoBeNotified)
        daystoBeNotifiedChanged,
    required TResult Function(List<String> timeofDay) timeofDayChanged,
    required TResult Function(DateTime monthlyDateToBeNotified)
        monthlyDateToBeNotifiedChanged,
    required TResult Function(bool isNotificationEnabled)
        isNotificationEnabledChanged,
    required TResult Function(String notesStr) notesChanged,
    required TResult Function(DateTime startDate) startDateChanged,
    required TResult Function() save,
  }) {
    final _that = this;
    switch (_that) {
      case _Initialized():
        return initialized(_that.initialMedicationOption);
      case _NameChanged():
        return nameChanged(_that.nameStr);
      case _DosageChanged():
        return dosageChanged(_that.dosageStr);
      case _FrequencyChanged():
        return frequencyChanged(_that.frequencyStr);
      case _FrequencyUnitChanged():
        return frequencyUnitChanged(_that.frequencyUnitStr);
      case _DosageUnitChanged():
        return dosageUnitChanged(_that.dosageUnitStr);
      case _DaystoBeNotifiedChanged():
        return daystoBeNotifiedChanged(_that.daystoBeNotified);
      case _TimeofDayChanged():
        return timeofDayChanged(_that.timeofDay);
      case _MonthlyDateToBeNotifiedChanged():
        return monthlyDateToBeNotifiedChanged(_that.monthlyDateToBeNotified);
      case _IsNotificationEnabledChanged():
        return isNotificationEnabledChanged(_that.isNotificationEnabled);
      case _NotesChanged():
        return notesChanged(_that.notesStr);
      case _StartDateChanged():
        return startDateChanged(_that.startDate);
      case _Save():
        return save();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Option<MedicationModel> initialMedicationOption)?
        initialized,
    TResult? Function(String nameStr)? nameChanged,
    TResult? Function(String dosageStr)? dosageChanged,
    TResult? Function(String frequencyStr)? frequencyChanged,
    TResult? Function(String frequencyUnitStr)? frequencyUnitChanged,
    TResult? Function(String dosageUnitStr)? dosageUnitChanged,
    TResult? Function(List<String> daystoBeNotified)? daystoBeNotifiedChanged,
    TResult? Function(List<String> timeofDay)? timeofDayChanged,
    TResult? Function(DateTime monthlyDateToBeNotified)?
        monthlyDateToBeNotifiedChanged,
    TResult? Function(bool isNotificationEnabled)? isNotificationEnabledChanged,
    TResult? Function(String notesStr)? notesChanged,
    TResult? Function(DateTime startDate)? startDateChanged,
    TResult? Function()? save,
  }) {
    final _that = this;
    switch (_that) {
      case _Initialized() when initialized != null:
        return initialized(_that.initialMedicationOption);
      case _NameChanged() when nameChanged != null:
        return nameChanged(_that.nameStr);
      case _DosageChanged() when dosageChanged != null:
        return dosageChanged(_that.dosageStr);
      case _FrequencyChanged() when frequencyChanged != null:
        return frequencyChanged(_that.frequencyStr);
      case _FrequencyUnitChanged() when frequencyUnitChanged != null:
        return frequencyUnitChanged(_that.frequencyUnitStr);
      case _DosageUnitChanged() when dosageUnitChanged != null:
        return dosageUnitChanged(_that.dosageUnitStr);
      case _DaystoBeNotifiedChanged() when daystoBeNotifiedChanged != null:
        return daystoBeNotifiedChanged(_that.daystoBeNotified);
      case _TimeofDayChanged() when timeofDayChanged != null:
        return timeofDayChanged(_that.timeofDay);
      case _MonthlyDateToBeNotifiedChanged()
          when monthlyDateToBeNotifiedChanged != null:
        return monthlyDateToBeNotifiedChanged(_that.monthlyDateToBeNotified);
      case _IsNotificationEnabledChanged()
          when isNotificationEnabledChanged != null:
        return isNotificationEnabledChanged(_that.isNotificationEnabled);
      case _NotesChanged() when notesChanged != null:
        return notesChanged(_that.notesStr);
      case _StartDateChanged() when startDateChanged != null:
        return startDateChanged(_that.startDate);
      case _Save() when save != null:
        return save();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initialized implements MedicationFormEvent {
  const _Initialized(this.initialMedicationOption);

  final Option<MedicationModel> initialMedicationOption;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InitializedCopyWith<_Initialized> get copyWith =>
      __$InitializedCopyWithImpl<_Initialized>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Initialized &&
            (identical(
                    other.initialMedicationOption, initialMedicationOption) ||
                other.initialMedicationOption == initialMedicationOption));
  }

  @override
  int get hashCode => Object.hash(runtimeType, initialMedicationOption);

  @override
  String toString() {
    return 'MedicationFormEvent.initialized(initialMedicationOption: $initialMedicationOption)';
  }
}

/// @nodoc
abstract mixin class _$InitializedCopyWith<$Res>
    implements $MedicationFormEventCopyWith<$Res> {
  factory _$InitializedCopyWith(
          _Initialized value, $Res Function(_Initialized) _then) =
      __$InitializedCopyWithImpl;
  @useResult
  $Res call({Option<MedicationModel> initialMedicationOption});
}

/// @nodoc
class __$InitializedCopyWithImpl<$Res> implements _$InitializedCopyWith<$Res> {
  __$InitializedCopyWithImpl(this._self, this._then);

  final _Initialized _self;
  final $Res Function(_Initialized) _then;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? initialMedicationOption = null,
  }) {
    return _then(_Initialized(
      null == initialMedicationOption
          ? _self.initialMedicationOption
          : initialMedicationOption // ignore: cast_nullable_to_non_nullable
              as Option<MedicationModel>,
    ));
  }
}

/// @nodoc

class _NameChanged implements MedicationFormEvent {
  const _NameChanged(this.nameStr);

  final String nameStr;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$NameChangedCopyWith<_NameChanged> get copyWith =>
      __$NameChangedCopyWithImpl<_NameChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _NameChanged &&
            (identical(other.nameStr, nameStr) || other.nameStr == nameStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, nameStr);

  @override
  String toString() {
    return 'MedicationFormEvent.nameChanged(nameStr: $nameStr)';
  }
}

/// @nodoc
abstract mixin class _$NameChangedCopyWith<$Res>
    implements $MedicationFormEventCopyWith<$Res> {
  factory _$NameChangedCopyWith(
          _NameChanged value, $Res Function(_NameChanged) _then) =
      __$NameChangedCopyWithImpl;
  @useResult
  $Res call({String nameStr});
}

/// @nodoc
class __$NameChangedCopyWithImpl<$Res> implements _$NameChangedCopyWith<$Res> {
  __$NameChangedCopyWithImpl(this._self, this._then);

  final _NameChanged _self;
  final $Res Function(_NameChanged) _then;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? nameStr = null,
  }) {
    return _then(_NameChanged(
      null == nameStr
          ? _self.nameStr
          : nameStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _DosageChanged implements MedicationFormEvent {
  const _DosageChanged(this.dosageStr);

  final String dosageStr;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DosageChangedCopyWith<_DosageChanged> get copyWith =>
      __$DosageChangedCopyWithImpl<_DosageChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DosageChanged &&
            (identical(other.dosageStr, dosageStr) ||
                other.dosageStr == dosageStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, dosageStr);

  @override
  String toString() {
    return 'MedicationFormEvent.dosageChanged(dosageStr: $dosageStr)';
  }
}

/// @nodoc
abstract mixin class _$DosageChangedCopyWith<$Res>
    implements $MedicationFormEventCopyWith<$Res> {
  factory _$DosageChangedCopyWith(
          _DosageChanged value, $Res Function(_DosageChanged) _then) =
      __$DosageChangedCopyWithImpl;
  @useResult
  $Res call({String dosageStr});
}

/// @nodoc
class __$DosageChangedCopyWithImpl<$Res>
    implements _$DosageChangedCopyWith<$Res> {
  __$DosageChangedCopyWithImpl(this._self, this._then);

  final _DosageChanged _self;
  final $Res Function(_DosageChanged) _then;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? dosageStr = null,
  }) {
    return _then(_DosageChanged(
      null == dosageStr
          ? _self.dosageStr
          : dosageStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _FrequencyChanged implements MedicationFormEvent {
  const _FrequencyChanged(this.frequencyStr);

  final String frequencyStr;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FrequencyChangedCopyWith<_FrequencyChanged> get copyWith =>
      __$FrequencyChangedCopyWithImpl<_FrequencyChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FrequencyChanged &&
            (identical(other.frequencyStr, frequencyStr) ||
                other.frequencyStr == frequencyStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, frequencyStr);

  @override
  String toString() {
    return 'MedicationFormEvent.frequencyChanged(frequencyStr: $frequencyStr)';
  }
}

/// @nodoc
abstract mixin class _$FrequencyChangedCopyWith<$Res>
    implements $MedicationFormEventCopyWith<$Res> {
  factory _$FrequencyChangedCopyWith(
          _FrequencyChanged value, $Res Function(_FrequencyChanged) _then) =
      __$FrequencyChangedCopyWithImpl;
  @useResult
  $Res call({String frequencyStr});
}

/// @nodoc
class __$FrequencyChangedCopyWithImpl<$Res>
    implements _$FrequencyChangedCopyWith<$Res> {
  __$FrequencyChangedCopyWithImpl(this._self, this._then);

  final _FrequencyChanged _self;
  final $Res Function(_FrequencyChanged) _then;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? frequencyStr = null,
  }) {
    return _then(_FrequencyChanged(
      null == frequencyStr
          ? _self.frequencyStr
          : frequencyStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _FrequencyUnitChanged implements MedicationFormEvent {
  const _FrequencyUnitChanged(this.frequencyUnitStr);

  final String frequencyUnitStr;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FrequencyUnitChangedCopyWith<_FrequencyUnitChanged> get copyWith =>
      __$FrequencyUnitChangedCopyWithImpl<_FrequencyUnitChanged>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FrequencyUnitChanged &&
            (identical(other.frequencyUnitStr, frequencyUnitStr) ||
                other.frequencyUnitStr == frequencyUnitStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, frequencyUnitStr);

  @override
  String toString() {
    return 'MedicationFormEvent.frequencyUnitChanged(frequencyUnitStr: $frequencyUnitStr)';
  }
}

/// @nodoc
abstract mixin class _$FrequencyUnitChangedCopyWith<$Res>
    implements $MedicationFormEventCopyWith<$Res> {
  factory _$FrequencyUnitChangedCopyWith(_FrequencyUnitChanged value,
          $Res Function(_FrequencyUnitChanged) _then) =
      __$FrequencyUnitChangedCopyWithImpl;
  @useResult
  $Res call({String frequencyUnitStr});
}

/// @nodoc
class __$FrequencyUnitChangedCopyWithImpl<$Res>
    implements _$FrequencyUnitChangedCopyWith<$Res> {
  __$FrequencyUnitChangedCopyWithImpl(this._self, this._then);

  final _FrequencyUnitChanged _self;
  final $Res Function(_FrequencyUnitChanged) _then;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? frequencyUnitStr = null,
  }) {
    return _then(_FrequencyUnitChanged(
      null == frequencyUnitStr
          ? _self.frequencyUnitStr
          : frequencyUnitStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _DosageUnitChanged implements MedicationFormEvent {
  const _DosageUnitChanged(this.dosageUnitStr);

  final String dosageUnitStr;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DosageUnitChangedCopyWith<_DosageUnitChanged> get copyWith =>
      __$DosageUnitChangedCopyWithImpl<_DosageUnitChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DosageUnitChanged &&
            (identical(other.dosageUnitStr, dosageUnitStr) ||
                other.dosageUnitStr == dosageUnitStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, dosageUnitStr);

  @override
  String toString() {
    return 'MedicationFormEvent.dosageUnitChanged(dosageUnitStr: $dosageUnitStr)';
  }
}

/// @nodoc
abstract mixin class _$DosageUnitChangedCopyWith<$Res>
    implements $MedicationFormEventCopyWith<$Res> {
  factory _$DosageUnitChangedCopyWith(
          _DosageUnitChanged value, $Res Function(_DosageUnitChanged) _then) =
      __$DosageUnitChangedCopyWithImpl;
  @useResult
  $Res call({String dosageUnitStr});
}

/// @nodoc
class __$DosageUnitChangedCopyWithImpl<$Res>
    implements _$DosageUnitChangedCopyWith<$Res> {
  __$DosageUnitChangedCopyWithImpl(this._self, this._then);

  final _DosageUnitChanged _self;
  final $Res Function(_DosageUnitChanged) _then;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? dosageUnitStr = null,
  }) {
    return _then(_DosageUnitChanged(
      null == dosageUnitStr
          ? _self.dosageUnitStr
          : dosageUnitStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _DaystoBeNotifiedChanged implements MedicationFormEvent {
  const _DaystoBeNotifiedChanged(final List<String> daystoBeNotified)
      : _daystoBeNotified = daystoBeNotified;

  final List<String> _daystoBeNotified;
  List<String> get daystoBeNotified {
    if (_daystoBeNotified is EqualUnmodifiableListView)
      return _daystoBeNotified;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_daystoBeNotified);
  }

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DaystoBeNotifiedChangedCopyWith<_DaystoBeNotifiedChanged> get copyWith =>
      __$DaystoBeNotifiedChangedCopyWithImpl<_DaystoBeNotifiedChanged>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DaystoBeNotifiedChanged &&
            const DeepCollectionEquality()
                .equals(other._daystoBeNotified, _daystoBeNotified));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_daystoBeNotified));

  @override
  String toString() {
    return 'MedicationFormEvent.daystoBeNotifiedChanged(daystoBeNotified: $daystoBeNotified)';
  }
}

/// @nodoc
abstract mixin class _$DaystoBeNotifiedChangedCopyWith<$Res>
    implements $MedicationFormEventCopyWith<$Res> {
  factory _$DaystoBeNotifiedChangedCopyWith(_DaystoBeNotifiedChanged value,
          $Res Function(_DaystoBeNotifiedChanged) _then) =
      __$DaystoBeNotifiedChangedCopyWithImpl;
  @useResult
  $Res call({List<String> daystoBeNotified});
}

/// @nodoc
class __$DaystoBeNotifiedChangedCopyWithImpl<$Res>
    implements _$DaystoBeNotifiedChangedCopyWith<$Res> {
  __$DaystoBeNotifiedChangedCopyWithImpl(this._self, this._then);

  final _DaystoBeNotifiedChanged _self;
  final $Res Function(_DaystoBeNotifiedChanged) _then;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? daystoBeNotified = null,
  }) {
    return _then(_DaystoBeNotifiedChanged(
      null == daystoBeNotified
          ? _self._daystoBeNotified
          : daystoBeNotified // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _TimeofDayChanged implements MedicationFormEvent {
  const _TimeofDayChanged(final List<String> timeofDay)
      : _timeofDay = timeofDay;

  final List<String> _timeofDay;
  List<String> get timeofDay {
    if (_timeofDay is EqualUnmodifiableListView) return _timeofDay;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_timeofDay);
  }

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TimeofDayChangedCopyWith<_TimeofDayChanged> get copyWith =>
      __$TimeofDayChangedCopyWithImpl<_TimeofDayChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TimeofDayChanged &&
            const DeepCollectionEquality()
                .equals(other._timeofDay, _timeofDay));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_timeofDay));

  @override
  String toString() {
    return 'MedicationFormEvent.timeofDayChanged(timeofDay: $timeofDay)';
  }
}

/// @nodoc
abstract mixin class _$TimeofDayChangedCopyWith<$Res>
    implements $MedicationFormEventCopyWith<$Res> {
  factory _$TimeofDayChangedCopyWith(
          _TimeofDayChanged value, $Res Function(_TimeofDayChanged) _then) =
      __$TimeofDayChangedCopyWithImpl;
  @useResult
  $Res call({List<String> timeofDay});
}

/// @nodoc
class __$TimeofDayChangedCopyWithImpl<$Res>
    implements _$TimeofDayChangedCopyWith<$Res> {
  __$TimeofDayChangedCopyWithImpl(this._self, this._then);

  final _TimeofDayChanged _self;
  final $Res Function(_TimeofDayChanged) _then;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? timeofDay = null,
  }) {
    return _then(_TimeofDayChanged(
      null == timeofDay
          ? _self._timeofDay
          : timeofDay // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc

class _MonthlyDateToBeNotifiedChanged implements MedicationFormEvent {
  const _MonthlyDateToBeNotifiedChanged(this.monthlyDateToBeNotified);

  final DateTime monthlyDateToBeNotified;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MonthlyDateToBeNotifiedChangedCopyWith<_MonthlyDateToBeNotifiedChanged>
      get copyWith => __$MonthlyDateToBeNotifiedChangedCopyWithImpl<
          _MonthlyDateToBeNotifiedChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MonthlyDateToBeNotifiedChanged &&
            (identical(
                    other.monthlyDateToBeNotified, monthlyDateToBeNotified) ||
                other.monthlyDateToBeNotified == monthlyDateToBeNotified));
  }

  @override
  int get hashCode => Object.hash(runtimeType, monthlyDateToBeNotified);

  @override
  String toString() {
    return 'MedicationFormEvent.monthlyDateToBeNotifiedChanged(monthlyDateToBeNotified: $monthlyDateToBeNotified)';
  }
}

/// @nodoc
abstract mixin class _$MonthlyDateToBeNotifiedChangedCopyWith<$Res>
    implements $MedicationFormEventCopyWith<$Res> {
  factory _$MonthlyDateToBeNotifiedChangedCopyWith(
          _MonthlyDateToBeNotifiedChanged value,
          $Res Function(_MonthlyDateToBeNotifiedChanged) _then) =
      __$MonthlyDateToBeNotifiedChangedCopyWithImpl;
  @useResult
  $Res call({DateTime monthlyDateToBeNotified});
}

/// @nodoc
class __$MonthlyDateToBeNotifiedChangedCopyWithImpl<$Res>
    implements _$MonthlyDateToBeNotifiedChangedCopyWith<$Res> {
  __$MonthlyDateToBeNotifiedChangedCopyWithImpl(this._self, this._then);

  final _MonthlyDateToBeNotifiedChanged _self;
  final $Res Function(_MonthlyDateToBeNotifiedChanged) _then;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? monthlyDateToBeNotified = null,
  }) {
    return _then(_MonthlyDateToBeNotifiedChanged(
      null == monthlyDateToBeNotified
          ? _self.monthlyDateToBeNotified
          : monthlyDateToBeNotified // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _IsNotificationEnabledChanged implements MedicationFormEvent {
  const _IsNotificationEnabledChanged(this.isNotificationEnabled);

  final bool isNotificationEnabled;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$IsNotificationEnabledChangedCopyWith<_IsNotificationEnabledChanged>
      get copyWith => __$IsNotificationEnabledChangedCopyWithImpl<
          _IsNotificationEnabledChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _IsNotificationEnabledChanged &&
            (identical(other.isNotificationEnabled, isNotificationEnabled) ||
                other.isNotificationEnabled == isNotificationEnabled));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isNotificationEnabled);

  @override
  String toString() {
    return 'MedicationFormEvent.isNotificationEnabledChanged(isNotificationEnabled: $isNotificationEnabled)';
  }
}

/// @nodoc
abstract mixin class _$IsNotificationEnabledChangedCopyWith<$Res>
    implements $MedicationFormEventCopyWith<$Res> {
  factory _$IsNotificationEnabledChangedCopyWith(
          _IsNotificationEnabledChanged value,
          $Res Function(_IsNotificationEnabledChanged) _then) =
      __$IsNotificationEnabledChangedCopyWithImpl;
  @useResult
  $Res call({bool isNotificationEnabled});
}

/// @nodoc
class __$IsNotificationEnabledChangedCopyWithImpl<$Res>
    implements _$IsNotificationEnabledChangedCopyWith<$Res> {
  __$IsNotificationEnabledChangedCopyWithImpl(this._self, this._then);

  final _IsNotificationEnabledChanged _self;
  final $Res Function(_IsNotificationEnabledChanged) _then;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isNotificationEnabled = null,
  }) {
    return _then(_IsNotificationEnabledChanged(
      null == isNotificationEnabled
          ? _self.isNotificationEnabled
          : isNotificationEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _NotesChanged implements MedicationFormEvent {
  const _NotesChanged(this.notesStr);

  final String notesStr;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$NotesChangedCopyWith<_NotesChanged> get copyWith =>
      __$NotesChangedCopyWithImpl<_NotesChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _NotesChanged &&
            (identical(other.notesStr, notesStr) ||
                other.notesStr == notesStr));
  }

  @override
  int get hashCode => Object.hash(runtimeType, notesStr);

  @override
  String toString() {
    return 'MedicationFormEvent.notesChanged(notesStr: $notesStr)';
  }
}

/// @nodoc
abstract mixin class _$NotesChangedCopyWith<$Res>
    implements $MedicationFormEventCopyWith<$Res> {
  factory _$NotesChangedCopyWith(
          _NotesChanged value, $Res Function(_NotesChanged) _then) =
      __$NotesChangedCopyWithImpl;
  @useResult
  $Res call({String notesStr});
}

/// @nodoc
class __$NotesChangedCopyWithImpl<$Res>
    implements _$NotesChangedCopyWith<$Res> {
  __$NotesChangedCopyWithImpl(this._self, this._then);

  final _NotesChanged _self;
  final $Res Function(_NotesChanged) _then;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? notesStr = null,
  }) {
    return _then(_NotesChanged(
      null == notesStr
          ? _self.notesStr
          : notesStr // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _StartDateChanged implements MedicationFormEvent {
  const _StartDateChanged(this.startDate);

  final DateTime startDate;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$StartDateChangedCopyWith<_StartDateChanged> get copyWith =>
      __$StartDateChangedCopyWithImpl<_StartDateChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _StartDateChanged &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, startDate);

  @override
  String toString() {
    return 'MedicationFormEvent.startDateChanged(startDate: $startDate)';
  }
}

/// @nodoc
abstract mixin class _$StartDateChangedCopyWith<$Res>
    implements $MedicationFormEventCopyWith<$Res> {
  factory _$StartDateChangedCopyWith(
          _StartDateChanged value, $Res Function(_StartDateChanged) _then) =
      __$StartDateChangedCopyWithImpl;
  @useResult
  $Res call({DateTime startDate});
}

/// @nodoc
class __$StartDateChangedCopyWithImpl<$Res>
    implements _$StartDateChangedCopyWith<$Res> {
  __$StartDateChangedCopyWithImpl(this._self, this._then);

  final _StartDateChanged _self;
  final $Res Function(_StartDateChanged) _then;

  /// Create a copy of MedicationFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? startDate = null,
  }) {
    return _then(_StartDateChanged(
      null == startDate
          ? _self.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _Save implements MedicationFormEvent {
  const _Save();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Save);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MedicationFormEvent.save()';
  }
}

// dart format on
