// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_management_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AccountManagementEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is AccountManagementEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AccountManagementEvent()';
  }
}

/// @nodoc
class $AccountManagementEventCopyWith<$Res> {
  $AccountManagementEventCopyWith(
      AccountManagementEvent _, $Res Function(AccountManagementEvent) __);
}

/// Adds pattern-matching-related methods to [AccountManagementEvent].
extension AccountManagementEventPatterns on AccountManagementEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult Function(DeleteAccount value)? deleteAccount,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case UpdateAccountDetails() when updateAccountDetails != null:
        return updateAccountDetails(_that);
      case UpdateAccountPassword() when updateAccountPassword != null:
        return updateAccountPassword(_that);
      case UpdateAccountEmail() when updateAccountEmail != null:
        return updateAccountEmail(_that);
      case UpdateAccountPhoneNumber() when updateAccountPhoneNumber != null:
        return updateAccountPhoneNumber(_that);
      case UpdateProfilePicture() when updateProfilePicture != null:
        return updateProfilePicture(_that);
      case DeleteProfilePicture() when deleteProfilePicture != null:
        return deleteProfilePicture(_that);
      case DeleteAccount() when deleteAccount != null:
        return deleteAccount(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateAccountDetails value) updateAccountDetails,
    required TResult Function(UpdateAccountPassword value)
        updateAccountPassword,
    required TResult Function(UpdateAccountEmail value) updateAccountEmail,
    required TResult Function(UpdateAccountPhoneNumber value)
        updateAccountPhoneNumber,
    required TResult Function(UpdateProfilePicture value) updateProfilePicture,
    required TResult Function(DeleteProfilePicture value) deleteProfilePicture,
    required TResult Function(DeleteAccount value) deleteAccount,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateAccountDetails():
        return updateAccountDetails(_that);
      case UpdateAccountPassword():
        return updateAccountPassword(_that);
      case UpdateAccountEmail():
        return updateAccountEmail(_that);
      case UpdateAccountPhoneNumber():
        return updateAccountPhoneNumber(_that);
      case UpdateProfilePicture():
        return updateProfilePicture(_that);
      case DeleteProfilePicture():
        return deleteProfilePicture(_that);
      case DeleteAccount():
        return deleteAccount(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateAccountDetails value)? updateAccountDetails,
    TResult? Function(UpdateAccountPassword value)? updateAccountPassword,
    TResult? Function(UpdateAccountEmail value)? updateAccountEmail,
    TResult? Function(UpdateAccountPhoneNumber value)? updateAccountPhoneNumber,
    TResult? Function(UpdateProfilePicture value)? updateProfilePicture,
    TResult? Function(DeleteProfilePicture value)? deleteProfilePicture,
    TResult? Function(DeleteAccount value)? deleteAccount,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateAccountDetails() when updateAccountDetails != null:
        return updateAccountDetails(_that);
      case UpdateAccountPassword() when updateAccountPassword != null:
        return updateAccountPassword(_that);
      case UpdateAccountEmail() when updateAccountEmail != null:
        return updateAccountEmail(_that);
      case UpdateAccountPhoneNumber() when updateAccountPhoneNumber != null:
        return updateAccountPhoneNumber(_that);
      case UpdateProfilePicture() when updateProfilePicture != null:
        return updateProfilePicture(_that);
      case DeleteProfilePicture() when deleteProfilePicture != null:
        return deleteProfilePicture(_that);
      case DeleteAccount() when deleteAccount != null:
        return deleteAccount(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult Function(String password)? updateAccountPassword,
    TResult Function(String email)? updateAccountEmail,
    TResult Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult Function(XFile? profilePicture)? updateProfilePicture,
    TResult Function()? deleteProfilePicture,
    TResult Function()? deleteAccount,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case UpdateAccountDetails() when updateAccountDetails != null:
        return updateAccountDetails(_that.accountDetails);
      case UpdateAccountPassword() when updateAccountPassword != null:
        return updateAccountPassword(_that.password);
      case UpdateAccountEmail() when updateAccountEmail != null:
        return updateAccountEmail(_that.email);
      case UpdateAccountPhoneNumber() when updateAccountPhoneNumber != null:
        return updateAccountPhoneNumber(_that.phoneNumber);
      case UpdateProfilePicture() when updateProfilePicture != null:
        return updateProfilePicture(_that.profilePicture);
      case DeleteProfilePicture() when deleteProfilePicture != null:
        return deleteProfilePicture();
      case DeleteAccount() when deleteAccount != null:
        return deleteAccount();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(AccountDetailsModel accountDetails)
        updateAccountDetails,
    required TResult Function(String password) updateAccountPassword,
    required TResult Function(String email) updateAccountEmail,
    required TResult Function(String phoneNumber) updateAccountPhoneNumber,
    required TResult Function(XFile? profilePicture) updateProfilePicture,
    required TResult Function() deleteProfilePicture,
    required TResult Function() deleteAccount,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateAccountDetails():
        return updateAccountDetails(_that.accountDetails);
      case UpdateAccountPassword():
        return updateAccountPassword(_that.password);
      case UpdateAccountEmail():
        return updateAccountEmail(_that.email);
      case UpdateAccountPhoneNumber():
        return updateAccountPhoneNumber(_that.phoneNumber);
      case UpdateProfilePicture():
        return updateProfilePicture(_that.profilePicture);
      case DeleteProfilePicture():
        return deleteProfilePicture();
      case DeleteAccount():
        return deleteAccount();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsModel accountDetails)? updateAccountDetails,
    TResult? Function(String password)? updateAccountPassword,
    TResult? Function(String email)? updateAccountEmail,
    TResult? Function(String phoneNumber)? updateAccountPhoneNumber,
    TResult? Function(XFile? profilePicture)? updateProfilePicture,
    TResult? Function()? deleteProfilePicture,
    TResult? Function()? deleteAccount,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateAccountDetails() when updateAccountDetails != null:
        return updateAccountDetails(_that.accountDetails);
      case UpdateAccountPassword() when updateAccountPassword != null:
        return updateAccountPassword(_that.password);
      case UpdateAccountEmail() when updateAccountEmail != null:
        return updateAccountEmail(_that.email);
      case UpdateAccountPhoneNumber() when updateAccountPhoneNumber != null:
        return updateAccountPhoneNumber(_that.phoneNumber);
      case UpdateProfilePicture() when updateProfilePicture != null:
        return updateProfilePicture(_that.profilePicture);
      case DeleteProfilePicture() when deleteProfilePicture != null:
        return deleteProfilePicture();
      case DeleteAccount() when deleteAccount != null:
        return deleteAccount();
      case _:
        return null;
    }
  }
}

/// @nodoc

class UpdateAccountDetails implements AccountManagementEvent {
  const UpdateAccountDetails(this.accountDetails);

  final AccountDetailsModel accountDetails;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateAccountDetailsCopyWith<UpdateAccountDetails> get copyWith =>
      _$UpdateAccountDetailsCopyWithImpl<UpdateAccountDetails>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateAccountDetails &&
            (identical(other.accountDetails, accountDetails) ||
                other.accountDetails == accountDetails));
  }

  @override
  int get hashCode => Object.hash(runtimeType, accountDetails);

  @override
  String toString() {
    return 'AccountManagementEvent.updateAccountDetails(accountDetails: $accountDetails)';
  }
}

/// @nodoc
abstract mixin class $UpdateAccountDetailsCopyWith<$Res>
    implements $AccountManagementEventCopyWith<$Res> {
  factory $UpdateAccountDetailsCopyWith(UpdateAccountDetails value,
          $Res Function(UpdateAccountDetails) _then) =
      _$UpdateAccountDetailsCopyWithImpl;
  @useResult
  $Res call({AccountDetailsModel accountDetails});
}

/// @nodoc
class _$UpdateAccountDetailsCopyWithImpl<$Res>
    implements $UpdateAccountDetailsCopyWith<$Res> {
  _$UpdateAccountDetailsCopyWithImpl(this._self, this._then);

  final UpdateAccountDetails _self;
  final $Res Function(UpdateAccountDetails) _then;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? accountDetails = null,
  }) {
    return _then(UpdateAccountDetails(
      null == accountDetails
          ? _self.accountDetails
          : accountDetails // ignore: cast_nullable_to_non_nullable
              as AccountDetailsModel,
    ));
  }
}

/// @nodoc

class UpdateAccountPassword implements AccountManagementEvent {
  const UpdateAccountPassword(this.password);

  final String password;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateAccountPasswordCopyWith<UpdateAccountPassword> get copyWith =>
      _$UpdateAccountPasswordCopyWithImpl<UpdateAccountPassword>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateAccountPassword &&
            (identical(other.password, password) ||
                other.password == password));
  }

  @override
  int get hashCode => Object.hash(runtimeType, password);

  @override
  String toString() {
    return 'AccountManagementEvent.updateAccountPassword(password: $password)';
  }
}

/// @nodoc
abstract mixin class $UpdateAccountPasswordCopyWith<$Res>
    implements $AccountManagementEventCopyWith<$Res> {
  factory $UpdateAccountPasswordCopyWith(UpdateAccountPassword value,
          $Res Function(UpdateAccountPassword) _then) =
      _$UpdateAccountPasswordCopyWithImpl;
  @useResult
  $Res call({String password});
}

/// @nodoc
class _$UpdateAccountPasswordCopyWithImpl<$Res>
    implements $UpdateAccountPasswordCopyWith<$Res> {
  _$UpdateAccountPasswordCopyWithImpl(this._self, this._then);

  final UpdateAccountPassword _self;
  final $Res Function(UpdateAccountPassword) _then;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? password = null,
  }) {
    return _then(UpdateAccountPassword(
      null == password
          ? _self.password
          : password // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class UpdateAccountEmail implements AccountManagementEvent {
  const UpdateAccountEmail(this.email);

  final String email;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateAccountEmailCopyWith<UpdateAccountEmail> get copyWith =>
      _$UpdateAccountEmailCopyWithImpl<UpdateAccountEmail>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateAccountEmail &&
            (identical(other.email, email) || other.email == email));
  }

  @override
  int get hashCode => Object.hash(runtimeType, email);

  @override
  String toString() {
    return 'AccountManagementEvent.updateAccountEmail(email: $email)';
  }
}

/// @nodoc
abstract mixin class $UpdateAccountEmailCopyWith<$Res>
    implements $AccountManagementEventCopyWith<$Res> {
  factory $UpdateAccountEmailCopyWith(
          UpdateAccountEmail value, $Res Function(UpdateAccountEmail) _then) =
      _$UpdateAccountEmailCopyWithImpl;
  @useResult
  $Res call({String email});
}

/// @nodoc
class _$UpdateAccountEmailCopyWithImpl<$Res>
    implements $UpdateAccountEmailCopyWith<$Res> {
  _$UpdateAccountEmailCopyWithImpl(this._self, this._then);

  final UpdateAccountEmail _self;
  final $Res Function(UpdateAccountEmail) _then;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? email = null,
  }) {
    return _then(UpdateAccountEmail(
      null == email
          ? _self.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class UpdateAccountPhoneNumber implements AccountManagementEvent {
  const UpdateAccountPhoneNumber(this.phoneNumber);

  final String phoneNumber;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateAccountPhoneNumberCopyWith<UpdateAccountPhoneNumber> get copyWith =>
      _$UpdateAccountPhoneNumberCopyWithImpl<UpdateAccountPhoneNumber>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateAccountPhoneNumber &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber));
  }

  @override
  int get hashCode => Object.hash(runtimeType, phoneNumber);

  @override
  String toString() {
    return 'AccountManagementEvent.updateAccountPhoneNumber(phoneNumber: $phoneNumber)';
  }
}

/// @nodoc
abstract mixin class $UpdateAccountPhoneNumberCopyWith<$Res>
    implements $AccountManagementEventCopyWith<$Res> {
  factory $UpdateAccountPhoneNumberCopyWith(UpdateAccountPhoneNumber value,
          $Res Function(UpdateAccountPhoneNumber) _then) =
      _$UpdateAccountPhoneNumberCopyWithImpl;
  @useResult
  $Res call({String phoneNumber});
}

/// @nodoc
class _$UpdateAccountPhoneNumberCopyWithImpl<$Res>
    implements $UpdateAccountPhoneNumberCopyWith<$Res> {
  _$UpdateAccountPhoneNumberCopyWithImpl(this._self, this._then);

  final UpdateAccountPhoneNumber _self;
  final $Res Function(UpdateAccountPhoneNumber) _then;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? phoneNumber = null,
  }) {
    return _then(UpdateAccountPhoneNumber(
      null == phoneNumber
          ? _self.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class UpdateProfilePicture implements AccountManagementEvent {
  const UpdateProfilePicture(this.profilePicture);

  final XFile? profilePicture;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateProfilePictureCopyWith<UpdateProfilePicture> get copyWith =>
      _$UpdateProfilePictureCopyWithImpl<UpdateProfilePicture>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateProfilePicture &&
            (identical(other.profilePicture, profilePicture) ||
                other.profilePicture == profilePicture));
  }

  @override
  int get hashCode => Object.hash(runtimeType, profilePicture);

  @override
  String toString() {
    return 'AccountManagementEvent.updateProfilePicture(profilePicture: $profilePicture)';
  }
}

/// @nodoc
abstract mixin class $UpdateProfilePictureCopyWith<$Res>
    implements $AccountManagementEventCopyWith<$Res> {
  factory $UpdateProfilePictureCopyWith(UpdateProfilePicture value,
          $Res Function(UpdateProfilePicture) _then) =
      _$UpdateProfilePictureCopyWithImpl;
  @useResult
  $Res call({XFile? profilePicture});
}

/// @nodoc
class _$UpdateProfilePictureCopyWithImpl<$Res>
    implements $UpdateProfilePictureCopyWith<$Res> {
  _$UpdateProfilePictureCopyWithImpl(this._self, this._then);

  final UpdateProfilePicture _self;
  final $Res Function(UpdateProfilePicture) _then;

  /// Create a copy of AccountManagementEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? profilePicture = freezed,
  }) {
    return _then(UpdateProfilePicture(
      freezed == profilePicture
          ? _self.profilePicture
          : profilePicture // ignore: cast_nullable_to_non_nullable
              as XFile?,
    ));
  }
}

/// @nodoc

class DeleteProfilePicture implements AccountManagementEvent {
  const DeleteProfilePicture();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeleteProfilePicture);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AccountManagementEvent.deleteProfilePicture()';
  }
}

/// @nodoc

class DeleteAccount implements AccountManagementEvent {
  const DeleteAccount();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeleteAccount);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AccountManagementEvent.deleteAccount()';
  }
}

/// @nodoc
mixin _$AccountManagementState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is AccountManagementState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AccountManagementState()';
  }
}

/// @nodoc
class $AccountManagementStateCopyWith<$Res> {
  $AccountManagementStateCopyWith(
      AccountManagementState _, $Res Function(AccountManagementState) __);
}

/// Adds pattern-matching-related methods to [AccountManagementState].
extension AccountManagementStatePatterns on AccountManagementState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(Updating value)? updating,
    TResult Function(Updated value)? updated,
    TResult Function(UpdateFailure value)? updateFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial(_that);
      case Updating() when updating != null:
        return updating(_that);
      case Updated() when updated != null:
        return updated(_that);
      case UpdateFailure() when updateFailure != null:
        return updateFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(Updating value) updating,
    required TResult Function(Updated value) updated,
    required TResult Function(UpdateFailure value) updateFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial():
        return initial(_that);
      case Updating():
        return updating(_that);
      case Updated():
        return updated(_that);
      case UpdateFailure():
        return updateFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(Updating value)? updating,
    TResult? Function(Updated value)? updated,
    TResult? Function(UpdateFailure value)? updateFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial(_that);
      case Updating() when updating != null:
        return updating(_that);
      case Updated() when updated != null:
        return updated(_that);
      case UpdateFailure() when updateFailure != null:
        return updateFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? updating,
    TResult Function()? updated,
    TResult Function(AccountManagementFailure failure)? updateFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial();
      case Updating() when updating != null:
        return updating();
      case Updated() when updated != null:
        return updated();
      case UpdateFailure() when updateFailure != null:
        return updateFailure(_that.failure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() updating,
    required TResult Function() updated,
    required TResult Function(AccountManagementFailure failure) updateFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial():
        return initial();
      case Updating():
        return updating();
      case Updated():
        return updated();
      case UpdateFailure():
        return updateFailure(_that.failure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? updating,
    TResult? Function()? updated,
    TResult? Function(AccountManagementFailure failure)? updateFailure,
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial();
      case Updating() when updating != null:
        return updating();
      case Updated() when updated != null:
        return updated();
      case UpdateFailure() when updateFailure != null:
        return updateFailure(_that.failure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class Initial implements AccountManagementState {
  const Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AccountManagementState.initial()';
  }
}

/// @nodoc

class Updating implements AccountManagementState {
  const Updating();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Updating);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AccountManagementState.updating()';
  }
}

/// @nodoc

class Updated implements AccountManagementState {
  const Updated();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Updated);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AccountManagementState.updated()';
  }
}

/// @nodoc

class UpdateFailure implements AccountManagementState {
  const UpdateFailure(this.failure);

  final AccountManagementFailure failure;

  /// Create a copy of AccountManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateFailureCopyWith<UpdateFailure> get copyWith =>
      _$UpdateFailureCopyWithImpl<UpdateFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateFailure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'AccountManagementState.updateFailure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class $UpdateFailureCopyWith<$Res>
    implements $AccountManagementStateCopyWith<$Res> {
  factory $UpdateFailureCopyWith(
          UpdateFailure value, $Res Function(UpdateFailure) _then) =
      _$UpdateFailureCopyWithImpl;
  @useResult
  $Res call({AccountManagementFailure failure});

  $AccountManagementFailureCopyWith<$Res> get failure;
}

/// @nodoc
class _$UpdateFailureCopyWithImpl<$Res>
    implements $UpdateFailureCopyWith<$Res> {
  _$UpdateFailureCopyWithImpl(this._self, this._then);

  final UpdateFailure _self;
  final $Res Function(UpdateFailure) _then;

  /// Create a copy of AccountManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(UpdateFailure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as AccountManagementFailure,
    ));
  }

  /// Create a copy of AccountManagementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountManagementFailureCopyWith<$Res> get failure {
    return $AccountManagementFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

// dart format on
