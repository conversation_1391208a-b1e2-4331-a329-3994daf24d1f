import 'package:account_management/domain/facade/account_management_facade.dart';
import 'package:fpdart/fpdart.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:injectable/injectable.dart';
import '../../domain/failure/account_management_failures.dart';
import '../../domain/model/account_details_model.dart';
part 'account_management_event.dart';
part 'account_management_state.dart';
part 'account_management_bloc.freezed.dart';

// This class manages the account-related operations such as updating account details, password, email, phone number, and profile picture.
@injectable
class AccountManagementBloc extends Bloc<AccountManagementEvent, AccountManagementState> {
  final AccountManagementFacade _accountManagementFacade;

  // Constructor initializes the bloc with an initial state and sets up event handlers.
  AccountManagementBloc(this._accountManagementFacade) : super(const AccountManagementState.initial()) {
    on<UpdateAccountDetails>(_onUpdateAccountDetails);
    on<UpdateAccountPassword>(_onUpdateAccountPassword);
    on<UpdateAccountEmail>(_onUpdateAccountEmail);
    on<UpdateAccountPhoneNumber>(_onUpdateAccountPhoneNumber);
    on<UpdateProfilePicture>(_onUpdateProfilePicture);
    on<DeleteProfilePicture>(_onDeleteProfilePicture);
  }

  // Handles the event of updating account details.
  Future<void> _onUpdateAccountDetails(UpdateAccountDetails event, Emitter<AccountManagementState> emit) async {
    emit(const AccountManagementState.updating());
    final result = await _accountManagementFacade.updateAccountDetails(event.accountDetails);
    result.fold(onFailure: (failure) {
      emit(AccountManagementState.updateFailure(failure));
    }, onSuccess: (_) {
      emit(const AccountManagementState.updated());
    });
  }

  // Handles the event of updating the account password.
  Future<void> _onUpdateAccountPassword(UpdateAccountPassword event, Emitter<AccountManagementState> emit) async {
    emit(const AccountManagementState.updating());
    final result = await _accountManagementFacade.updateAccountPassword(event.password);
    result.fold(onFailure: (failure) {
      emit(AccountManagementState.updateFailure(failure));
    }, onSuccess: (_) {
      emit(const AccountManagementState.updated());
    });
  }

  // Handles the event of updating the account email.
  Future<void> _onUpdateAccountEmail(UpdateAccountEmail event, Emitter<AccountManagementState> emit) async {
    emit(const AccountManagementState.updating());
    final result = await _accountManagementFacade.updateAccountEmail(event.email);
    result.fold(onFailure: (failure) {
      emit(AccountManagementState.updateFailure(failure));
    }, onSuccess: (_) {
      emit(const AccountManagementState.updated());
    });
  }

  // Handles the event of updating the account phone number.
  Future<void> _onUpdateAccountPhoneNumber(UpdateAccountPhoneNumber event, Emitter<AccountManagementState> emit) async {
    emit(const AccountManagementState.updating());
    final result = await _accountManagementFacade.updateAccountPhoneNumber(event.phoneNumber);
    result.fold(onFailure: (failure) {
      emit(AccountManagementState.updateFailure(failure));
    }, onSuccess: (_) {
      emit(const AccountManagementState.updated());
    });
  }

  // Handles the event of updating the profile picture.
  Future<void> _onUpdateProfilePicture(UpdateProfilePicture event, Emitter<AccountManagementState> emit) async {
    emit(const AccountManagementState.updating());
    final result = await _accountManagementFacade.updateProfilePicture(event.profilePicture);
    result.fold(onFailure: (failure) {
      emit(AccountManagementState.updateFailure(failure));
    }, onSuccess: (_) {
      emit(const AccountManagementState.updated());
    });
  }

  // Handles the event of deleting the profile picture.
  Future<void> _onDeleteProfilePicture(DeleteProfilePicture event, Emitter<AccountManagementState> emit) async {
    emit(const AccountManagementState.updating());
    final result = await _accountManagementFacade.deleteProfilePicture();
    result.fold(onFailure: (failure) {
      emit(AccountManagementState.updateFailure(failure));
    }, onSuccess: (_) {
      emit(const AccountManagementState.updated());
    });
  }
}