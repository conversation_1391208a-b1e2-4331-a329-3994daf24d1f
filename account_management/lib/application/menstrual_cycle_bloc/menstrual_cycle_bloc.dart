import 'dart:async';
import 'package:account_management/domain/facade/mestrual_cycle_facade.dart';
import 'package:account_management/domain/facade/period_tracking_facade.dart';
import 'package:account_management/domain/failure/period_tracking_failure.dart';
import 'package:account_management/repository/menstrual_cycle_data_model.dart';
import 'package:doso/doso.dart';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
part 'menstrual_cycle_event.dart';
part 'menstrual_cycle_state.dart';
part 'menstrual_cycle_bloc.freezed.dart';

@injectable
class MenstrualCycleBloc extends Bloc<MenstrualCycleEvent, MenstrualCycleState> {
  final MenstrualCycleFacade _menstrualCycleFacade;
  StreamSubscription<Do<PeriodTrackingFailure, MenstrualCycleData>>? _menstrualCycleStreamSubscription;

  MenstrualCycleBloc(this._menstrualCycleFacade) : super(const MenstrualCycleState.loading()) {
    on<_WatchAllStarted>(_onWatchAllStarted);
    on<_DataReceived>(_onDataReceived);
     on<_GetInitialData>(_onGetInitialData);
  }

  Future<void> _onGetInitialData(
      _GetInitialData event,
      Emitter<MenstrualCycleState> emit,
      ) async {
    emit(const MenstrualCycleState.loading());
    add(_DataReceived(await _menstrualCycleFacade.getInitialMenstrualCycleData()));
  }

  Future<void> _onWatchAllStarted(
      _WatchAllStarted event,
      Emitter<MenstrualCycleState> emit,
      ) async {
    emit(const MenstrualCycleState.loading());
    await _menstrualCycleStreamSubscription?.cancel();
    _menstrualCycleStreamSubscription = _menstrualCycleFacade.getMenstrualCycle().listen(
          (failureOrMenstrualCycleData) {
        add(MenstrualCycleEvent.dataReceived(failureOrMenstrualCycleData));
      },
    );
  }

  void _onDataReceived(
      _DataReceived event,
      Emitter<MenstrualCycleState> emit,
      ) {
    event.failureOrMenstrualCycleData.fold(
      onFailure: (failure) => emit(MenstrualCycleState.initial()),
      onSuccess: (menstrualCycleData) {
        final newState = MenstrualCycleState.data(
          currentCycleDay: menstrualCycleData.currentCycleDay,
          periodDays: menstrualCycleData.periodDays,
          cycleLength: menstrualCycleData.cycleLength,
          ovulationDayStart: menstrualCycleData.ovulationDayStart,
          ovulationDaysLength: menstrualCycleData.ovulationDaysLength,
        );
        emit(newState);
      },
    );
  }

  @override
  Future<void> close() {
    _menstrualCycleStreamSubscription?.cancel();
    return super.close();
  }
}