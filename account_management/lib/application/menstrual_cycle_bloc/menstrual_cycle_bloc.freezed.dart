// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'menstrual_cycle_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MenstrualCycleEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is MenstrualCycleEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MenstrualCycleEvent()';
  }
}

/// @nodoc
class $MenstrualCycleEventCopyWith<$Res> {
  $MenstrualCycleEventCopyWith(
      MenstrualCycleEvent _, $Res Function(MenstrualCycleEvent) __);
}

/// Adds pattern-matching-related methods to [MenstrualCycleEvent].
extension MenstrualCycleEventPatterns on MenstrualCycleEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_GetInitialData value)? getInitialData,
    TResult Function(_WatchAllStarted value)? watchAllStarted,
    TResult Function(_DataReceived value)? dataReceived,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GetInitialData() when getInitialData != null:
        return getInitialData(_that);
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted(_that);
      case _DataReceived() when dataReceived != null:
        return dataReceived(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_GetInitialData value) getInitialData,
    required TResult Function(_WatchAllStarted value) watchAllStarted,
    required TResult Function(_DataReceived value) dataReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _GetInitialData():
        return getInitialData(_that);
      case _WatchAllStarted():
        return watchAllStarted(_that);
      case _DataReceived():
        return dataReceived(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_GetInitialData value)? getInitialData,
    TResult? Function(_WatchAllStarted value)? watchAllStarted,
    TResult? Function(_DataReceived value)? dataReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _GetInitialData() when getInitialData != null:
        return getInitialData(_that);
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted(_that);
      case _DataReceived() when dataReceived != null:
        return dataReceived(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? getInitialData,
    TResult Function()? watchAllStarted,
    TResult Function(
            Do<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)?
        dataReceived,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GetInitialData() when getInitialData != null:
        return getInitialData();
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted();
      case _DataReceived() when dataReceived != null:
        return dataReceived(_that.failureOrMenstrualCycleData);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() getInitialData,
    required TResult Function() watchAllStarted,
    required TResult Function(
            Do<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)
        dataReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _GetInitialData():
        return getInitialData();
      case _WatchAllStarted():
        return watchAllStarted();
      case _DataReceived():
        return dataReceived(_that.failureOrMenstrualCycleData);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? getInitialData,
    TResult? Function()? watchAllStarted,
    TResult? Function(
            Do<PeriodTrackingFailure, MenstrualCycleData>
                failureOrMenstrualCycleData)?
        dataReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _GetInitialData() when getInitialData != null:
        return getInitialData();
      case _WatchAllStarted() when watchAllStarted != null:
        return watchAllStarted();
      case _DataReceived() when dataReceived != null:
        return dataReceived(_that.failureOrMenstrualCycleData);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _GetInitialData implements MenstrualCycleEvent {
  const _GetInitialData();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _GetInitialData);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MenstrualCycleEvent.getInitialData()';
  }
}

/// @nodoc

class _WatchAllStarted implements MenstrualCycleEvent {
  const _WatchAllStarted();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _WatchAllStarted);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MenstrualCycleEvent.watchAllStarted()';
  }
}

/// @nodoc

class _DataReceived implements MenstrualCycleEvent {
  const _DataReceived(this.failureOrMenstrualCycleData);

  final Do<PeriodTrackingFailure, MenstrualCycleData>
      failureOrMenstrualCycleData;

  /// Create a copy of MenstrualCycleEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DataReceivedCopyWith<_DataReceived> get copyWith =>
      __$DataReceivedCopyWithImpl<_DataReceived>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DataReceived &&
            (identical(other.failureOrMenstrualCycleData,
                    failureOrMenstrualCycleData) ||
                other.failureOrMenstrualCycleData ==
                    failureOrMenstrualCycleData));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrMenstrualCycleData);

  @override
  String toString() {
    return 'MenstrualCycleEvent.dataReceived(failureOrMenstrualCycleData: $failureOrMenstrualCycleData)';
  }
}

/// @nodoc
abstract mixin class _$DataReceivedCopyWith<$Res>
    implements $MenstrualCycleEventCopyWith<$Res> {
  factory _$DataReceivedCopyWith(
          _DataReceived value, $Res Function(_DataReceived) _then) =
      __$DataReceivedCopyWithImpl;
  @useResult
  $Res call(
      {Do<PeriodTrackingFailure, MenstrualCycleData>
          failureOrMenstrualCycleData});
}

/// @nodoc
class __$DataReceivedCopyWithImpl<$Res>
    implements _$DataReceivedCopyWith<$Res> {
  __$DataReceivedCopyWithImpl(this._self, this._then);

  final _DataReceived _self;
  final $Res Function(_DataReceived) _then;

  /// Create a copy of MenstrualCycleEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureOrMenstrualCycleData = null,
  }) {
    return _then(_DataReceived(
      null == failureOrMenstrualCycleData
          ? _self.failureOrMenstrualCycleData
          : failureOrMenstrualCycleData // ignore: cast_nullable_to_non_nullable
              as Do<PeriodTrackingFailure, MenstrualCycleData>,
    ));
  }
}

/// @nodoc
mixin _$MenstrualCycleState {
  @override
  String toString() {
    return 'MenstrualCycleState()';
  }
}

/// @nodoc
class $MenstrualCycleStateCopyWith<$Res> {
  $MenstrualCycleStateCopyWith(
      MenstrualCycleState _, $Res Function(MenstrualCycleState) __);
}

/// Adds pattern-matching-related methods to [MenstrualCycleState].
extension MenstrualCycleStatePatterns on MenstrualCycleState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Loading value)? loading,
    TResult Function(_Data value)? data,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Loading() when loading != null:
        return loading(_that);
      case _Data() when data != null:
        return data(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Loading value) loading,
    required TResult Function(_Data value) data,
  }) {
    final _that = this;
    switch (_that) {
      case _Loading():
        return loading(_that);
      case _Data():
        return data(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Data value)? data,
  }) {
    final _that = this;
    switch (_that) {
      case _Loading() when loading != null:
        return loading(_that);
      case _Data() when data != null:
        return data(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loading,
    TResult Function(int currentCycleDay, int periodDays, int cycleLength,
            int ovulationDayStart, int ovulationDaysLength)?
        data,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Loading() when loading != null:
        return loading();
      case _Data() when data != null:
        return data(_that.currentCycleDay, _that.periodDays, _that.cycleLength,
            _that.ovulationDayStart, _that.ovulationDaysLength);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loading,
    required TResult Function(int currentCycleDay, int periodDays,
            int cycleLength, int ovulationDayStart, int ovulationDaysLength)
        data,
  }) {
    final _that = this;
    switch (_that) {
      case _Loading():
        return loading();
      case _Data():
        return data(_that.currentCycleDay, _that.periodDays, _that.cycleLength,
            _that.ovulationDayStart, _that.ovulationDaysLength);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loading,
    TResult? Function(int currentCycleDay, int periodDays, int cycleLength,
            int ovulationDayStart, int ovulationDaysLength)?
        data,
  }) {
    final _that = this;
    switch (_that) {
      case _Loading() when loading != null:
        return loading();
      case _Data() when data != null:
        return data(_that.currentCycleDay, _that.periodDays, _that.cycleLength,
            _that.ovulationDayStart, _that.ovulationDaysLength);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Loading extends MenstrualCycleState {
  const _Loading() : super._();

  @override
  String toString() {
    return 'MenstrualCycleState.loading()';
  }
}

/// @nodoc

class _Data extends MenstrualCycleState {
  const _Data(
      {required this.currentCycleDay,
      required this.periodDays,
      required this.cycleLength,
      required this.ovulationDayStart,
      required this.ovulationDaysLength})
      : super._();

  final int currentCycleDay;
  final int periodDays;
  final int cycleLength;
  final int ovulationDayStart;
  final int ovulationDaysLength;

  /// Create a copy of MenstrualCycleState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DataCopyWith<_Data> get copyWith =>
      __$DataCopyWithImpl<_Data>(this, _$identity);

  @override
  String toString() {
    return 'MenstrualCycleState.data(currentCycleDay: $currentCycleDay, periodDays: $periodDays, cycleLength: $cycleLength, ovulationDayStart: $ovulationDayStart, ovulationDaysLength: $ovulationDaysLength)';
  }
}

/// @nodoc
abstract mixin class _$DataCopyWith<$Res>
    implements $MenstrualCycleStateCopyWith<$Res> {
  factory _$DataCopyWith(_Data value, $Res Function(_Data) _then) =
      __$DataCopyWithImpl;
  @useResult
  $Res call(
      {int currentCycleDay,
      int periodDays,
      int cycleLength,
      int ovulationDayStart,
      int ovulationDaysLength});
}

/// @nodoc
class __$DataCopyWithImpl<$Res> implements _$DataCopyWith<$Res> {
  __$DataCopyWithImpl(this._self, this._then);

  final _Data _self;
  final $Res Function(_Data) _then;

  /// Create a copy of MenstrualCycleState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? currentCycleDay = null,
    Object? periodDays = null,
    Object? cycleLength = null,
    Object? ovulationDayStart = null,
    Object? ovulationDaysLength = null,
  }) {
    return _then(_Data(
      currentCycleDay: null == currentCycleDay
          ? _self.currentCycleDay
          : currentCycleDay // ignore: cast_nullable_to_non_nullable
              as int,
      periodDays: null == periodDays
          ? _self.periodDays
          : periodDays // ignore: cast_nullable_to_non_nullable
              as int,
      cycleLength: null == cycleLength
          ? _self.cycleLength
          : cycleLength // ignore: cast_nullable_to_non_nullable
              as int,
      ovulationDayStart: null == ovulationDayStart
          ? _self.ovulationDayStart
          : ovulationDayStart // ignore: cast_nullable_to_non_nullable
              as int,
      ovulationDaysLength: null == ovulationDaysLength
          ? _self.ovulationDaysLength
          : ovulationDaysLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

// dart format on
