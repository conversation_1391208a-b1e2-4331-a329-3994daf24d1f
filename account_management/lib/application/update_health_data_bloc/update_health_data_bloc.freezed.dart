// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'update_health_data_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UpdateHealthDataEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is UpdateHealthDataEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'UpdateHealthDataEvent()';
  }
}

/// @nodoc
class $UpdateHealthDataEventCopyWith<$Res> {
  $UpdateHealthDataEventCopyWith(
      UpdateHealthDataEvent _, $Res Function(UpdateHealthDataEvent) __);
}

/// Adds pattern-matching-related methods to [UpdateHealthDataEvent].
extension UpdateHealthDataEventPatterns on UpdateHealthDataEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateCycleLength value)? updateCycleLength,
    TResult Function(UpdatePeriodLength value)? updatePeriodLength,
    TResult Function(UpdateOvulationDate value)? updateOvulationDate,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case UpdateCycleLength() when updateCycleLength != null:
        return updateCycleLength(_that);
      case UpdatePeriodLength() when updatePeriodLength != null:
        return updatePeriodLength(_that);
      case UpdateOvulationDate() when updateOvulationDate != null:
        return updateOvulationDate(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateCycleLength value) updateCycleLength,
    required TResult Function(UpdatePeriodLength value) updatePeriodLength,
    required TResult Function(UpdateOvulationDate value) updateOvulationDate,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateCycleLength():
        return updateCycleLength(_that);
      case UpdatePeriodLength():
        return updatePeriodLength(_that);
      case UpdateOvulationDate():
        return updateOvulationDate(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateCycleLength value)? updateCycleLength,
    TResult? Function(UpdatePeriodLength value)? updatePeriodLength,
    TResult? Function(UpdateOvulationDate value)? updateOvulationDate,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateCycleLength() when updateCycleLength != null:
        return updateCycleLength(_that);
      case UpdatePeriodLength() when updatePeriodLength != null:
        return updatePeriodLength(_that);
      case UpdateOvulationDate() when updateOvulationDate != null:
        return updateOvulationDate(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int cycleLength)? updateCycleLength,
    TResult Function(int periodLength)? updatePeriodLength,
    TResult Function(DateTime ovulationDate)? updateOvulationDate,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case UpdateCycleLength() when updateCycleLength != null:
        return updateCycleLength(_that.cycleLength);
      case UpdatePeriodLength() when updatePeriodLength != null:
        return updatePeriodLength(_that.periodLength);
      case UpdateOvulationDate() when updateOvulationDate != null:
        return updateOvulationDate(_that.ovulationDate);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int cycleLength) updateCycleLength,
    required TResult Function(int periodLength) updatePeriodLength,
    required TResult Function(DateTime ovulationDate) updateOvulationDate,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateCycleLength():
        return updateCycleLength(_that.cycleLength);
      case UpdatePeriodLength():
        return updatePeriodLength(_that.periodLength);
      case UpdateOvulationDate():
        return updateOvulationDate(_that.ovulationDate);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int cycleLength)? updateCycleLength,
    TResult? Function(int periodLength)? updatePeriodLength,
    TResult? Function(DateTime ovulationDate)? updateOvulationDate,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateCycleLength() when updateCycleLength != null:
        return updateCycleLength(_that.cycleLength);
      case UpdatePeriodLength() when updatePeriodLength != null:
        return updatePeriodLength(_that.periodLength);
      case UpdateOvulationDate() when updateOvulationDate != null:
        return updateOvulationDate(_that.ovulationDate);
      case _:
        return null;
    }
  }
}

/// @nodoc

class UpdateCycleLength implements UpdateHealthDataEvent {
  const UpdateCycleLength(this.cycleLength);

  final int cycleLength;

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateCycleLengthCopyWith<UpdateCycleLength> get copyWith =>
      _$UpdateCycleLengthCopyWithImpl<UpdateCycleLength>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateCycleLength &&
            (identical(other.cycleLength, cycleLength) ||
                other.cycleLength == cycleLength));
  }

  @override
  int get hashCode => Object.hash(runtimeType, cycleLength);

  @override
  String toString() {
    return 'UpdateHealthDataEvent.updateCycleLength(cycleLength: $cycleLength)';
  }
}

/// @nodoc
abstract mixin class $UpdateCycleLengthCopyWith<$Res>
    implements $UpdateHealthDataEventCopyWith<$Res> {
  factory $UpdateCycleLengthCopyWith(
          UpdateCycleLength value, $Res Function(UpdateCycleLength) _then) =
      _$UpdateCycleLengthCopyWithImpl;
  @useResult
  $Res call({int cycleLength});
}

/// @nodoc
class _$UpdateCycleLengthCopyWithImpl<$Res>
    implements $UpdateCycleLengthCopyWith<$Res> {
  _$UpdateCycleLengthCopyWithImpl(this._self, this._then);

  final UpdateCycleLength _self;
  final $Res Function(UpdateCycleLength) _then;

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? cycleLength = null,
  }) {
    return _then(UpdateCycleLength(
      null == cycleLength
          ? _self.cycleLength
          : cycleLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class UpdatePeriodLength implements UpdateHealthDataEvent {
  const UpdatePeriodLength(this.periodLength);

  final int periodLength;

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdatePeriodLengthCopyWith<UpdatePeriodLength> get copyWith =>
      _$UpdatePeriodLengthCopyWithImpl<UpdatePeriodLength>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdatePeriodLength &&
            (identical(other.periodLength, periodLength) ||
                other.periodLength == periodLength));
  }

  @override
  int get hashCode => Object.hash(runtimeType, periodLength);

  @override
  String toString() {
    return 'UpdateHealthDataEvent.updatePeriodLength(periodLength: $periodLength)';
  }
}

/// @nodoc
abstract mixin class $UpdatePeriodLengthCopyWith<$Res>
    implements $UpdateHealthDataEventCopyWith<$Res> {
  factory $UpdatePeriodLengthCopyWith(
          UpdatePeriodLength value, $Res Function(UpdatePeriodLength) _then) =
      _$UpdatePeriodLengthCopyWithImpl;
  @useResult
  $Res call({int periodLength});
}

/// @nodoc
class _$UpdatePeriodLengthCopyWithImpl<$Res>
    implements $UpdatePeriodLengthCopyWith<$Res> {
  _$UpdatePeriodLengthCopyWithImpl(this._self, this._then);

  final UpdatePeriodLength _self;
  final $Res Function(UpdatePeriodLength) _then;

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? periodLength = null,
  }) {
    return _then(UpdatePeriodLength(
      null == periodLength
          ? _self.periodLength
          : periodLength // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class UpdateOvulationDate implements UpdateHealthDataEvent {
  const UpdateOvulationDate(this.ovulationDate);

  final DateTime ovulationDate;

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateOvulationDateCopyWith<UpdateOvulationDate> get copyWith =>
      _$UpdateOvulationDateCopyWithImpl<UpdateOvulationDate>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateOvulationDate &&
            (identical(other.ovulationDate, ovulationDate) ||
                other.ovulationDate == ovulationDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, ovulationDate);

  @override
  String toString() {
    return 'UpdateHealthDataEvent.updateOvulationDate(ovulationDate: $ovulationDate)';
  }
}

/// @nodoc
abstract mixin class $UpdateOvulationDateCopyWith<$Res>
    implements $UpdateHealthDataEventCopyWith<$Res> {
  factory $UpdateOvulationDateCopyWith(
          UpdateOvulationDate value, $Res Function(UpdateOvulationDate) _then) =
      _$UpdateOvulationDateCopyWithImpl;
  @useResult
  $Res call({DateTime ovulationDate});
}

/// @nodoc
class _$UpdateOvulationDateCopyWithImpl<$Res>
    implements $UpdateOvulationDateCopyWith<$Res> {
  _$UpdateOvulationDateCopyWithImpl(this._self, this._then);

  final UpdateOvulationDate _self;
  final $Res Function(UpdateOvulationDate) _then;

  /// Create a copy of UpdateHealthDataEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? ovulationDate = null,
  }) {
    return _then(UpdateOvulationDate(
      null == ovulationDate
          ? _self.ovulationDate
          : ovulationDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
mixin _$UpdateHealthDataState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is UpdateHealthDataState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'UpdateHealthDataState()';
  }
}

/// @nodoc
class $UpdateHealthDataStateCopyWith<$Res> {
  $UpdateHealthDataStateCopyWith(
      UpdateHealthDataState _, $Res Function(UpdateHealthDataState) __);
}

/// Adds pattern-matching-related methods to [UpdateHealthDataState].
extension UpdateHealthDataStatePatterns on UpdateHealthDataState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_UpdatedHealthData value)? updatedHealthData,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _UpdatedHealthData() when updatedHealthData != null:
        return updatedHealthData(_that);
      case _Failure() when failure != null:
        return failure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_UpdatedHealthData value) updatedHealthData,
    required TResult Function(_Failure value) failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _Loading():
        return loading(_that);
      case _UpdatedHealthData():
        return updatedHealthData(_that);
      case _Failure():
        return failure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_UpdatedHealthData value)? updatedHealthData,
    TResult? Function(_Failure value)? failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _UpdatedHealthData() when updatedHealthData != null:
        return updatedHealthData(_that);
      case _Failure() when failure != null:
        return failure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(String type)? updatedHealthData,
    TResult Function(HealthDataFailure failure)? failure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _UpdatedHealthData() when updatedHealthData != null:
        return updatedHealthData(_that.type);
      case _Failure() when failure != null:
        return failure(_that.failure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(String type) updatedHealthData,
    required TResult Function(HealthDataFailure failure) failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _Loading():
        return loading();
      case _UpdatedHealthData():
        return updatedHealthData(_that.type);
      case _Failure():
        return failure(_that.failure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(String type)? updatedHealthData,
    TResult? Function(HealthDataFailure failure)? failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _UpdatedHealthData() when updatedHealthData != null:
        return updatedHealthData(_that.type);
      case _Failure() when failure != null:
        return failure(_that.failure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements UpdateHealthDataState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'UpdateHealthDataState.initial()';
  }
}

/// @nodoc

class _Loading implements UpdateHealthDataState {
  const _Loading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Loading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'UpdateHealthDataState.loading()';
  }
}

/// @nodoc

class _UpdatedHealthData implements UpdateHealthDataState {
  const _UpdatedHealthData(this.type);

  final String type;

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdatedHealthDataCopyWith<_UpdatedHealthData> get copyWith =>
      __$UpdatedHealthDataCopyWithImpl<_UpdatedHealthData>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdatedHealthData &&
            (identical(other.type, type) || other.type == type));
  }

  @override
  int get hashCode => Object.hash(runtimeType, type);

  @override
  String toString() {
    return 'UpdateHealthDataState.updatedHealthData(type: $type)';
  }
}

/// @nodoc
abstract mixin class _$UpdatedHealthDataCopyWith<$Res>
    implements $UpdateHealthDataStateCopyWith<$Res> {
  factory _$UpdatedHealthDataCopyWith(
          _UpdatedHealthData value, $Res Function(_UpdatedHealthData) _then) =
      __$UpdatedHealthDataCopyWithImpl;
  @useResult
  $Res call({String type});
}

/// @nodoc
class __$UpdatedHealthDataCopyWithImpl<$Res>
    implements _$UpdatedHealthDataCopyWith<$Res> {
  __$UpdatedHealthDataCopyWithImpl(this._self, this._then);

  final _UpdatedHealthData _self;
  final $Res Function(_UpdatedHealthData) _then;

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? type = null,
  }) {
    return _then(_UpdatedHealthData(
      null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _Failure implements UpdateHealthDataState {
  const _Failure(this.failure);

  final HealthDataFailure failure;

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FailureCopyWith<_Failure> get copyWith =>
      __$FailureCopyWithImpl<_Failure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Failure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'UpdateHealthDataState.failure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class _$FailureCopyWith<$Res>
    implements $UpdateHealthDataStateCopyWith<$Res> {
  factory _$FailureCopyWith(_Failure value, $Res Function(_Failure) _then) =
      __$FailureCopyWithImpl;
  @useResult
  $Res call({HealthDataFailure failure});

  $HealthDataFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$FailureCopyWithImpl<$Res> implements _$FailureCopyWith<$Res> {
  __$FailureCopyWithImpl(this._self, this._then);

  final _Failure _self;
  final $Res Function(_Failure) _then;

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(_Failure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as HealthDataFailure,
    ));
  }

  /// Create a copy of UpdateHealthDataState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $HealthDataFailureCopyWith<$Res> get failure {
    return $HealthDataFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

// dart format on
