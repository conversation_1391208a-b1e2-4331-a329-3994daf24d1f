// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'symptom_tracking_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SymptomTrackingEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SymptomTrackingEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomTrackingEvent()';
  }
}

/// @nodoc
class $SymptomTrackingEventCopyWith<$Res> {
  $SymptomTrackingEventCopyWith(
      SymptomTrackingEvent _, $Res Function(SymptomTrackingEvent) __);
}

/// Adds pattern-matching-related methods to [SymptomTrackingEvent].
extension SymptomTrackingEventPatterns on SymptomTrackingEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadSymptomData value)? loadSymptomData,
    TResult Function(_SaveSymptomData value)? saveSymptomData,
    TResult Function(_DateChanged value)? dateChanged,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LoadSymptomData() when loadSymptomData != null:
        return loadSymptomData(_that);
      case _SaveSymptomData() when saveSymptomData != null:
        return saveSymptomData(_that);
      case _DateChanged() when dateChanged != null:
        return dateChanged(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadSymptomData value) loadSymptomData,
    required TResult Function(_SaveSymptomData value) saveSymptomData,
    required TResult Function(_DateChanged value) dateChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _LoadSymptomData():
        return loadSymptomData(_that);
      case _SaveSymptomData():
        return saveSymptomData(_that);
      case _DateChanged():
        return dateChanged(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadSymptomData value)? loadSymptomData,
    TResult? Function(_SaveSymptomData value)? saveSymptomData,
    TResult? Function(_DateChanged value)? dateChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _LoadSymptomData() when loadSymptomData != null:
        return loadSymptomData(_that);
      case _SaveSymptomData() when saveSymptomData != null:
        return saveSymptomData(_that);
      case _DateChanged() when dateChanged != null:
        return dateChanged(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? loadSymptomData,
    TResult Function(DateTime date, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)?
        saveSymptomData,
    TResult Function(DateTime newDate)? dateChanged,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _LoadSymptomData() when loadSymptomData != null:
        return loadSymptomData(_that.date);
      case _SaveSymptomData() when saveSymptomData != null:
        return saveSymptomData(
            _that.date, _that.symptoms, _that.painLevel, _that.flowLevel);
      case _DateChanged() when dateChanged != null:
        return dateChanged(_that.newDate);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) loadSymptomData,
    required TResult Function(DateTime date, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)
        saveSymptomData,
    required TResult Function(DateTime newDate) dateChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _LoadSymptomData():
        return loadSymptomData(_that.date);
      case _SaveSymptomData():
        return saveSymptomData(
            _that.date, _that.symptoms, _that.painLevel, _that.flowLevel);
      case _DateChanged():
        return dateChanged(_that.newDate);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? loadSymptomData,
    TResult? Function(DateTime date, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)?
        saveSymptomData,
    TResult? Function(DateTime newDate)? dateChanged,
  }) {
    final _that = this;
    switch (_that) {
      case _LoadSymptomData() when loadSymptomData != null:
        return loadSymptomData(_that.date);
      case _SaveSymptomData() when saveSymptomData != null:
        return saveSymptomData(
            _that.date, _that.symptoms, _that.painLevel, _that.flowLevel);
      case _DateChanged() when dateChanged != null:
        return dateChanged(_that.newDate);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _LoadSymptomData implements SymptomTrackingEvent {
  const _LoadSymptomData({required this.date});

  final DateTime date;

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadSymptomDataCopyWith<_LoadSymptomData> get copyWith =>
      __$LoadSymptomDataCopyWithImpl<_LoadSymptomData>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoadSymptomData &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date);

  @override
  String toString() {
    return 'SymptomTrackingEvent.loadSymptomData(date: $date)';
  }
}

/// @nodoc
abstract mixin class _$LoadSymptomDataCopyWith<$Res>
    implements $SymptomTrackingEventCopyWith<$Res> {
  factory _$LoadSymptomDataCopyWith(
          _LoadSymptomData value, $Res Function(_LoadSymptomData) _then) =
      __$LoadSymptomDataCopyWithImpl;
  @useResult
  $Res call({DateTime date});
}

/// @nodoc
class __$LoadSymptomDataCopyWithImpl<$Res>
    implements _$LoadSymptomDataCopyWith<$Res> {
  __$LoadSymptomDataCopyWithImpl(this._self, this._then);

  final _LoadSymptomData _self;
  final $Res Function(_LoadSymptomData) _then;

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? date = null,
  }) {
    return _then(_LoadSymptomData(
      date: null == date
          ? _self.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _SaveSymptomData implements SymptomTrackingEvent {
  const _SaveSymptomData(
      {required this.date,
      final List<SymptomModel>? symptoms,
      this.painLevel,
      this.flowLevel})
      : _symptoms = symptoms;

  final DateTime date;
  final List<SymptomModel>? _symptoms;
  List<SymptomModel>? get symptoms {
    final value = _symptoms;
    if (value == null) return null;
    if (_symptoms is EqualUnmodifiableListView) return _symptoms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final int? painLevel;
  final int? flowLevel;

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SaveSymptomDataCopyWith<_SaveSymptomData> get copyWith =>
      __$SaveSymptomDataCopyWithImpl<_SaveSymptomData>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SaveSymptomData &&
            (identical(other.date, date) || other.date == date) &&
            const DeepCollectionEquality().equals(other._symptoms, _symptoms) &&
            (identical(other.painLevel, painLevel) ||
                other.painLevel == painLevel) &&
            (identical(other.flowLevel, flowLevel) ||
                other.flowLevel == flowLevel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date,
      const DeepCollectionEquality().hash(_symptoms), painLevel, flowLevel);

  @override
  String toString() {
    return 'SymptomTrackingEvent.saveSymptomData(date: $date, symptoms: $symptoms, painLevel: $painLevel, flowLevel: $flowLevel)';
  }
}

/// @nodoc
abstract mixin class _$SaveSymptomDataCopyWith<$Res>
    implements $SymptomTrackingEventCopyWith<$Res> {
  factory _$SaveSymptomDataCopyWith(
          _SaveSymptomData value, $Res Function(_SaveSymptomData) _then) =
      __$SaveSymptomDataCopyWithImpl;
  @useResult
  $Res call(
      {DateTime date,
      List<SymptomModel>? symptoms,
      int? painLevel,
      int? flowLevel});
}

/// @nodoc
class __$SaveSymptomDataCopyWithImpl<$Res>
    implements _$SaveSymptomDataCopyWith<$Res> {
  __$SaveSymptomDataCopyWithImpl(this._self, this._then);

  final _SaveSymptomData _self;
  final $Res Function(_SaveSymptomData) _then;

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? date = null,
    Object? symptoms = freezed,
    Object? painLevel = freezed,
    Object? flowLevel = freezed,
  }) {
    return _then(_SaveSymptomData(
      date: null == date
          ? _self.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      symptoms: freezed == symptoms
          ? _self._symptoms
          : symptoms // ignore: cast_nullable_to_non_nullable
              as List<SymptomModel>?,
      painLevel: freezed == painLevel
          ? _self.painLevel
          : painLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      flowLevel: freezed == flowLevel
          ? _self.flowLevel
          : flowLevel // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _DateChanged implements SymptomTrackingEvent {
  const _DateChanged({required this.newDate});

  final DateTime newDate;

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DateChangedCopyWith<_DateChanged> get copyWith =>
      __$DateChangedCopyWithImpl<_DateChanged>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DateChanged &&
            (identical(other.newDate, newDate) || other.newDate == newDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, newDate);

  @override
  String toString() {
    return 'SymptomTrackingEvent.dateChanged(newDate: $newDate)';
  }
}

/// @nodoc
abstract mixin class _$DateChangedCopyWith<$Res>
    implements $SymptomTrackingEventCopyWith<$Res> {
  factory _$DateChangedCopyWith(
          _DateChanged value, $Res Function(_DateChanged) _then) =
      __$DateChangedCopyWithImpl;
  @useResult
  $Res call({DateTime newDate});
}

/// @nodoc
class __$DateChangedCopyWithImpl<$Res> implements _$DateChangedCopyWith<$Res> {
  __$DateChangedCopyWithImpl(this._self, this._then);

  final _DateChanged _self;
  final $Res Function(_DateChanged) _then;

  /// Create a copy of SymptomTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? newDate = null,
  }) {
    return _then(_DateChanged(
      newDate: null == newDate
          ? _self.newDate
          : newDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
mixin _$SymptomTrackingState {
  DateTime? get selectedDate;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SymptomTrackingStateCopyWith<SymptomTrackingState> get copyWith =>
      _$SymptomTrackingStateCopyWithImpl<SymptomTrackingState>(
          this as SymptomTrackingState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SymptomTrackingState &&
            (identical(other.selectedDate, selectedDate) ||
                other.selectedDate == selectedDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedDate);

  @override
  String toString() {
    return 'SymptomTrackingState(selectedDate: $selectedDate)';
  }
}

/// @nodoc
abstract mixin class $SymptomTrackingStateCopyWith<$Res> {
  factory $SymptomTrackingStateCopyWith(SymptomTrackingState value,
          $Res Function(SymptomTrackingState) _then) =
      _$SymptomTrackingStateCopyWithImpl;
  @useResult
  $Res call({DateTime selectedDate});
}

/// @nodoc
class _$SymptomTrackingStateCopyWithImpl<$Res>
    implements $SymptomTrackingStateCopyWith<$Res> {
  _$SymptomTrackingStateCopyWithImpl(this._self, this._then);

  final SymptomTrackingState _self;
  final $Res Function(SymptomTrackingState) _then;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? selectedDate = null,
  }) {
    return _then(_self.copyWith(
      selectedDate: null == selectedDate
          ? _self.selectedDate!
          : selectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// Adds pattern-matching-related methods to [SymptomTrackingState].
extension SymptomTrackingStatePatterns on SymptomTrackingState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Loaded value)? loaded,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Loaded() when loaded != null:
        return loaded(_that);
      case _Success() when success != null:
        return success(_that);
      case _Failure() when failure != null:
        return failure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Loaded value) loaded,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _Loading():
        return loading(_that);
      case _Loaded():
        return loaded(_that);
      case _Success():
        return success(_that);
      case _Failure():
        return failure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Loaded value)? loaded,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Loaded() when loaded != null:
        return loaded(_that);
      case _Success() when success != null:
        return success(_that);
      case _Failure() when failure != null:
        return failure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime? selectedDate)? initial,
    TResult Function(DateTime? selectedDate)? loading,
    TResult Function(DateTime selectedDate, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)?
        loaded,
    TResult Function(DateTime selectedDate)? success,
    TResult Function(DateTime? selectedDate, PeriodTrackingFailure failure)?
        failure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that.selectedDate);
      case _Loading() when loading != null:
        return loading(_that.selectedDate);
      case _Loaded() when loaded != null:
        return loaded(_that.selectedDate, _that.symptoms, _that.painLevel,
            _that.flowLevel);
      case _Success() when success != null:
        return success(_that.selectedDate);
      case _Failure() when failure != null:
        return failure(_that.selectedDate, _that.failure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime? selectedDate) initial,
    required TResult Function(DateTime? selectedDate) loading,
    required TResult Function(DateTime selectedDate,
            List<SymptomModel>? symptoms, int? painLevel, int? flowLevel)
        loaded,
    required TResult Function(DateTime selectedDate) success,
    required TResult Function(
            DateTime? selectedDate, PeriodTrackingFailure failure)
        failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that.selectedDate);
      case _Loading():
        return loading(_that.selectedDate);
      case _Loaded():
        return loaded(_that.selectedDate, _that.symptoms, _that.painLevel,
            _that.flowLevel);
      case _Success():
        return success(_that.selectedDate);
      case _Failure():
        return failure(_that.selectedDate, _that.failure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime? selectedDate)? initial,
    TResult? Function(DateTime? selectedDate)? loading,
    TResult? Function(DateTime selectedDate, List<SymptomModel>? symptoms,
            int? painLevel, int? flowLevel)?
        loaded,
    TResult? Function(DateTime selectedDate)? success,
    TResult? Function(DateTime? selectedDate, PeriodTrackingFailure failure)?
        failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that.selectedDate);
      case _Loading() when loading != null:
        return loading(_that.selectedDate);
      case _Loaded() when loaded != null:
        return loaded(_that.selectedDate, _that.symptoms, _that.painLevel,
            _that.flowLevel);
      case _Success() when success != null:
        return success(_that.selectedDate);
      case _Failure() when failure != null:
        return failure(_that.selectedDate, _that.failure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements SymptomTrackingState {
  const _Initial({this.selectedDate});

  @override
  final DateTime? selectedDate;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InitialCopyWith<_Initial> get copyWith =>
      __$InitialCopyWithImpl<_Initial>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Initial &&
            (identical(other.selectedDate, selectedDate) ||
                other.selectedDate == selectedDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedDate);

  @override
  String toString() {
    return 'SymptomTrackingState.initial(selectedDate: $selectedDate)';
  }
}

/// @nodoc
abstract mixin class _$InitialCopyWith<$Res>
    implements $SymptomTrackingStateCopyWith<$Res> {
  factory _$InitialCopyWith(_Initial value, $Res Function(_Initial) _then) =
      __$InitialCopyWithImpl;
  @override
  @useResult
  $Res call({DateTime? selectedDate});
}

/// @nodoc
class __$InitialCopyWithImpl<$Res> implements _$InitialCopyWith<$Res> {
  __$InitialCopyWithImpl(this._self, this._then);

  final _Initial _self;
  final $Res Function(_Initial) _then;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedDate = freezed,
  }) {
    return _then(_Initial(
      selectedDate: freezed == selectedDate
          ? _self.selectedDate
          : selectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _Loading implements SymptomTrackingState {
  const _Loading({this.selectedDate});

  @override
  final DateTime? selectedDate;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadingCopyWith<_Loading> get copyWith =>
      __$LoadingCopyWithImpl<_Loading>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Loading &&
            (identical(other.selectedDate, selectedDate) ||
                other.selectedDate == selectedDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedDate);

  @override
  String toString() {
    return 'SymptomTrackingState.loading(selectedDate: $selectedDate)';
  }
}

/// @nodoc
abstract mixin class _$LoadingCopyWith<$Res>
    implements $SymptomTrackingStateCopyWith<$Res> {
  factory _$LoadingCopyWith(_Loading value, $Res Function(_Loading) _then) =
      __$LoadingCopyWithImpl;
  @override
  @useResult
  $Res call({DateTime? selectedDate});
}

/// @nodoc
class __$LoadingCopyWithImpl<$Res> implements _$LoadingCopyWith<$Res> {
  __$LoadingCopyWithImpl(this._self, this._then);

  final _Loading _self;
  final $Res Function(_Loading) _then;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedDate = freezed,
  }) {
    return _then(_Loading(
      selectedDate: freezed == selectedDate
          ? _self.selectedDate
          : selectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _Loaded implements SymptomTrackingState {
  const _Loaded(
      {required this.selectedDate,
      final List<SymptomModel>? symptoms,
      this.painLevel,
      this.flowLevel})
      : _symptoms = symptoms;

  @override
  final DateTime selectedDate;
  final List<SymptomModel>? _symptoms;
  List<SymptomModel>? get symptoms {
    final value = _symptoms;
    if (value == null) return null;
    if (_symptoms is EqualUnmodifiableListView) return _symptoms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final int? painLevel;
  final int? flowLevel;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadedCopyWith<_Loaded> get copyWith =>
      __$LoadedCopyWithImpl<_Loaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Loaded &&
            (identical(other.selectedDate, selectedDate) ||
                other.selectedDate == selectedDate) &&
            const DeepCollectionEquality().equals(other._symptoms, _symptoms) &&
            (identical(other.painLevel, painLevel) ||
                other.painLevel == painLevel) &&
            (identical(other.flowLevel, flowLevel) ||
                other.flowLevel == flowLevel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedDate,
      const DeepCollectionEquality().hash(_symptoms), painLevel, flowLevel);

  @override
  String toString() {
    return 'SymptomTrackingState.loaded(selectedDate: $selectedDate, symptoms: $symptoms, painLevel: $painLevel, flowLevel: $flowLevel)';
  }
}

/// @nodoc
abstract mixin class _$LoadedCopyWith<$Res>
    implements $SymptomTrackingStateCopyWith<$Res> {
  factory _$LoadedCopyWith(_Loaded value, $Res Function(_Loaded) _then) =
      __$LoadedCopyWithImpl;
  @override
  @useResult
  $Res call(
      {DateTime selectedDate,
      List<SymptomModel>? symptoms,
      int? painLevel,
      int? flowLevel});
}

/// @nodoc
class __$LoadedCopyWithImpl<$Res> implements _$LoadedCopyWith<$Res> {
  __$LoadedCopyWithImpl(this._self, this._then);

  final _Loaded _self;
  final $Res Function(_Loaded) _then;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedDate = null,
    Object? symptoms = freezed,
    Object? painLevel = freezed,
    Object? flowLevel = freezed,
  }) {
    return _then(_Loaded(
      selectedDate: null == selectedDate
          ? _self.selectedDate
          : selectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      symptoms: freezed == symptoms
          ? _self._symptoms
          : symptoms // ignore: cast_nullable_to_non_nullable
              as List<SymptomModel>?,
      painLevel: freezed == painLevel
          ? _self.painLevel
          : painLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      flowLevel: freezed == flowLevel
          ? _self.flowLevel
          : flowLevel // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc

class _Success implements SymptomTrackingState {
  const _Success({required this.selectedDate});

  @override
  final DateTime selectedDate;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SuccessCopyWith<_Success> get copyWith =>
      __$SuccessCopyWithImpl<_Success>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Success &&
            (identical(other.selectedDate, selectedDate) ||
                other.selectedDate == selectedDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedDate);

  @override
  String toString() {
    return 'SymptomTrackingState.success(selectedDate: $selectedDate)';
  }
}

/// @nodoc
abstract mixin class _$SuccessCopyWith<$Res>
    implements $SymptomTrackingStateCopyWith<$Res> {
  factory _$SuccessCopyWith(_Success value, $Res Function(_Success) _then) =
      __$SuccessCopyWithImpl;
  @override
  @useResult
  $Res call({DateTime selectedDate});
}

/// @nodoc
class __$SuccessCopyWithImpl<$Res> implements _$SuccessCopyWith<$Res> {
  __$SuccessCopyWithImpl(this._self, this._then);

  final _Success _self;
  final $Res Function(_Success) _then;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedDate = null,
  }) {
    return _then(_Success(
      selectedDate: null == selectedDate
          ? _self.selectedDate
          : selectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _Failure implements SymptomTrackingState {
  const _Failure({this.selectedDate, required this.failure});

  @override
  final DateTime? selectedDate;
  final PeriodTrackingFailure failure;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FailureCopyWith<_Failure> get copyWith =>
      __$FailureCopyWithImpl<_Failure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Failure &&
            (identical(other.selectedDate, selectedDate) ||
                other.selectedDate == selectedDate) &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, selectedDate, failure);

  @override
  String toString() {
    return 'SymptomTrackingState.failure(selectedDate: $selectedDate, failure: $failure)';
  }
}

/// @nodoc
abstract mixin class _$FailureCopyWith<$Res>
    implements $SymptomTrackingStateCopyWith<$Res> {
  factory _$FailureCopyWith(_Failure value, $Res Function(_Failure) _then) =
      __$FailureCopyWithImpl;
  @override
  @useResult
  $Res call({DateTime? selectedDate, PeriodTrackingFailure failure});

  $PeriodTrackingFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$FailureCopyWithImpl<$Res> implements _$FailureCopyWith<$Res> {
  __$FailureCopyWithImpl(this._self, this._then);

  final _Failure _self;
  final $Res Function(_Failure) _then;

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedDate = freezed,
    Object? failure = null,
  }) {
    return _then(_Failure(
      selectedDate: freezed == selectedDate
          ? _self.selectedDate
          : selectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      failure: null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as PeriodTrackingFailure,
    ));
  }

  /// Create a copy of SymptomTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PeriodTrackingFailureCopyWith<$Res> get failure {
    return $PeriodTrackingFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

// dart format on
