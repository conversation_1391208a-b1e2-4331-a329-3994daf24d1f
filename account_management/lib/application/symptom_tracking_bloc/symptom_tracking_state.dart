part of 'symptom_tracking_bloc.dart';

@freezed
abstract class SymptomTrackingState with _$SymptomTrackingState {
  const factory SymptomTrackingState.initial({
    DateTime? selectedDate,
  }) = _Initial;
  const factory SymptomTrackingState.loading({
    DateTime? selectedDate,
  }) = _Loading;
  const factory SymptomTrackingState.loaded({
    required DateTime selectedDate,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
  }) = _Loaded;
  const factory SymptomTrackingState.success({
    required DateTime selectedDate,
  }) = _Success;
  const factory SymptomTrackingState.failure({
    DateTime? selectedDate,
    required PeriodTrackingFailure failure,
  }) = _Failure;
}
