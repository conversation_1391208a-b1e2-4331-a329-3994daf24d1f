// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_ovulation_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ManageOvulationEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ManageOvulationEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageOvulationEvent()';
  }
}

/// @nodoc
class $ManageOvulationEventCopyWith<$Res> {
  $ManageOvulationEventCopyWith(
      ManageOvulationEvent _, $Res Function(ManageOvulationEvent) __);
}

/// Adds pattern-matching-related methods to [ManageOvulationEvent].
extension ManageOvulationEventPatterns on ManageOvulationEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_HandlePeriodTrackingChanges value)?
        handlePeriodTrackingChanges,
    TResult Function(_GetAllOvulationDates value)? getAllOvulationDates,
    TResult Function(_ClearAllOvulationDates value)? clearAllOvulationDates,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _HandlePeriodTrackingChanges()
          when handlePeriodTrackingChanges != null:
        return handlePeriodTrackingChanges(_that);
      case _GetAllOvulationDates() when getAllOvulationDates != null:
        return getAllOvulationDates(_that);
      case _ClearAllOvulationDates() when clearAllOvulationDates != null:
        return clearAllOvulationDates(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_HandlePeriodTrackingChanges value)
        handlePeriodTrackingChanges,
    required TResult Function(_GetAllOvulationDates value) getAllOvulationDates,
    required TResult Function(_ClearAllOvulationDates value)
        clearAllOvulationDates,
  }) {
    final _that = this;
    switch (_that) {
      case _HandlePeriodTrackingChanges():
        return handlePeriodTrackingChanges(_that);
      case _GetAllOvulationDates():
        return getAllOvulationDates(_that);
      case _ClearAllOvulationDates():
        return clearAllOvulationDates(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_HandlePeriodTrackingChanges value)?
        handlePeriodTrackingChanges,
    TResult? Function(_GetAllOvulationDates value)? getAllOvulationDates,
    TResult? Function(_ClearAllOvulationDates value)? clearAllOvulationDates,
  }) {
    final _that = this;
    switch (_that) {
      case _HandlePeriodTrackingChanges()
          when handlePeriodTrackingChanges != null:
        return handlePeriodTrackingChanges(_that);
      case _GetAllOvulationDates() when getAllOvulationDates != null:
        return getAllOvulationDates(_that);
      case _ClearAllOvulationDates() when clearAllOvulationDates != null:
        return clearAllOvulationDates(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Set<DateTime> newlySelected, Set<DateTime> newlyDeselected,
            Set<DateTime> allPeriodDates)?
        handlePeriodTrackingChanges,
    TResult Function()? getAllOvulationDates,
    TResult Function()? clearAllOvulationDates,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _HandlePeriodTrackingChanges()
          when handlePeriodTrackingChanges != null:
        return handlePeriodTrackingChanges(
            _that.newlySelected, _that.newlyDeselected, _that.allPeriodDates);
      case _GetAllOvulationDates() when getAllOvulationDates != null:
        return getAllOvulationDates();
      case _ClearAllOvulationDates() when clearAllOvulationDates != null:
        return clearAllOvulationDates();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Set<DateTime> newlySelected,
            Set<DateTime> newlyDeselected, Set<DateTime> allPeriodDates)
        handlePeriodTrackingChanges,
    required TResult Function() getAllOvulationDates,
    required TResult Function() clearAllOvulationDates,
  }) {
    final _that = this;
    switch (_that) {
      case _HandlePeriodTrackingChanges():
        return handlePeriodTrackingChanges(
            _that.newlySelected, _that.newlyDeselected, _that.allPeriodDates);
      case _GetAllOvulationDates():
        return getAllOvulationDates();
      case _ClearAllOvulationDates():
        return clearAllOvulationDates();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Set<DateTime> newlySelected,
            Set<DateTime> newlyDeselected, Set<DateTime> allPeriodDates)?
        handlePeriodTrackingChanges,
    TResult? Function()? getAllOvulationDates,
    TResult? Function()? clearAllOvulationDates,
  }) {
    final _that = this;
    switch (_that) {
      case _HandlePeriodTrackingChanges()
          when handlePeriodTrackingChanges != null:
        return handlePeriodTrackingChanges(
            _that.newlySelected, _that.newlyDeselected, _that.allPeriodDates);
      case _GetAllOvulationDates() when getAllOvulationDates != null:
        return getAllOvulationDates();
      case _ClearAllOvulationDates() when clearAllOvulationDates != null:
        return clearAllOvulationDates();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _HandlePeriodTrackingChanges implements ManageOvulationEvent {
  const _HandlePeriodTrackingChanges(
      {required final Set<DateTime> newlySelected,
      required final Set<DateTime> newlyDeselected,
      required final Set<DateTime> allPeriodDates})
      : _newlySelected = newlySelected,
        _newlyDeselected = newlyDeselected,
        _allPeriodDates = allPeriodDates;

  final Set<DateTime> _newlySelected;
  Set<DateTime> get newlySelected {
    if (_newlySelected is EqualUnmodifiableSetView) return _newlySelected;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_newlySelected);
  }

  final Set<DateTime> _newlyDeselected;
  Set<DateTime> get newlyDeselected {
    if (_newlyDeselected is EqualUnmodifiableSetView) return _newlyDeselected;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_newlyDeselected);
  }

  final Set<DateTime> _allPeriodDates;
  Set<DateTime> get allPeriodDates {
    if (_allPeriodDates is EqualUnmodifiableSetView) return _allPeriodDates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_allPeriodDates);
  }

  /// Create a copy of ManageOvulationEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$HandlePeriodTrackingChangesCopyWith<_HandlePeriodTrackingChanges>
      get copyWith => __$HandlePeriodTrackingChangesCopyWithImpl<
          _HandlePeriodTrackingChanges>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _HandlePeriodTrackingChanges &&
            const DeepCollectionEquality()
                .equals(other._newlySelected, _newlySelected) &&
            const DeepCollectionEquality()
                .equals(other._newlyDeselected, _newlyDeselected) &&
            const DeepCollectionEquality()
                .equals(other._allPeriodDates, _allPeriodDates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_newlySelected),
      const DeepCollectionEquality().hash(_newlyDeselected),
      const DeepCollectionEquality().hash(_allPeriodDates));

  @override
  String toString() {
    return 'ManageOvulationEvent.handlePeriodTrackingChanges(newlySelected: $newlySelected, newlyDeselected: $newlyDeselected, allPeriodDates: $allPeriodDates)';
  }
}

/// @nodoc
abstract mixin class _$HandlePeriodTrackingChangesCopyWith<$Res>
    implements $ManageOvulationEventCopyWith<$Res> {
  factory _$HandlePeriodTrackingChangesCopyWith(
          _HandlePeriodTrackingChanges value,
          $Res Function(_HandlePeriodTrackingChanges) _then) =
      __$HandlePeriodTrackingChangesCopyWithImpl;
  @useResult
  $Res call(
      {Set<DateTime> newlySelected,
      Set<DateTime> newlyDeselected,
      Set<DateTime> allPeriodDates});
}

/// @nodoc
class __$HandlePeriodTrackingChangesCopyWithImpl<$Res>
    implements _$HandlePeriodTrackingChangesCopyWith<$Res> {
  __$HandlePeriodTrackingChangesCopyWithImpl(this._self, this._then);

  final _HandlePeriodTrackingChanges _self;
  final $Res Function(_HandlePeriodTrackingChanges) _then;

  /// Create a copy of ManageOvulationEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? newlySelected = null,
    Object? newlyDeselected = null,
    Object? allPeriodDates = null,
  }) {
    return _then(_HandlePeriodTrackingChanges(
      newlySelected: null == newlySelected
          ? _self._newlySelected
          : newlySelected // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
      newlyDeselected: null == newlyDeselected
          ? _self._newlyDeselected
          : newlyDeselected // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
      allPeriodDates: null == allPeriodDates
          ? _self._allPeriodDates
          : allPeriodDates // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ));
  }
}

/// @nodoc

class _GetAllOvulationDates implements ManageOvulationEvent {
  const _GetAllOvulationDates();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _GetAllOvulationDates);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageOvulationEvent.getAllOvulationDates()';
  }
}

/// @nodoc

class _ClearAllOvulationDates implements ManageOvulationEvent {
  const _ClearAllOvulationDates();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ClearAllOvulationDates);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageOvulationEvent.clearAllOvulationDates()';
  }
}

/// @nodoc
mixin _$ManageOvulationState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ManageOvulationState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageOvulationState()';
  }
}

/// @nodoc
class $ManageOvulationStateCopyWith<$Res> {
  $ManageOvulationStateCopyWith(
      ManageOvulationState _, $Res Function(ManageOvulationState) __);
}

/// Adds pattern-matching-related methods to [ManageOvulationState].
extension ManageOvulationStatePatterns on ManageOvulationState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Success value)? success,
    TResult Function(_OvulationDatesCalculated value)? ovulationDatesCalculated,
    TResult Function(_OvulationDatesRetrieved value)? ovulationDatesRetrieved,
    TResult Function(_PeriodDataValidated value)? periodDataValidated,
    TResult Function(_NextOvulationDateUpdated value)? nextOvulationDateUpdated,
    TResult Function(_OvulationDatesCleared value)? ovulationDatesCleared,
    TResult Function(_OvulationDatesRemovedForCycles value)?
        ovulationDatesRemovedForCycles,
    TResult Function(_PeriodTrackingChangesHandled value)?
        periodTrackingChangesHandled,
    TResult Function(_Failure value)? failure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Success() when success != null:
        return success(_that);
      case _OvulationDatesCalculated() when ovulationDatesCalculated != null:
        return ovulationDatesCalculated(_that);
      case _OvulationDatesRetrieved() when ovulationDatesRetrieved != null:
        return ovulationDatesRetrieved(_that);
      case _PeriodDataValidated() when periodDataValidated != null:
        return periodDataValidated(_that);
      case _NextOvulationDateUpdated() when nextOvulationDateUpdated != null:
        return nextOvulationDateUpdated(_that);
      case _OvulationDatesCleared() when ovulationDatesCleared != null:
        return ovulationDatesCleared(_that);
      case _OvulationDatesRemovedForCycles()
          when ovulationDatesRemovedForCycles != null:
        return ovulationDatesRemovedForCycles(_that);
      case _PeriodTrackingChangesHandled()
          when periodTrackingChangesHandled != null:
        return periodTrackingChangesHandled(_that);
      case _Failure() when failure != null:
        return failure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Success value) success,
    required TResult Function(_OvulationDatesCalculated value)
        ovulationDatesCalculated,
    required TResult Function(_OvulationDatesRetrieved value)
        ovulationDatesRetrieved,
    required TResult Function(_PeriodDataValidated value) periodDataValidated,
    required TResult Function(_NextOvulationDateUpdated value)
        nextOvulationDateUpdated,
    required TResult Function(_OvulationDatesCleared value)
        ovulationDatesCleared,
    required TResult Function(_OvulationDatesRemovedForCycles value)
        ovulationDatesRemovedForCycles,
    required TResult Function(_PeriodTrackingChangesHandled value)
        periodTrackingChangesHandled,
    required TResult Function(_Failure value) failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _Loading():
        return loading(_that);
      case _Success():
        return success(_that);
      case _OvulationDatesCalculated():
        return ovulationDatesCalculated(_that);
      case _OvulationDatesRetrieved():
        return ovulationDatesRetrieved(_that);
      case _PeriodDataValidated():
        return periodDataValidated(_that);
      case _NextOvulationDateUpdated():
        return nextOvulationDateUpdated(_that);
      case _OvulationDatesCleared():
        return ovulationDatesCleared(_that);
      case _OvulationDatesRemovedForCycles():
        return ovulationDatesRemovedForCycles(_that);
      case _PeriodTrackingChangesHandled():
        return periodTrackingChangesHandled(_that);
      case _Failure():
        return failure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Success value)? success,
    TResult? Function(_OvulationDatesCalculated value)?
        ovulationDatesCalculated,
    TResult? Function(_OvulationDatesRetrieved value)? ovulationDatesRetrieved,
    TResult? Function(_PeriodDataValidated value)? periodDataValidated,
    TResult? Function(_NextOvulationDateUpdated value)?
        nextOvulationDateUpdated,
    TResult? Function(_OvulationDatesCleared value)? ovulationDatesCleared,
    TResult? Function(_OvulationDatesRemovedForCycles value)?
        ovulationDatesRemovedForCycles,
    TResult? Function(_PeriodTrackingChangesHandled value)?
        periodTrackingChangesHandled,
    TResult? Function(_Failure value)? failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Success() when success != null:
        return success(_that);
      case _OvulationDatesCalculated() when ovulationDatesCalculated != null:
        return ovulationDatesCalculated(_that);
      case _OvulationDatesRetrieved() when ovulationDatesRetrieved != null:
        return ovulationDatesRetrieved(_that);
      case _PeriodDataValidated() when periodDataValidated != null:
        return periodDataValidated(_that);
      case _NextOvulationDateUpdated() when nextOvulationDateUpdated != null:
        return nextOvulationDateUpdated(_that);
      case _OvulationDatesCleared() when ovulationDatesCleared != null:
        return ovulationDatesCleared(_that);
      case _OvulationDatesRemovedForCycles()
          when ovulationDatesRemovedForCycles != null:
        return ovulationDatesRemovedForCycles(_that);
      case _PeriodTrackingChangesHandled()
          when periodTrackingChangesHandled != null:
        return periodTrackingChangesHandled(_that);
      case _Failure() when failure != null:
        return failure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? success,
    TResult Function(Set<DateTime> ovulationDates)? ovulationDatesCalculated,
    TResult Function(Set<DateTime> ovulationDates)? ovulationDatesRetrieved,
    TResult Function(bool isValid)? periodDataValidated,
    TResult Function(DateTime? nextOvulationDate)? nextOvulationDateUpdated,
    TResult Function()? ovulationDatesCleared,
    TResult Function(Set<DateTime> removedDates)?
        ovulationDatesRemovedForCycles,
    TResult Function(Set<DateTime> updatedOvulationDates)?
        periodTrackingChangesHandled,
    TResult Function(OvulationFailure failure)? failure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _Success() when success != null:
        return success();
      case _OvulationDatesCalculated() when ovulationDatesCalculated != null:
        return ovulationDatesCalculated(_that.ovulationDates);
      case _OvulationDatesRetrieved() when ovulationDatesRetrieved != null:
        return ovulationDatesRetrieved(_that.ovulationDates);
      case _PeriodDataValidated() when periodDataValidated != null:
        return periodDataValidated(_that.isValid);
      case _NextOvulationDateUpdated() when nextOvulationDateUpdated != null:
        return nextOvulationDateUpdated(_that.nextOvulationDate);
      case _OvulationDatesCleared() when ovulationDatesCleared != null:
        return ovulationDatesCleared();
      case _OvulationDatesRemovedForCycles()
          when ovulationDatesRemovedForCycles != null:
        return ovulationDatesRemovedForCycles(_that.removedDates);
      case _PeriodTrackingChangesHandled()
          when periodTrackingChangesHandled != null:
        return periodTrackingChangesHandled(_that.updatedOvulationDates);
      case _Failure() when failure != null:
        return failure(_that.failure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() success,
    required TResult Function(Set<DateTime> ovulationDates)
        ovulationDatesCalculated,
    required TResult Function(Set<DateTime> ovulationDates)
        ovulationDatesRetrieved,
    required TResult Function(bool isValid) periodDataValidated,
    required TResult Function(DateTime? nextOvulationDate)
        nextOvulationDateUpdated,
    required TResult Function() ovulationDatesCleared,
    required TResult Function(Set<DateTime> removedDates)
        ovulationDatesRemovedForCycles,
    required TResult Function(Set<DateTime> updatedOvulationDates)
        periodTrackingChangesHandled,
    required TResult Function(OvulationFailure failure) failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _Loading():
        return loading();
      case _Success():
        return success();
      case _OvulationDatesCalculated():
        return ovulationDatesCalculated(_that.ovulationDates);
      case _OvulationDatesRetrieved():
        return ovulationDatesRetrieved(_that.ovulationDates);
      case _PeriodDataValidated():
        return periodDataValidated(_that.isValid);
      case _NextOvulationDateUpdated():
        return nextOvulationDateUpdated(_that.nextOvulationDate);
      case _OvulationDatesCleared():
        return ovulationDatesCleared();
      case _OvulationDatesRemovedForCycles():
        return ovulationDatesRemovedForCycles(_that.removedDates);
      case _PeriodTrackingChangesHandled():
        return periodTrackingChangesHandled(_that.updatedOvulationDates);
      case _Failure():
        return failure(_that.failure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? success,
    TResult? Function(Set<DateTime> ovulationDates)? ovulationDatesCalculated,
    TResult? Function(Set<DateTime> ovulationDates)? ovulationDatesRetrieved,
    TResult? Function(bool isValid)? periodDataValidated,
    TResult? Function(DateTime? nextOvulationDate)? nextOvulationDateUpdated,
    TResult? Function()? ovulationDatesCleared,
    TResult? Function(Set<DateTime> removedDates)?
        ovulationDatesRemovedForCycles,
    TResult? Function(Set<DateTime> updatedOvulationDates)?
        periodTrackingChangesHandled,
    TResult? Function(OvulationFailure failure)? failure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _Success() when success != null:
        return success();
      case _OvulationDatesCalculated() when ovulationDatesCalculated != null:
        return ovulationDatesCalculated(_that.ovulationDates);
      case _OvulationDatesRetrieved() when ovulationDatesRetrieved != null:
        return ovulationDatesRetrieved(_that.ovulationDates);
      case _PeriodDataValidated() when periodDataValidated != null:
        return periodDataValidated(_that.isValid);
      case _NextOvulationDateUpdated() when nextOvulationDateUpdated != null:
        return nextOvulationDateUpdated(_that.nextOvulationDate);
      case _OvulationDatesCleared() when ovulationDatesCleared != null:
        return ovulationDatesCleared();
      case _OvulationDatesRemovedForCycles()
          when ovulationDatesRemovedForCycles != null:
        return ovulationDatesRemovedForCycles(_that.removedDates);
      case _PeriodTrackingChangesHandled()
          when periodTrackingChangesHandled != null:
        return periodTrackingChangesHandled(_that.updatedOvulationDates);
      case _Failure() when failure != null:
        return failure(_that.failure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements ManageOvulationState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageOvulationState.initial()';
  }
}

/// @nodoc

class _Loading implements ManageOvulationState {
  const _Loading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Loading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageOvulationState.loading()';
  }
}

/// @nodoc

class _Success implements ManageOvulationState {
  const _Success();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Success);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageOvulationState.success()';
  }
}

/// @nodoc

class _OvulationDatesCalculated implements ManageOvulationState {
  const _OvulationDatesCalculated(final Set<DateTime> ovulationDates)
      : _ovulationDates = ovulationDates;

  final Set<DateTime> _ovulationDates;
  Set<DateTime> get ovulationDates {
    if (_ovulationDates is EqualUnmodifiableSetView) return _ovulationDates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_ovulationDates);
  }

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OvulationDatesCalculatedCopyWith<_OvulationDatesCalculated> get copyWith =>
      __$OvulationDatesCalculatedCopyWithImpl<_OvulationDatesCalculated>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OvulationDatesCalculated &&
            const DeepCollectionEquality()
                .equals(other._ovulationDates, _ovulationDates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_ovulationDates));

  @override
  String toString() {
    return 'ManageOvulationState.ovulationDatesCalculated(ovulationDates: $ovulationDates)';
  }
}

/// @nodoc
abstract mixin class _$OvulationDatesCalculatedCopyWith<$Res>
    implements $ManageOvulationStateCopyWith<$Res> {
  factory _$OvulationDatesCalculatedCopyWith(_OvulationDatesCalculated value,
          $Res Function(_OvulationDatesCalculated) _then) =
      __$OvulationDatesCalculatedCopyWithImpl;
  @useResult
  $Res call({Set<DateTime> ovulationDates});
}

/// @nodoc
class __$OvulationDatesCalculatedCopyWithImpl<$Res>
    implements _$OvulationDatesCalculatedCopyWith<$Res> {
  __$OvulationDatesCalculatedCopyWithImpl(this._self, this._then);

  final _OvulationDatesCalculated _self;
  final $Res Function(_OvulationDatesCalculated) _then;

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? ovulationDates = null,
  }) {
    return _then(_OvulationDatesCalculated(
      null == ovulationDates
          ? _self._ovulationDates
          : ovulationDates // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ));
  }
}

/// @nodoc

class _OvulationDatesRetrieved implements ManageOvulationState {
  const _OvulationDatesRetrieved(final Set<DateTime> ovulationDates)
      : _ovulationDates = ovulationDates;

  final Set<DateTime> _ovulationDates;
  Set<DateTime> get ovulationDates {
    if (_ovulationDates is EqualUnmodifiableSetView) return _ovulationDates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_ovulationDates);
  }

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OvulationDatesRetrievedCopyWith<_OvulationDatesRetrieved> get copyWith =>
      __$OvulationDatesRetrievedCopyWithImpl<_OvulationDatesRetrieved>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OvulationDatesRetrieved &&
            const DeepCollectionEquality()
                .equals(other._ovulationDates, _ovulationDates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_ovulationDates));

  @override
  String toString() {
    return 'ManageOvulationState.ovulationDatesRetrieved(ovulationDates: $ovulationDates)';
  }
}

/// @nodoc
abstract mixin class _$OvulationDatesRetrievedCopyWith<$Res>
    implements $ManageOvulationStateCopyWith<$Res> {
  factory _$OvulationDatesRetrievedCopyWith(_OvulationDatesRetrieved value,
          $Res Function(_OvulationDatesRetrieved) _then) =
      __$OvulationDatesRetrievedCopyWithImpl;
  @useResult
  $Res call({Set<DateTime> ovulationDates});
}

/// @nodoc
class __$OvulationDatesRetrievedCopyWithImpl<$Res>
    implements _$OvulationDatesRetrievedCopyWith<$Res> {
  __$OvulationDatesRetrievedCopyWithImpl(this._self, this._then);

  final _OvulationDatesRetrieved _self;
  final $Res Function(_OvulationDatesRetrieved) _then;

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? ovulationDates = null,
  }) {
    return _then(_OvulationDatesRetrieved(
      null == ovulationDates
          ? _self._ovulationDates
          : ovulationDates // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ));
  }
}

/// @nodoc

class _PeriodDataValidated implements ManageOvulationState {
  const _PeriodDataValidated(this.isValid);

  final bool isValid;

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PeriodDataValidatedCopyWith<_PeriodDataValidated> get copyWith =>
      __$PeriodDataValidatedCopyWithImpl<_PeriodDataValidated>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PeriodDataValidated &&
            (identical(other.isValid, isValid) || other.isValid == isValid));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isValid);

  @override
  String toString() {
    return 'ManageOvulationState.periodDataValidated(isValid: $isValid)';
  }
}

/// @nodoc
abstract mixin class _$PeriodDataValidatedCopyWith<$Res>
    implements $ManageOvulationStateCopyWith<$Res> {
  factory _$PeriodDataValidatedCopyWith(_PeriodDataValidated value,
          $Res Function(_PeriodDataValidated) _then) =
      __$PeriodDataValidatedCopyWithImpl;
  @useResult
  $Res call({bool isValid});
}

/// @nodoc
class __$PeriodDataValidatedCopyWithImpl<$Res>
    implements _$PeriodDataValidatedCopyWith<$Res> {
  __$PeriodDataValidatedCopyWithImpl(this._self, this._then);

  final _PeriodDataValidated _self;
  final $Res Function(_PeriodDataValidated) _then;

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isValid = null,
  }) {
    return _then(_PeriodDataValidated(
      null == isValid
          ? _self.isValid
          : isValid // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _NextOvulationDateUpdated implements ManageOvulationState {
  const _NextOvulationDateUpdated(this.nextOvulationDate);

  final DateTime? nextOvulationDate;

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$NextOvulationDateUpdatedCopyWith<_NextOvulationDateUpdated> get copyWith =>
      __$NextOvulationDateUpdatedCopyWithImpl<_NextOvulationDateUpdated>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _NextOvulationDateUpdated &&
            (identical(other.nextOvulationDate, nextOvulationDate) ||
                other.nextOvulationDate == nextOvulationDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, nextOvulationDate);

  @override
  String toString() {
    return 'ManageOvulationState.nextOvulationDateUpdated(nextOvulationDate: $nextOvulationDate)';
  }
}

/// @nodoc
abstract mixin class _$NextOvulationDateUpdatedCopyWith<$Res>
    implements $ManageOvulationStateCopyWith<$Res> {
  factory _$NextOvulationDateUpdatedCopyWith(_NextOvulationDateUpdated value,
          $Res Function(_NextOvulationDateUpdated) _then) =
      __$NextOvulationDateUpdatedCopyWithImpl;
  @useResult
  $Res call({DateTime? nextOvulationDate});
}

/// @nodoc
class __$NextOvulationDateUpdatedCopyWithImpl<$Res>
    implements _$NextOvulationDateUpdatedCopyWith<$Res> {
  __$NextOvulationDateUpdatedCopyWithImpl(this._self, this._then);

  final _NextOvulationDateUpdated _self;
  final $Res Function(_NextOvulationDateUpdated) _then;

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? nextOvulationDate = freezed,
  }) {
    return _then(_NextOvulationDateUpdated(
      freezed == nextOvulationDate
          ? _self.nextOvulationDate
          : nextOvulationDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _OvulationDatesCleared implements ManageOvulationState {
  const _OvulationDatesCleared();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _OvulationDatesCleared);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageOvulationState.ovulationDatesCleared()';
  }
}

/// @nodoc

class _OvulationDatesRemovedForCycles implements ManageOvulationState {
  const _OvulationDatesRemovedForCycles(final Set<DateTime> removedDates)
      : _removedDates = removedDates;

  final Set<DateTime> _removedDates;
  Set<DateTime> get removedDates {
    if (_removedDates is EqualUnmodifiableSetView) return _removedDates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_removedDates);
  }

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OvulationDatesRemovedForCyclesCopyWith<_OvulationDatesRemovedForCycles>
      get copyWith => __$OvulationDatesRemovedForCyclesCopyWithImpl<
          _OvulationDatesRemovedForCycles>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OvulationDatesRemovedForCycles &&
            const DeepCollectionEquality()
                .equals(other._removedDates, _removedDates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_removedDates));

  @override
  String toString() {
    return 'ManageOvulationState.ovulationDatesRemovedForCycles(removedDates: $removedDates)';
  }
}

/// @nodoc
abstract mixin class _$OvulationDatesRemovedForCyclesCopyWith<$Res>
    implements $ManageOvulationStateCopyWith<$Res> {
  factory _$OvulationDatesRemovedForCyclesCopyWith(
          _OvulationDatesRemovedForCycles value,
          $Res Function(_OvulationDatesRemovedForCycles) _then) =
      __$OvulationDatesRemovedForCyclesCopyWithImpl;
  @useResult
  $Res call({Set<DateTime> removedDates});
}

/// @nodoc
class __$OvulationDatesRemovedForCyclesCopyWithImpl<$Res>
    implements _$OvulationDatesRemovedForCyclesCopyWith<$Res> {
  __$OvulationDatesRemovedForCyclesCopyWithImpl(this._self, this._then);

  final _OvulationDatesRemovedForCycles _self;
  final $Res Function(_OvulationDatesRemovedForCycles) _then;

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? removedDates = null,
  }) {
    return _then(_OvulationDatesRemovedForCycles(
      null == removedDates
          ? _self._removedDates
          : removedDates // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ));
  }
}

/// @nodoc

class _PeriodTrackingChangesHandled implements ManageOvulationState {
  const _PeriodTrackingChangesHandled(final Set<DateTime> updatedOvulationDates)
      : _updatedOvulationDates = updatedOvulationDates;

  final Set<DateTime> _updatedOvulationDates;
  Set<DateTime> get updatedOvulationDates {
    if (_updatedOvulationDates is EqualUnmodifiableSetView)
      return _updatedOvulationDates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_updatedOvulationDates);
  }

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PeriodTrackingChangesHandledCopyWith<_PeriodTrackingChangesHandled>
      get copyWith => __$PeriodTrackingChangesHandledCopyWithImpl<
          _PeriodTrackingChangesHandled>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PeriodTrackingChangesHandled &&
            const DeepCollectionEquality()
                .equals(other._updatedOvulationDates, _updatedOvulationDates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_updatedOvulationDates));

  @override
  String toString() {
    return 'ManageOvulationState.periodTrackingChangesHandled(updatedOvulationDates: $updatedOvulationDates)';
  }
}

/// @nodoc
abstract mixin class _$PeriodTrackingChangesHandledCopyWith<$Res>
    implements $ManageOvulationStateCopyWith<$Res> {
  factory _$PeriodTrackingChangesHandledCopyWith(
          _PeriodTrackingChangesHandled value,
          $Res Function(_PeriodTrackingChangesHandled) _then) =
      __$PeriodTrackingChangesHandledCopyWithImpl;
  @useResult
  $Res call({Set<DateTime> updatedOvulationDates});
}

/// @nodoc
class __$PeriodTrackingChangesHandledCopyWithImpl<$Res>
    implements _$PeriodTrackingChangesHandledCopyWith<$Res> {
  __$PeriodTrackingChangesHandledCopyWithImpl(this._self, this._then);

  final _PeriodTrackingChangesHandled _self;
  final $Res Function(_PeriodTrackingChangesHandled) _then;

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? updatedOvulationDates = null,
  }) {
    return _then(_PeriodTrackingChangesHandled(
      null == updatedOvulationDates
          ? _self._updatedOvulationDates
          : updatedOvulationDates // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ));
  }
}

/// @nodoc

class _Failure implements ManageOvulationState {
  const _Failure(this.failure);

  final OvulationFailure failure;

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FailureCopyWith<_Failure> get copyWith =>
      __$FailureCopyWithImpl<_Failure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Failure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'ManageOvulationState.failure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class _$FailureCopyWith<$Res>
    implements $ManageOvulationStateCopyWith<$Res> {
  factory _$FailureCopyWith(_Failure value, $Res Function(_Failure) _then) =
      __$FailureCopyWithImpl;
  @useResult
  $Res call({OvulationFailure failure});

  $OvulationFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$FailureCopyWithImpl<$Res> implements _$FailureCopyWith<$Res> {
  __$FailureCopyWithImpl(this._self, this._then);

  final _Failure _self;
  final $Res Function(_Failure) _then;

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(_Failure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as OvulationFailure,
    ));
  }

  /// Create a copy of ManageOvulationState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $OvulationFailureCopyWith<$Res> get failure {
    return $OvulationFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

// dart format on
