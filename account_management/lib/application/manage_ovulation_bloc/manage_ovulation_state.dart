part of 'manage_ovulation_bloc.dart';

@freezed
class ManageOvulationState with _$ManageOvulationState {
  /// Initial state
  const factory ManageOvulationState.initial() = _Initial;

  /// Loading state for any ovulation operation
  const factory ManageOvulationState.loading() = _Loading;

  /// Success state after ovulation calculation/update operations
  const factory ManageOvulationState.success() = _Success;

  /// State when ovulation dates are successfully calculated
  const factory ManageOvulationState.ovulationDatesCalculated(
    Set<DateTime> ovulationDates,
  ) = _OvulationDatesCalculated;

  /// State when ovulation dates are successfully retrieved
  const factory ManageOvulationState.ovulationDatesRetrieved(
    Set<DateTime> ovulationDates,
  ) = _OvulationDatesRetrieved;

  /// State when period data validation is complete
  const factory ManageOvulationState.periodDataValidated(
    bool isValid,
  ) = _PeriodDataValidated;

  /// State when next ovulation date is successfully updated
  const factory ManageOvulationState.nextOvulationDateUpdated(
    DateTime? nextOvulationDate,
  ) = _NextOvulationDateUpdated;

  /// State when ovulation dates are successfully cleared
  const factory ManageOvulationState.ovulationDatesCleared() = _OvulationDatesCleared;

  /// State when ovulation dates are successfully removed for specific cycles
  const factory ManageOvulationState.ovulationDatesRemovedForCycles(
    Set<DateTime> removedDates,
  ) = _OvulationDatesRemovedForCycles;

  /// State when period tracking changes are successfully handled
  const factory ManageOvulationState.periodTrackingChangesHandled(
    Set<DateTime> updatedOvulationDates,
  ) = _PeriodTrackingChangesHandled;

  /// Failure state with specific ovulation failure
  const factory ManageOvulationState.failure(
    OvulationFailure failure,
  ) = _Failure;
}
