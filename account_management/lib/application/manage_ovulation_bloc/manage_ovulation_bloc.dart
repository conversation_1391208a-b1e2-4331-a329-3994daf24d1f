import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import '../../domain/facade/ovulation_facade.dart';
import '../../domain/failure/ovulation_failure.dart';
part 'manage_ovulation_event.dart';
part 'manage_ovulation_state.dart';
part 'manage_ovulation_bloc.freezed.dart';

@injectable
class ManageOvulationBloc
    extends Bloc<ManageOvulationEvent, ManageOvulationState> {
  final OvulationFacade _ovulationFacade;

  ManageOvulationBloc(this._ovulationFacade)
      : super(const ManageOvulationState.initial()) {
    on<_HandlePeriodTrackingChanges>(_onHandlePeriodTrackingChanges);
    on<_GetAllOvulationDates>(_onGetAllOvulationDates);
    on<_ClearAllOvulationDates>(_onClearAllOvulationDates);
  }

  Future<void> _onGetAllOvulationDates(
    _GetAllOvulationDates event,
    Emitter<ManageOvulationState> emit,
  ) async {
    emit(const ManageOvulationState.loading());

    debugPrint('🥚 ManageOvulationBloc: Getting all ovulation dates');

    final result = await _ovulationFacade.getAllExistingOvulationDates();

    result.fold(
      onFailure: (failure) {
        debugPrint(
            '❌ ManageOvulationBloc: Failed to get all ovulation dates: $failure');
        emit(ManageOvulationState.failure(failure));
      },
      onSuccess: (ovulationDates) {
        debugPrint(
            '✅ ManageOvulationBloc: Successfully retrieved ${ovulationDates.length} ovulation dates');
        emit(ManageOvulationState.ovulationDatesRetrieved(ovulationDates));
      },
    );
  }

  Future<void> _onClearAllOvulationDates(
    _ClearAllOvulationDates event,
    Emitter<ManageOvulationState> emit,
  ) async {
    emit(const ManageOvulationState.loading());

    debugPrint('🥚 ManageOvulationBloc: Clearing all ovulation dates');

    final result = await _ovulationFacade.clearAllOvulationDates();

    result.fold(
      onFailure: (failure) {
        debugPrint(
            '❌ ManageOvulationBloc: Failed to clear all ovulation dates: $failure');
        emit(ManageOvulationState.failure(failure));
      },
      onSuccess: (_) {
        debugPrint(
            '✅ ManageOvulationBloc: Successfully cleared all ovulation dates');
        emit(const ManageOvulationState.ovulationDatesCleared());
      },
    );
  }

  Future<void> _onHandlePeriodTrackingChanges(
    _HandlePeriodTrackingChanges event,
    Emitter<ManageOvulationState> emit,
  ) async {
    debugPrint('🥚 ManageOvulationBloc: Handling period tracking changes');
    debugPrint(
        '🥚 Newly selected: ${event.newlySelected.length}, newly deselected: ${event.newlyDeselected.length}');
    debugPrint('🥚 Total period dates: ${event.allPeriodDates.length}');

    emit(const ManageOvulationState.loading());

    final result = await _ovulationFacade.handlePeriodTrackingChanges(
      newlySelected: event.newlySelected,
      newlyDeselected: event.newlyDeselected,
      allPeriodDates: event.allPeriodDates,
    );

    result.fold(
      onFailure: (failure) {
        debugPrint(
            '❌ ManageOvulationBloc: Failed to handle period tracking changes: $failure');
        emit(ManageOvulationState.failure(failure));
      },
      onSuccess: (_) {
        debugPrint(
            '✅ ManageOvulationBloc: Successfully handled period tracking changes');
        emit(const ManageOvulationState.success());
      },
    );
  }
}
