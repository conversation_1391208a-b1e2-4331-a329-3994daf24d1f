part of 'manage_ovulation_bloc.dart';

@freezed
class ManageOvulationEvent with _$ManageOvulationEvent {
  /// Handle period tracking changes (main entry point from UI)
  const factory ManageOvulationEvent.handlePeriodTrackingChanges({
    required Set<DateTime> newlySelected,
    required Set<DateTime> newlyDeselected,
    required Set<DateTime> allPeriodDates,
  }) = _HandlePeriodTrackingChanges;

  /// Get all existing ovulation dates
  const factory ManageOvulationEvent.getAllOvulationDates() =
      _GetAllOvulationDates;

  /// Clear all ovulation dates (for data cleanup)
  const factory ManageOvulationEvent.clearAllOvulationDates() =
      _ClearAllOvulationDates;
}
