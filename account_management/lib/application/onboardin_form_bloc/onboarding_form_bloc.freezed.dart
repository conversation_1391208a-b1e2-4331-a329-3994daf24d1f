// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onboarding_form_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OnboardingFormEvent {
  HealthDataModel get healthData;
  DateTime get dateOfBirth;

  /// Create a copy of OnboardingFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OnboardingFormEventCopyWith<OnboardingFormEvent> get copyWith =>
      _$OnboardingFormEventCopyWithImpl<OnboardingFormEvent>(
          this as OnboardingFormEvent, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OnboardingFormEvent &&
            (identical(other.healthData, healthData) ||
                other.healthData == healthData) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth));
  }

  @override
  int get hashCode => Object.hash(runtimeType, healthData, dateOfBirth);

  @override
  String toString() {
    return 'OnboardingFormEvent(healthData: $healthData, dateOfBirth: $dateOfBirth)';
  }
}

/// @nodoc
abstract mixin class $OnboardingFormEventCopyWith<$Res> {
  factory $OnboardingFormEventCopyWith(
          OnboardingFormEvent value, $Res Function(OnboardingFormEvent) _then) =
      _$OnboardingFormEventCopyWithImpl;
  @useResult
  $Res call({HealthDataModel healthData, DateTime dateOfBirth});
}

/// @nodoc
class _$OnboardingFormEventCopyWithImpl<$Res>
    implements $OnboardingFormEventCopyWith<$Res> {
  _$OnboardingFormEventCopyWithImpl(this._self, this._then);

  final OnboardingFormEvent _self;
  final $Res Function(OnboardingFormEvent) _then;

  /// Create a copy of OnboardingFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? healthData = null,
    Object? dateOfBirth = null,
  }) {
    return _then(_self.copyWith(
      healthData: null == healthData
          ? _self.healthData
          : healthData // ignore: cast_nullable_to_non_nullable
              as HealthDataModel,
      dateOfBirth: null == dateOfBirth
          ? _self.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// Adds pattern-matching-related methods to [OnboardingFormEvent].
extension OnboardingFormEventPatterns on OnboardingFormEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateOnboardingForm value)? updateOnboardingForm,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case UpdateOnboardingForm() when updateOnboardingForm != null:
        return updateOnboardingForm(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateOnboardingForm value) updateOnboardingForm,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateOnboardingForm():
        return updateOnboardingForm(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateOnboardingForm value)? updateOnboardingForm,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateOnboardingForm() when updateOnboardingForm != null:
        return updateOnboardingForm(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(HealthDataModel healthData, DateTime dateOfBirth)?
        updateOnboardingForm,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case UpdateOnboardingForm() when updateOnboardingForm != null:
        return updateOnboardingForm(_that.healthData, _that.dateOfBirth);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(HealthDataModel healthData, DateTime dateOfBirth)
        updateOnboardingForm,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateOnboardingForm():
        return updateOnboardingForm(_that.healthData, _that.dateOfBirth);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(HealthDataModel healthData, DateTime dateOfBirth)?
        updateOnboardingForm,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateOnboardingForm() when updateOnboardingForm != null:
        return updateOnboardingForm(_that.healthData, _that.dateOfBirth);
      case _:
        return null;
    }
  }
}

/// @nodoc

class UpdateOnboardingForm implements OnboardingFormEvent {
  const UpdateOnboardingForm(this.healthData, this.dateOfBirth);

  @override
  final HealthDataModel healthData;
  @override
  final DateTime dateOfBirth;

  /// Create a copy of OnboardingFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateOnboardingFormCopyWith<UpdateOnboardingForm> get copyWith =>
      _$UpdateOnboardingFormCopyWithImpl<UpdateOnboardingForm>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateOnboardingForm &&
            (identical(other.healthData, healthData) ||
                other.healthData == healthData) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth));
  }

  @override
  int get hashCode => Object.hash(runtimeType, healthData, dateOfBirth);

  @override
  String toString() {
    return 'OnboardingFormEvent.updateOnboardingForm(healthData: $healthData, dateOfBirth: $dateOfBirth)';
  }
}

/// @nodoc
abstract mixin class $UpdateOnboardingFormCopyWith<$Res>
    implements $OnboardingFormEventCopyWith<$Res> {
  factory $UpdateOnboardingFormCopyWith(UpdateOnboardingForm value,
          $Res Function(UpdateOnboardingForm) _then) =
      _$UpdateOnboardingFormCopyWithImpl;
  @override
  @useResult
  $Res call({HealthDataModel healthData, DateTime dateOfBirth});
}

/// @nodoc
class _$UpdateOnboardingFormCopyWithImpl<$Res>
    implements $UpdateOnboardingFormCopyWith<$Res> {
  _$UpdateOnboardingFormCopyWithImpl(this._self, this._then);

  final UpdateOnboardingForm _self;
  final $Res Function(UpdateOnboardingForm) _then;

  /// Create a copy of OnboardingFormEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? healthData = null,
    Object? dateOfBirth = null,
  }) {
    return _then(UpdateOnboardingForm(
      null == healthData
          ? _self.healthData
          : healthData // ignore: cast_nullable_to_non_nullable
              as HealthDataModel,
      null == dateOfBirth
          ? _self.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
mixin _$OnboardingFormState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OnboardingFormState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OnboardingFormState()';
  }
}

/// @nodoc
class $OnboardingFormStateCopyWith<$Res> {
  $OnboardingFormStateCopyWith(
      OnboardingFormState _, $Res Function(OnboardingFormState) __);
}

/// Adds pattern-matching-related methods to [OnboardingFormState].
extension OnboardingFormStatePatterns on OnboardingFormState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_LoadInProgress value)? loadInProgress,
    TResult Function(_UpdateSuccess value)? updateSuccess,
    TResult Function(_UpdateFailure value)? updateFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _LoadInProgress() when loadInProgress != null:
        return loadInProgress(_that);
      case _UpdateSuccess() when updateSuccess != null:
        return updateSuccess(_that);
      case _UpdateFailure() when updateFailure != null:
        return updateFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_LoadInProgress value) loadInProgress,
    required TResult Function(_UpdateSuccess value) updateSuccess,
    required TResult Function(_UpdateFailure value) updateFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _LoadInProgress():
        return loadInProgress(_that);
      case _UpdateSuccess():
        return updateSuccess(_that);
      case _UpdateFailure():
        return updateFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_LoadInProgress value)? loadInProgress,
    TResult? Function(_UpdateSuccess value)? updateSuccess,
    TResult? Function(_UpdateFailure value)? updateFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _LoadInProgress() when loadInProgress != null:
        return loadInProgress(_that);
      case _UpdateSuccess() when updateSuccess != null:
        return updateSuccess(_that);
      case _UpdateFailure() when updateFailure != null:
        return updateFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loadInProgress,
    TResult Function()? updateSuccess,
    TResult Function(AccountManagementFailure onboardingFormFailure)?
        updateFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _LoadInProgress() when loadInProgress != null:
        return loadInProgress();
      case _UpdateSuccess() when updateSuccess != null:
        return updateSuccess();
      case _UpdateFailure() when updateFailure != null:
        return updateFailure(_that.onboardingFormFailure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loadInProgress,
    required TResult Function() updateSuccess,
    required TResult Function(AccountManagementFailure onboardingFormFailure)
        updateFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _LoadInProgress():
        return loadInProgress();
      case _UpdateSuccess():
        return updateSuccess();
      case _UpdateFailure():
        return updateFailure(_that.onboardingFormFailure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loadInProgress,
    TResult? Function()? updateSuccess,
    TResult? Function(AccountManagementFailure onboardingFormFailure)?
        updateFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _LoadInProgress() when loadInProgress != null:
        return loadInProgress();
      case _UpdateSuccess() when updateSuccess != null:
        return updateSuccess();
      case _UpdateFailure() when updateFailure != null:
        return updateFailure(_that.onboardingFormFailure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements OnboardingFormState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OnboardingFormState.initial()';
  }
}

/// @nodoc

class _LoadInProgress implements OnboardingFormState {
  const _LoadInProgress();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _LoadInProgress);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OnboardingFormState.loadInProgress()';
  }
}

/// @nodoc

class _UpdateSuccess implements OnboardingFormState {
  const _UpdateSuccess();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _UpdateSuccess);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OnboardingFormState.updateSuccess()';
  }
}

/// @nodoc

class _UpdateFailure implements OnboardingFormState {
  const _UpdateFailure(this.onboardingFormFailure);

  final AccountManagementFailure onboardingFormFailure;

  /// Create a copy of OnboardingFormState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdateFailureCopyWith<_UpdateFailure> get copyWith =>
      __$UpdateFailureCopyWithImpl<_UpdateFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateFailure &&
            (identical(other.onboardingFormFailure, onboardingFormFailure) ||
                other.onboardingFormFailure == onboardingFormFailure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, onboardingFormFailure);

  @override
  String toString() {
    return 'OnboardingFormState.updateFailure(onboardingFormFailure: $onboardingFormFailure)';
  }
}

/// @nodoc
abstract mixin class _$UpdateFailureCopyWith<$Res>
    implements $OnboardingFormStateCopyWith<$Res> {
  factory _$UpdateFailureCopyWith(
          _UpdateFailure value, $Res Function(_UpdateFailure) _then) =
      __$UpdateFailureCopyWithImpl;
  @useResult
  $Res call({AccountManagementFailure onboardingFormFailure});

  $AccountManagementFailureCopyWith<$Res> get onboardingFormFailure;
}

/// @nodoc
class __$UpdateFailureCopyWithImpl<$Res>
    implements _$UpdateFailureCopyWith<$Res> {
  __$UpdateFailureCopyWithImpl(this._self, this._then);

  final _UpdateFailure _self;
  final $Res Function(_UpdateFailure) _then;

  /// Create a copy of OnboardingFormState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? onboardingFormFailure = null,
  }) {
    return _then(_UpdateFailure(
      null == onboardingFormFailure
          ? _self.onboardingFormFailure
          : onboardingFormFailure // ignore: cast_nullable_to_non_nullable
              as AccountManagementFailure,
    ));
  }

  /// Create a copy of OnboardingFormState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AccountManagementFailureCopyWith<$Res> get onboardingFormFailure {
    return $AccountManagementFailureCopyWith<$Res>(_self.onboardingFormFailure,
        (value) {
      return _then(_self.copyWith(onboardingFormFailure: value));
    });
  }
}

// dart format on
