import 'package:account_management/domain/failure/account_management_failures.dart';
import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import '../../domain/facade/account_management_facade.dart';
import '../../domain/model/health_data.dart';

part 'onboarding_form_event.dart';
part 'onboarding_form_state.dart';
part 'onboarding_form_bloc.freezed.dart';
@injectable
class OnboardingFormBloc extends Bloc<OnboardingFormEvent, OnboardingFormState> {
  final AccountManagementFacade _accountManagementFacade;
  OnboardingFormBloc(this._accountManagementFacade) : super(const OnboardingFormState.initial()) {
    on<UpdateOnboardingForm>(_onUpdateOnboardingForm);

  }
  // Handles the event of updating the onboarding form.
  Future<void> _onUpdateOnboardingForm(UpdateOnboardingForm event, Emitter<OnboardingFormState> emit) async {
    emit(const OnboardingFormState.loadInProgress());
    final result = await _accountManagementFacade.updateOnboardingData(event.healthData,event.dateOfBirth);
    result.fold(onFailure: (failure) {
      emit(OnboardingFormState.updateFailure(failure));
    }, onSuccess: (_) {
      emit(const OnboardingFormState.updateSuccess());
    });
  }
}
