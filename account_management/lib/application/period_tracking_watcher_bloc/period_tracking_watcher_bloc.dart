import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:doso/doso.dart';
import 'package:fpdart/fpdart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../domain/facade/period_tracking_facade.dart';
import '../../domain/failure/period_tracking_failure.dart';
import '../../domain/model/period_tracking_model.dart';

part 'period_tracking_watcher_event.dart';
part 'period_tracking_watcher_state.dart';
part 'period_tracking_watcher_bloc.freezed.dart';

@injectable
class PeriodTrackingWatcherBloc
    extends Bloc<PeriodTrackingWatcherEvent, PeriodTrackingWatcherState> {
  final PeriodTrackingFacade _periodTrackingFacade;

  StreamSubscription<
          Do<PeriodTrackingFailure,
              Map<String, Map<String, PeriodTrackingModel>>>>?
      _periodStreamSubscription;

  PeriodTrackingWatcherBloc(this._periodTrackingFacade)
      : super(const PeriodTrackingWatcherState.initial()) {
    on<_WatchYearStarted>(_onWatchYearStarted);
    on<_PeriodDataReceived>(_onPeriodDataReceived);
  }

  Future<void> _onWatchYearStarted(
    _WatchYearStarted event,
    Emitter<PeriodTrackingWatcherState> emit,
  ) async {
    print(
        '🎯 PeriodTrackingWatcherBloc._onWatchYearStarted called for year: ${event.year}');
    emit(const PeriodTrackingWatcherState.loading());
    await _periodStreamSubscription?.cancel();

    print(
        '🎯 PeriodTrackingWatcherBloc: Setting up stream subscription for year ${event.year}');
    _periodStreamSubscription =
        _periodTrackingFacade.watchYearData(event.year).listen(
      (failureOrData) {
        if (isClosed) return;
        print(
            '🎯 PeriodTrackingWatcherBloc: Received data from stream, adding periodDataReceived event');
        add(PeriodTrackingWatcherEvent.periodDataReceived(failureOrData));
      },
    );
  }

  Future<void> _onPeriodDataReceived(
    _PeriodDataReceived event,
    Emitter<PeriodTrackingWatcherState> emit,
  ) async {
    print('🎯 PeriodTrackingWatcherBloc._onPeriodDataReceived called');

    event.failureOrData.fold(
      onFailure: (failure) {
        print('❌ PeriodTrackingWatcherBloc: Received failure: $failure');
        if (!emit.isDone) {
          emit(PeriodTrackingWatcherState.loadFailure(failure));
        }
      },
      onSuccess: (data) async {
        print(
            '✅ PeriodTrackingWatcherBloc: Received success data with ${data.keys.length} months');

        // Debug: Print data structure
        for (final monthEntry in data.entries) {
          final monthKey = monthEntry.key;
          final monthData = monthEntry.value;
          print('🎯 Month $monthKey: ${monthData.keys.length} days');

          // Count period and ovulation dates
          int periodCount = 0;
          int ovulationCount = 0;
          for (final dayEntry in monthData.entries) {
            final model = dayEntry.value;
            if (model.isPeriodDate == true) periodCount++;
            if (model.isOvulationDate == true) ovulationCount++;
          }
          print(
              '🎯   Period dates: $periodCount, Ovulation dates: $ovulationCount');
        }

        final resolvedData = data;

        // Check if bloc is still active before proceeding
        if (emit.isDone || isClosed) {
          print(
              '⚠️ PeriodTrackingWatcherBloc: Bloc closed, not processing further');
          return;
        }

        // Generate immediate fallback predictions based on existing data
        final fallbackPredictions = _generateFallbackPredictions(resolvedData);

        // Emit data immediately with fallback predictions
        print(
            '🎯 PeriodTrackingWatcherBloc: Emitting loadSuccess state with fallback predictions');
        emit(PeriodTrackingWatcherState.loadSuccess(resolvedData,
            futurePredictions: fallbackPredictions));

        // Calculate predictions asynchronously (non-blocking)
        print(
            '🎯 PeriodTrackingWatcherBloc: Starting async prediction calculation...');
        _calculateFuturePredictions().then((futurePredictions) {
          print(
              '🎯 PeriodTrackingWatcherBloc: Async predictions calculated: ${futurePredictions.keys}');

          // Check again if bloc is still active before emitting with predictions
          if (!emit.isDone && !isClosed) {
            print(
                '🎯 PeriodTrackingWatcherBloc: Emitting loadSuccess state with predictions');
            emit(PeriodTrackingWatcherState.loadSuccess(resolvedData,
                futurePredictions: futurePredictions));
          } else {
            print(
                '⚠️ PeriodTrackingWatcherBloc: Bloc closed, not emitting predictions');
          }
        }).catchError((error) {
          print(
              '❌ PeriodTrackingWatcherBloc: Async prediction calculation failed: $error');
          // Don't emit error, just continue without predictions
        });
      },
    );
  }

  /// Calculate future period and ovulation predictions using the facade
  Future<Map<String, Set<DateTime>>> _calculateFuturePredictions() async {
    try {
      print('🎯 PeriodTrackingWatcherBloc: Starting prediction calculation...');

      // Use the facade's generatePredictions method which properly uses health data
      final result =
          await _periodTrackingFacade.generatePredictions(monthsAhead: 12);

      return result.fold(
        onFailure: (failure) {
          print(
              '❌ PeriodTrackingWatcherBloc: Prediction calculation failed: $failure');
          return {
            'periods': <DateTime>{},
            'ovulations': <DateTime>{},
          };
        },
        onSuccess: (predictions) {
          print(
              '✅ PeriodTrackingWatcherBloc: Prediction calculation success: ${predictions['periods']?.length ?? 0} periods, ${predictions['ovulations']?.length ?? 0} ovulations');
          return predictions;
        },
      );
    } catch (e) {
      print(
          '❌ PeriodTrackingWatcherBloc: Exception in prediction calculation: $e');
      // Return empty predictions if calculation fails
      return {
        'periods': <DateTime>{},
        'ovulations': <DateTime>{},
      };
    }
  }

  /// Generate immediate fallback predictions based on existing period data
  Map<String, Set<DateTime>> _generateFallbackPredictions(
      Map<String, Map<String, PeriodTrackingModel>> yearData) {
    print('🎯 PeriodTrackingWatcherBloc: Generating fallback predictions...');

    final predictions = <String, Set<DateTime>>{
      'periods': <DateTime>{},
      'ovulations': <DateTime>{},
    };

    // Extract existing period dates
    final existingPeriodDates = <DateTime>{};
    for (final monthEntry in yearData.entries) {
      for (final dayEntry in monthEntry.value.entries) {
        final model = dayEntry.value;
        if (model.isPeriodDate == true && model.date != null) {
          existingPeriodDates.add(model.date!);
        }
      }
    }

    if (existingPeriodDates.isEmpty) {
      print('🎯 No existing period dates for fallback predictions');
      return predictions;
    }

    // Find the most recent period date
    final sortedDates = existingPeriodDates.toList()..sort();
    final lastPeriodDate = sortedDates.last;
    final today = DateTime.now();

    print('🎯 Last period date for fallback: $lastPeriodDate');

    // Try to get actual health data, fallback to defaults if not available
    int cycleLength = 28; // default
    int periodLength = 5; // default
    const monthsAhead = 6; // predict 6 months ahead

    // TODO: Get actual health data from user profile
    // This could be enhanced to get real health data from the facade
    // For now, use defaults but this is the proper place for this logic

    final futurePeriods = <DateTime>{};
    final futureOvulations = <DateTime>{};

    // Generate future period predictions
    DateTime nextPeriodStart = lastPeriodDate.add(Duration(days: cycleLength));
    final endDate = today.add(Duration(days: monthsAhead * 30));

    while (nextPeriodStart.isBefore(endDate)) {
      // Only predict future dates
      if (nextPeriodStart.isAfter(today)) {
        // Add period dates (periodLength consecutive days)
        for (int i = 0; i < periodLength; i++) {
          final periodDate = nextPeriodStart.add(Duration(days: i));
          if (periodDate.isBefore(endDate)) {
            futurePeriods.add(periodDate);
          }
        }

        // Add ovulation dates (typically 14 days before next period)
        final ovulationStart =
            nextPeriodStart.subtract(const Duration(days: 14));
        if (ovulationStart.isAfter(today)) {
          // Add 5 ovulation dates
          for (int i = 0; i < 5; i++) {
            final ovulationDate = ovulationStart.add(Duration(days: i));
            if (ovulationDate.isBefore(endDate) &&
                ovulationDate.isAfter(today)) {
              futureOvulations.add(ovulationDate);
            }
          }
        }
      }

      // Move to next cycle
      nextPeriodStart = nextPeriodStart.add(Duration(days: cycleLength));
    }

    predictions['periods'] = futurePeriods;
    predictions['ovulations'] = futureOvulations;

    print('🎯 Generated ${futurePeriods.length} fallback period predictions');
    print(
        '🎯 Generated ${futureOvulations.length} fallback ovulation predictions');

    return predictions;
  }

  @override
  Future<void> close() async {
    await _periodStreamSubscription?.cancel();
    return super.close();
  }
}
