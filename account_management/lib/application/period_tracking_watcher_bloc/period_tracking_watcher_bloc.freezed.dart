// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'period_tracking_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PeriodTrackingWatcherEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PeriodTrackingWatcherEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodTrackingWatcherEvent()';
  }
}

/// @nodoc
class $PeriodTrackingWatcherEventCopyWith<$Res> {
  $PeriodTrackingWatcherEventCopyWith(PeriodTrackingWatcherEvent _,
      $Res Function(PeriodTrackingWatcherEvent) __);
}

/// Adds pattern-matching-related methods to [PeriodTrackingWatcherEvent].
extension PeriodTrackingWatcherEventPatterns on PeriodTrackingWatcherEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchYearStarted value)? watchYearStarted,
    TResult Function(_PeriodDataReceived value)? periodDataReceived,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchYearStarted() when watchYearStarted != null:
        return watchYearStarted(_that);
      case _PeriodDataReceived() when periodDataReceived != null:
        return periodDataReceived(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchYearStarted value) watchYearStarted,
    required TResult Function(_PeriodDataReceived value) periodDataReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchYearStarted():
        return watchYearStarted(_that);
      case _PeriodDataReceived():
        return periodDataReceived(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchYearStarted value)? watchYearStarted,
    TResult? Function(_PeriodDataReceived value)? periodDataReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchYearStarted() when watchYearStarted != null:
        return watchYearStarted(_that);
      case _PeriodDataReceived() when periodDataReceived != null:
        return periodDataReceived(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year)? watchYearStarted,
    TResult Function(
            Do<PeriodTrackingFailure,
                    Map<String, Map<String, PeriodTrackingModel>>>
                failureOrData)?
        periodDataReceived,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchYearStarted() when watchYearStarted != null:
        return watchYearStarted(_that.year);
      case _PeriodDataReceived() when periodDataReceived != null:
        return periodDataReceived(_that.failureOrData);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year) watchYearStarted,
    required TResult Function(
            Do<PeriodTrackingFailure,
                    Map<String, Map<String, PeriodTrackingModel>>>
                failureOrData)
        periodDataReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchYearStarted():
        return watchYearStarted(_that.year);
      case _PeriodDataReceived():
        return periodDataReceived(_that.failureOrData);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year)? watchYearStarted,
    TResult? Function(
            Do<PeriodTrackingFailure,
                    Map<String, Map<String, PeriodTrackingModel>>>
                failureOrData)?
        periodDataReceived,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchYearStarted() when watchYearStarted != null:
        return watchYearStarted(_that.year);
      case _PeriodDataReceived() when periodDataReceived != null:
        return periodDataReceived(_that.failureOrData);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _WatchYearStarted implements PeriodTrackingWatcherEvent {
  const _WatchYearStarted(this.year);

  final int year;

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$WatchYearStartedCopyWith<_WatchYearStarted> get copyWith =>
      __$WatchYearStartedCopyWithImpl<_WatchYearStarted>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _WatchYearStarted &&
            (identical(other.year, year) || other.year == year));
  }

  @override
  int get hashCode => Object.hash(runtimeType, year);

  @override
  String toString() {
    return 'PeriodTrackingWatcherEvent.watchYearStarted(year: $year)';
  }
}

/// @nodoc
abstract mixin class _$WatchYearStartedCopyWith<$Res>
    implements $PeriodTrackingWatcherEventCopyWith<$Res> {
  factory _$WatchYearStartedCopyWith(
          _WatchYearStarted value, $Res Function(_WatchYearStarted) _then) =
      __$WatchYearStartedCopyWithImpl;
  @useResult
  $Res call({int year});
}

/// @nodoc
class __$WatchYearStartedCopyWithImpl<$Res>
    implements _$WatchYearStartedCopyWith<$Res> {
  __$WatchYearStartedCopyWithImpl(this._self, this._then);

  final _WatchYearStarted _self;
  final $Res Function(_WatchYearStarted) _then;

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? year = null,
  }) {
    return _then(_WatchYearStarted(
      null == year
          ? _self.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _PeriodDataReceived implements PeriodTrackingWatcherEvent {
  const _PeriodDataReceived(this.failureOrData);

  final Do<PeriodTrackingFailure, Map<String, Map<String, PeriodTrackingModel>>>
      failureOrData;

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PeriodDataReceivedCopyWith<_PeriodDataReceived> get copyWith =>
      __$PeriodDataReceivedCopyWithImpl<_PeriodDataReceived>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PeriodDataReceived &&
            (identical(other.failureOrData, failureOrData) ||
                other.failureOrData == failureOrData));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failureOrData);

  @override
  String toString() {
    return 'PeriodTrackingWatcherEvent.periodDataReceived(failureOrData: $failureOrData)';
  }
}

/// @nodoc
abstract mixin class _$PeriodDataReceivedCopyWith<$Res>
    implements $PeriodTrackingWatcherEventCopyWith<$Res> {
  factory _$PeriodDataReceivedCopyWith(
          _PeriodDataReceived value, $Res Function(_PeriodDataReceived) _then) =
      __$PeriodDataReceivedCopyWithImpl;
  @useResult
  $Res call(
      {Do<PeriodTrackingFailure, Map<String, Map<String, PeriodTrackingModel>>>
          failureOrData});
}

/// @nodoc
class __$PeriodDataReceivedCopyWithImpl<$Res>
    implements _$PeriodDataReceivedCopyWith<$Res> {
  __$PeriodDataReceivedCopyWithImpl(this._self, this._then);

  final _PeriodDataReceived _self;
  final $Res Function(_PeriodDataReceived) _then;

  /// Create a copy of PeriodTrackingWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failureOrData = null,
  }) {
    return _then(_PeriodDataReceived(
      null == failureOrData
          ? _self.failureOrData
          : failureOrData // ignore: cast_nullable_to_non_nullable
              as Do<PeriodTrackingFailure,
                  Map<String, Map<String, PeriodTrackingModel>>>,
    ));
  }
}

/// @nodoc
mixin _$PeriodTrackingWatcherState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PeriodTrackingWatcherState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodTrackingWatcherState()';
  }
}

/// @nodoc
class $PeriodTrackingWatcherStateCopyWith<$Res> {
  $PeriodTrackingWatcherStateCopyWith(PeriodTrackingWatcherState _,
      $Res Function(PeriodTrackingWatcherState) __);
}

/// Adds pattern-matching-related methods to [PeriodTrackingWatcherState].
extension PeriodTrackingWatcherStatePatterns on PeriodTrackingWatcherState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadSuccess value)? loadSuccess,
    TResult Function(_LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _LoadSuccess() when loadSuccess != null:
        return loadSuccess(_that);
      case _LoadFailure() when loadFailure != null:
        return loadFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadSuccess value) loadSuccess,
    required TResult Function(_LoadFailure value) loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _Loading():
        return loading(_that);
      case _LoadSuccess():
        return loadSuccess(_that);
      case _LoadFailure():
        return loadFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadSuccess value)? loadSuccess,
    TResult? Function(_LoadFailure value)? loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _LoadSuccess() when loadSuccess != null:
        return loadSuccess(_that);
      case _LoadFailure() when loadFailure != null:
        return loadFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)?
        loadSuccess,
    TResult Function(PeriodTrackingFailure failure)? loadFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _LoadSuccess() when loadSuccess != null:
        return loadSuccess(_that.yearData, _that.futurePredictions);
      case _LoadFailure() when loadFailure != null:
        return loadFailure(_that.failure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)
        loadSuccess,
    required TResult Function(PeriodTrackingFailure failure) loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _Loading():
        return loading();
      case _LoadSuccess():
        return loadSuccess(_that.yearData, _that.futurePredictions);
      case _LoadFailure():
        return loadFailure(_that.failure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Map<String, Map<String, PeriodTrackingModel>> yearData,
            Map<String, Set<DateTime>>? futurePredictions)?
        loadSuccess,
    TResult? Function(PeriodTrackingFailure failure)? loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _LoadSuccess() when loadSuccess != null:
        return loadSuccess(_that.yearData, _that.futurePredictions);
      case _LoadFailure() when loadFailure != null:
        return loadFailure(_that.failure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements PeriodTrackingWatcherState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodTrackingWatcherState.initial()';
  }
}

/// @nodoc

class _Loading implements PeriodTrackingWatcherState {
  const _Loading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Loading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodTrackingWatcherState.loading()';
  }
}

/// @nodoc

class _LoadSuccess implements PeriodTrackingWatcherState {
  const _LoadSuccess(
      final Map<String, Map<String, PeriodTrackingModel>> yearData,
      {final Map<String, Set<DateTime>>? futurePredictions})
      : _yearData = yearData,
        _futurePredictions = futurePredictions;

  final Map<String, Map<String, PeriodTrackingModel>> _yearData;
  Map<String, Map<String, PeriodTrackingModel>> get yearData {
    if (_yearData is EqualUnmodifiableMapView) return _yearData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_yearData);
  }

  final Map<String, Set<DateTime>>? _futurePredictions;
  Map<String, Set<DateTime>>? get futurePredictions {
    final value = _futurePredictions;
    if (value == null) return null;
    if (_futurePredictions is EqualUnmodifiableMapView)
      return _futurePredictions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadSuccessCopyWith<_LoadSuccess> get copyWith =>
      __$LoadSuccessCopyWithImpl<_LoadSuccess>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoadSuccess &&
            const DeepCollectionEquality().equals(other._yearData, _yearData) &&
            const DeepCollectionEquality()
                .equals(other._futurePredictions, _futurePredictions));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_yearData),
      const DeepCollectionEquality().hash(_futurePredictions));

  @override
  String toString() {
    return 'PeriodTrackingWatcherState.loadSuccess(yearData: $yearData, futurePredictions: $futurePredictions)';
  }
}

/// @nodoc
abstract mixin class _$LoadSuccessCopyWith<$Res>
    implements $PeriodTrackingWatcherStateCopyWith<$Res> {
  factory _$LoadSuccessCopyWith(
          _LoadSuccess value, $Res Function(_LoadSuccess) _then) =
      __$LoadSuccessCopyWithImpl;
  @useResult
  $Res call(
      {Map<String, Map<String, PeriodTrackingModel>> yearData,
      Map<String, Set<DateTime>>? futurePredictions});
}

/// @nodoc
class __$LoadSuccessCopyWithImpl<$Res> implements _$LoadSuccessCopyWith<$Res> {
  __$LoadSuccessCopyWithImpl(this._self, this._then);

  final _LoadSuccess _self;
  final $Res Function(_LoadSuccess) _then;

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? yearData = null,
    Object? futurePredictions = freezed,
  }) {
    return _then(_LoadSuccess(
      null == yearData
          ? _self._yearData
          : yearData // ignore: cast_nullable_to_non_nullable
              as Map<String, Map<String, PeriodTrackingModel>>,
      futurePredictions: freezed == futurePredictions
          ? _self._futurePredictions
          : futurePredictions // ignore: cast_nullable_to_non_nullable
              as Map<String, Set<DateTime>>?,
    ));
  }
}

/// @nodoc

class _LoadFailure implements PeriodTrackingWatcherState {
  const _LoadFailure(this.failure);

  final PeriodTrackingFailure failure;

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadFailureCopyWith<_LoadFailure> get copyWith =>
      __$LoadFailureCopyWithImpl<_LoadFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoadFailure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'PeriodTrackingWatcherState.loadFailure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class _$LoadFailureCopyWith<$Res>
    implements $PeriodTrackingWatcherStateCopyWith<$Res> {
  factory _$LoadFailureCopyWith(
          _LoadFailure value, $Res Function(_LoadFailure) _then) =
      __$LoadFailureCopyWithImpl;
  @useResult
  $Res call({PeriodTrackingFailure failure});

  $PeriodTrackingFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$LoadFailureCopyWithImpl<$Res> implements _$LoadFailureCopyWith<$Res> {
  __$LoadFailureCopyWithImpl(this._self, this._then);

  final _LoadFailure _self;
  final $Res Function(_LoadFailure) _then;

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(_LoadFailure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as PeriodTrackingFailure,
    ));
  }

  /// Create a copy of PeriodTrackingWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PeriodTrackingFailureCopyWith<$Res> get failure {
    return $PeriodTrackingFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

// dart format on
