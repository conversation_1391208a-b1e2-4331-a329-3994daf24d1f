part of 'period_tracking_watcher_bloc.dart';

@freezed
class PeriodTrackingWatcherEvent with _$PeriodTrackingWatcherEvent {
  const factory PeriodTrackingWatcherEvent.watchYearStarted(int year) =
      _WatchYearStarted;
  const factory PeriodTrackingWatcherEvent.periodDataReceived(
      Do<PeriodTrackingFailure,
              Map<String, Map<String, PeriodTrackingModel>>>
          failureOrData) = _PeriodDataReceived;
}
