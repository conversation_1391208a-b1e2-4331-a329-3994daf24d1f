// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'symptom_watcher_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SymptomWatcherEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SymptomWatcherEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomWatcherEvent()';
  }
}

/// @nodoc
class $SymptomWatcherEventCopyWith<$Res> {
  $SymptomWatcherEventCopyWith(
      SymptomWatcherEvent _, $Res Function(SymptomWatcherEvent) __);
}

/// Adds pattern-matching-related methods to [SymptomWatcherEvent].
extension SymptomWatcherEventPatterns on SymptomWatcherEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_WatchStarted value)? watchStarted,
    TResult Function(_SymptomsReceived value)? symptomsReceived,
    TResult Function(_SyncRequested value)? syncRequested,
    TResult Function(_ClearCache value)? clearCache,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchStarted() when watchStarted != null:
        return watchStarted(_that);
      case _SymptomsReceived() when symptomsReceived != null:
        return symptomsReceived(_that);
      case _SyncRequested() when syncRequested != null:
        return syncRequested(_that);
      case _ClearCache() when clearCache != null:
        return clearCache(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_WatchStarted value) watchStarted,
    required TResult Function(_SymptomsReceived value) symptomsReceived,
    required TResult Function(_SyncRequested value) syncRequested,
    required TResult Function(_ClearCache value) clearCache,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchStarted():
        return watchStarted(_that);
      case _SymptomsReceived():
        return symptomsReceived(_that);
      case _SyncRequested():
        return syncRequested(_that);
      case _ClearCache():
        return clearCache(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_WatchStarted value)? watchStarted,
    TResult? Function(_SymptomsReceived value)? symptomsReceived,
    TResult? Function(_SyncRequested value)? syncRequested,
    TResult? Function(_ClearCache value)? clearCache,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchStarted() when watchStarted != null:
        return watchStarted(_that);
      case _SymptomsReceived() when symptomsReceived != null:
        return symptomsReceived(_that);
      case _SyncRequested() when syncRequested != null:
        return syncRequested(_that);
      case _ClearCache() when clearCache != null:
        return clearCache(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? watchStarted,
    TResult Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        symptomsReceived,
    TResult Function()? syncRequested,
    TResult Function()? clearCache,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _WatchStarted() when watchStarted != null:
        return watchStarted();
      case _SymptomsReceived() when symptomsReceived != null:
        return symptomsReceived(_that.symptomsByCategory);
      case _SyncRequested() when syncRequested != null:
        return syncRequested();
      case _ClearCache() when clearCache != null:
        return clearCache();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() watchStarted,
    required TResult Function(
            Map<String, List<SymptomModel>> symptomsByCategory)
        symptomsReceived,
    required TResult Function() syncRequested,
    required TResult Function() clearCache,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchStarted():
        return watchStarted();
      case _SymptomsReceived():
        return symptomsReceived(_that.symptomsByCategory);
      case _SyncRequested():
        return syncRequested();
      case _ClearCache():
        return clearCache();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? watchStarted,
    TResult? Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        symptomsReceived,
    TResult? Function()? syncRequested,
    TResult? Function()? clearCache,
  }) {
    final _that = this;
    switch (_that) {
      case _WatchStarted() when watchStarted != null:
        return watchStarted();
      case _SymptomsReceived() when symptomsReceived != null:
        return symptomsReceived(_that.symptomsByCategory);
      case _SyncRequested() when syncRequested != null:
        return syncRequested();
      case _ClearCache() when clearCache != null:
        return clearCache();
      case _:
        return null;
    }
  }
}

/// @nodoc

class _WatchStarted implements SymptomWatcherEvent {
  const _WatchStarted();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _WatchStarted);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomWatcherEvent.watchStarted()';
  }
}

/// @nodoc

class _SymptomsReceived implements SymptomWatcherEvent {
  const _SymptomsReceived(
      {required final Map<String, List<SymptomModel>> symptomsByCategory})
      : _symptomsByCategory = symptomsByCategory;

  final Map<String, List<SymptomModel>> _symptomsByCategory;
  Map<String, List<SymptomModel>> get symptomsByCategory {
    if (_symptomsByCategory is EqualUnmodifiableMapView)
      return _symptomsByCategory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_symptomsByCategory);
  }

  /// Create a copy of SymptomWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SymptomsReceivedCopyWith<_SymptomsReceived> get copyWith =>
      __$SymptomsReceivedCopyWithImpl<_SymptomsReceived>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SymptomsReceived &&
            const DeepCollectionEquality()
                .equals(other._symptomsByCategory, _symptomsByCategory));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_symptomsByCategory));

  @override
  String toString() {
    return 'SymptomWatcherEvent.symptomsReceived(symptomsByCategory: $symptomsByCategory)';
  }
}

/// @nodoc
abstract mixin class _$SymptomsReceivedCopyWith<$Res>
    implements $SymptomWatcherEventCopyWith<$Res> {
  factory _$SymptomsReceivedCopyWith(
          _SymptomsReceived value, $Res Function(_SymptomsReceived) _then) =
      __$SymptomsReceivedCopyWithImpl;
  @useResult
  $Res call({Map<String, List<SymptomModel>> symptomsByCategory});
}

/// @nodoc
class __$SymptomsReceivedCopyWithImpl<$Res>
    implements _$SymptomsReceivedCopyWith<$Res> {
  __$SymptomsReceivedCopyWithImpl(this._self, this._then);

  final _SymptomsReceived _self;
  final $Res Function(_SymptomsReceived) _then;

  /// Create a copy of SymptomWatcherEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? symptomsByCategory = null,
  }) {
    return _then(_SymptomsReceived(
      symptomsByCategory: null == symptomsByCategory
          ? _self._symptomsByCategory
          : symptomsByCategory // ignore: cast_nullable_to_non_nullable
              as Map<String, List<SymptomModel>>,
    ));
  }
}

/// @nodoc

class _SyncRequested implements SymptomWatcherEvent {
  const _SyncRequested();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _SyncRequested);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomWatcherEvent.syncRequested()';
  }
}

/// @nodoc

class _ClearCache implements SymptomWatcherEvent {
  const _ClearCache();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ClearCache);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomWatcherEvent.clearCache()';
  }
}

/// @nodoc
mixin _$SymptomWatcherState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SymptomWatcherState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomWatcherState()';
  }
}

/// @nodoc
class $SymptomWatcherStateCopyWith<$Res> {
  $SymptomWatcherStateCopyWith(
      SymptomWatcherState _, $Res Function(SymptomWatcherState) __);
}

/// Adds pattern-matching-related methods to [SymptomWatcherState].
extension SymptomWatcherStatePatterns on SymptomWatcherState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_LoadSuccess value)? loadSuccess,
    TResult Function(_LoadFailure value)? loadFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _LoadSuccess() when loadSuccess != null:
        return loadSuccess(_that);
      case _LoadFailure() when loadFailure != null:
        return loadFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_LoadSuccess value) loadSuccess,
    required TResult Function(_LoadFailure value) loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _Loading():
        return loading(_that);
      case _LoadSuccess():
        return loadSuccess(_that);
      case _LoadFailure():
        return loadFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_LoadSuccess value)? loadSuccess,
    TResult? Function(_LoadFailure value)? loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _LoadSuccess() when loadSuccess != null:
        return loadSuccess(_that);
      case _LoadFailure() when loadFailure != null:
        return loadFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        loadSuccess,
    TResult Function(SymptomManagementFailure failure)? loadFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _LoadSuccess() when loadSuccess != null:
        return loadSuccess(_that.symptomsByCategory);
      case _LoadFailure() when loadFailure != null:
        return loadFailure(_that.failure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function(
            Map<String, List<SymptomModel>> symptomsByCategory)
        loadSuccess,
    required TResult Function(SymptomManagementFailure failure) loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _Loading():
        return loading();
      case _LoadSuccess():
        return loadSuccess(_that.symptomsByCategory);
      case _LoadFailure():
        return loadFailure(_that.failure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function(Map<String, List<SymptomModel>> symptomsByCategory)?
        loadSuccess,
    TResult? Function(SymptomManagementFailure failure)? loadFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _LoadSuccess() when loadSuccess != null:
        return loadSuccess(_that.symptomsByCategory);
      case _LoadFailure() when loadFailure != null:
        return loadFailure(_that.failure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements SymptomWatcherState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomWatcherState.initial()';
  }
}

/// @nodoc

class _Loading implements SymptomWatcherState {
  const _Loading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Loading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomWatcherState.loading()';
  }
}

/// @nodoc

class _LoadSuccess implements SymptomWatcherState {
  const _LoadSuccess(
      {required final Map<String, List<SymptomModel>> symptomsByCategory})
      : _symptomsByCategory = symptomsByCategory;

  final Map<String, List<SymptomModel>> _symptomsByCategory;
  Map<String, List<SymptomModel>> get symptomsByCategory {
    if (_symptomsByCategory is EqualUnmodifiableMapView)
      return _symptomsByCategory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_symptomsByCategory);
  }

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadSuccessCopyWith<_LoadSuccess> get copyWith =>
      __$LoadSuccessCopyWithImpl<_LoadSuccess>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoadSuccess &&
            const DeepCollectionEquality()
                .equals(other._symptomsByCategory, _symptomsByCategory));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_symptomsByCategory));

  @override
  String toString() {
    return 'SymptomWatcherState.loadSuccess(symptomsByCategory: $symptomsByCategory)';
  }
}

/// @nodoc
abstract mixin class _$LoadSuccessCopyWith<$Res>
    implements $SymptomWatcherStateCopyWith<$Res> {
  factory _$LoadSuccessCopyWith(
          _LoadSuccess value, $Res Function(_LoadSuccess) _then) =
      __$LoadSuccessCopyWithImpl;
  @useResult
  $Res call({Map<String, List<SymptomModel>> symptomsByCategory});
}

/// @nodoc
class __$LoadSuccessCopyWithImpl<$Res> implements _$LoadSuccessCopyWith<$Res> {
  __$LoadSuccessCopyWithImpl(this._self, this._then);

  final _LoadSuccess _self;
  final $Res Function(_LoadSuccess) _then;

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? symptomsByCategory = null,
  }) {
    return _then(_LoadSuccess(
      symptomsByCategory: null == symptomsByCategory
          ? _self._symptomsByCategory
          : symptomsByCategory // ignore: cast_nullable_to_non_nullable
              as Map<String, List<SymptomModel>>,
    ));
  }
}

/// @nodoc

class _LoadFailure implements SymptomWatcherState {
  const _LoadFailure({required this.failure});

  final SymptomManagementFailure failure;

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadFailureCopyWith<_LoadFailure> get copyWith =>
      __$LoadFailureCopyWithImpl<_LoadFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoadFailure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'SymptomWatcherState.loadFailure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class _$LoadFailureCopyWith<$Res>
    implements $SymptomWatcherStateCopyWith<$Res> {
  factory _$LoadFailureCopyWith(
          _LoadFailure value, $Res Function(_LoadFailure) _then) =
      __$LoadFailureCopyWithImpl;
  @useResult
  $Res call({SymptomManagementFailure failure});

  $SymptomManagementFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$LoadFailureCopyWithImpl<$Res> implements _$LoadFailureCopyWith<$Res> {
  __$LoadFailureCopyWithImpl(this._self, this._then);

  final _LoadFailure _self;
  final $Res Function(_LoadFailure) _then;

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(_LoadFailure(
      failure: null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as SymptomManagementFailure,
    ));
  }

  /// Create a copy of SymptomWatcherState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SymptomManagementFailureCopyWith<$Res> get failure {
    return $SymptomManagementFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

// dart format on
