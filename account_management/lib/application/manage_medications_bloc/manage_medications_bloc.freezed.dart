// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_medications_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ManageMedicationsEvent {
  MedicationModel get medication;

  /// Create a copy of ManageMedicationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ManageMedicationsEventCopyWith<ManageMedicationsEvent> get copyWith =>
      _$ManageMedicationsEventCopyWithImpl<ManageMedicationsEvent>(
          this as ManageMedicationsEvent, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ManageMedicationsEvent &&
            (identical(other.medication, medication) ||
                other.medication == medication));
  }

  @override
  int get hashCode => Object.hash(runtimeType, medication);

  @override
  String toString() {
    return 'ManageMedicationsEvent(medication: $medication)';
  }
}

/// @nodoc
abstract mixin class $ManageMedicationsEventCopyWith<$Res> {
  factory $ManageMedicationsEventCopyWith(ManageMedicationsEvent value,
          $Res Function(ManageMedicationsEvent) _then) =
      _$ManageMedicationsEventCopyWithImpl;
  @useResult
  $Res call({MedicationModel medication});
}

/// @nodoc
class _$ManageMedicationsEventCopyWithImpl<$Res>
    implements $ManageMedicationsEventCopyWith<$Res> {
  _$ManageMedicationsEventCopyWithImpl(this._self, this._then);

  final ManageMedicationsEvent _self;
  final $Res Function(ManageMedicationsEvent) _then;

  /// Create a copy of ManageMedicationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? medication = null,
  }) {
    return _then(_self.copyWith(
      medication: null == medication
          ? _self.medication
          : medication // ignore: cast_nullable_to_non_nullable
              as MedicationModel,
    ));
  }
}

/// Adds pattern-matching-related methods to [ManageMedicationsEvent].
extension ManageMedicationsEventPatterns on ManageMedicationsEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DeleteMedication value)? deleteMedication,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DeleteMedication() when deleteMedication != null:
        return deleteMedication(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DeleteMedication value) deleteMedication,
  }) {
    final _that = this;
    switch (_that) {
      case _DeleteMedication():
        return deleteMedication(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DeleteMedication value)? deleteMedication,
  }) {
    final _that = this;
    switch (_that) {
      case _DeleteMedication() when deleteMedication != null:
        return deleteMedication(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(MedicationModel medication)? deleteMedication,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DeleteMedication() when deleteMedication != null:
        return deleteMedication(_that.medication);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(MedicationModel medication) deleteMedication,
  }) {
    final _that = this;
    switch (_that) {
      case _DeleteMedication():
        return deleteMedication(_that.medication);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(MedicationModel medication)? deleteMedication,
  }) {
    final _that = this;
    switch (_that) {
      case _DeleteMedication() when deleteMedication != null:
        return deleteMedication(_that.medication);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _DeleteMedication implements ManageMedicationsEvent {
  const _DeleteMedication(this.medication);

  @override
  final MedicationModel medication;

  /// Create a copy of ManageMedicationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DeleteMedicationCopyWith<_DeleteMedication> get copyWith =>
      __$DeleteMedicationCopyWithImpl<_DeleteMedication>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DeleteMedication &&
            (identical(other.medication, medication) ||
                other.medication == medication));
  }

  @override
  int get hashCode => Object.hash(runtimeType, medication);

  @override
  String toString() {
    return 'ManageMedicationsEvent.deleteMedication(medication: $medication)';
  }
}

/// @nodoc
abstract mixin class _$DeleteMedicationCopyWith<$Res>
    implements $ManageMedicationsEventCopyWith<$Res> {
  factory _$DeleteMedicationCopyWith(
          _DeleteMedication value, $Res Function(_DeleteMedication) _then) =
      __$DeleteMedicationCopyWithImpl;
  @override
  @useResult
  $Res call({MedicationModel medication});
}

/// @nodoc
class __$DeleteMedicationCopyWithImpl<$Res>
    implements _$DeleteMedicationCopyWith<$Res> {
  __$DeleteMedicationCopyWithImpl(this._self, this._then);

  final _DeleteMedication _self;
  final $Res Function(_DeleteMedication) _then;

  /// Create a copy of ManageMedicationsEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? medication = null,
  }) {
    return _then(_DeleteMedication(
      null == medication
          ? _self.medication
          : medication // ignore: cast_nullable_to_non_nullable
              as MedicationModel,
    ));
  }
}

/// @nodoc
mixin _$ManageMedicationsState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ManageMedicationsState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageMedicationsState()';
  }
}

/// @nodoc
class $ManageMedicationsStateCopyWith<$Res> {
  $ManageMedicationsStateCopyWith(
      ManageMedicationsState _, $Res Function(ManageMedicationsState) __);
}

/// Adds pattern-matching-related methods to [ManageMedicationsState].
extension ManageMedicationsStatePatterns on ManageMedicationsState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_ActionInProgress value)? actionInProgress,
    TResult Function(_MedicationDeleted value)? medicationDeleted,
    TResult Function(_MedicationFailure value)? medicationFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _ActionInProgress() when actionInProgress != null:
        return actionInProgress(_that);
      case _MedicationDeleted() when medicationDeleted != null:
        return medicationDeleted(_that);
      case _MedicationFailure() when medicationFailure != null:
        return medicationFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_ActionInProgress value) actionInProgress,
    required TResult Function(_MedicationDeleted value) medicationDeleted,
    required TResult Function(_MedicationFailure value) medicationFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _ActionInProgress():
        return actionInProgress(_that);
      case _MedicationDeleted():
        return medicationDeleted(_that);
      case _MedicationFailure():
        return medicationFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_ActionInProgress value)? actionInProgress,
    TResult? Function(_MedicationDeleted value)? medicationDeleted,
    TResult? Function(_MedicationFailure value)? medicationFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _ActionInProgress() when actionInProgress != null:
        return actionInProgress(_that);
      case _MedicationDeleted() when medicationDeleted != null:
        return medicationDeleted(_that);
      case _MedicationFailure() when medicationFailure != null:
        return medicationFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? actionInProgress,
    TResult Function()? medicationDeleted,
    TResult Function(MedicationFailure failure)? medicationFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _ActionInProgress() when actionInProgress != null:
        return actionInProgress();
      case _MedicationDeleted() when medicationDeleted != null:
        return medicationDeleted();
      case _MedicationFailure() when medicationFailure != null:
        return medicationFailure(_that.failure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() actionInProgress,
    required TResult Function() medicationDeleted,
    required TResult Function(MedicationFailure failure) medicationFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _ActionInProgress():
        return actionInProgress();
      case _MedicationDeleted():
        return medicationDeleted();
      case _MedicationFailure():
        return medicationFailure(_that.failure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? actionInProgress,
    TResult? Function()? medicationDeleted,
    TResult? Function(MedicationFailure failure)? medicationFailure,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _ActionInProgress() when actionInProgress != null:
        return actionInProgress();
      case _MedicationDeleted() when medicationDeleted != null:
        return medicationDeleted();
      case _MedicationFailure() when medicationFailure != null:
        return medicationFailure(_that.failure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements ManageMedicationsState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageMedicationsState.initial()';
  }
}

/// @nodoc

class _ActionInProgress implements ManageMedicationsState {
  const _ActionInProgress();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ActionInProgress);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageMedicationsState.actionInProgress()';
  }
}

/// @nodoc

class _MedicationDeleted implements ManageMedicationsState {
  const _MedicationDeleted();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _MedicationDeleted);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManageMedicationsState.medicationDeleted()';
  }
}

/// @nodoc

class _MedicationFailure implements ManageMedicationsState {
  const _MedicationFailure(this.failure);

  final MedicationFailure failure;

  /// Create a copy of ManageMedicationsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MedicationFailureCopyWith<_MedicationFailure> get copyWith =>
      __$MedicationFailureCopyWithImpl<_MedicationFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MedicationFailure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'ManageMedicationsState.medicationFailure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class _$MedicationFailureCopyWith<$Res>
    implements $ManageMedicationsStateCopyWith<$Res> {
  factory _$MedicationFailureCopyWith(
          _MedicationFailure value, $Res Function(_MedicationFailure) _then) =
      __$MedicationFailureCopyWithImpl;
  @useResult
  $Res call({MedicationFailure failure});

  $MedicationFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$MedicationFailureCopyWithImpl<$Res>
    implements _$MedicationFailureCopyWith<$Res> {
  __$MedicationFailureCopyWithImpl(this._self, this._then);

  final _MedicationFailure _self;
  final $Res Function(_MedicationFailure) _then;

  /// Create a copy of ManageMedicationsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(_MedicationFailure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as MedicationFailure,
    ));
  }

  /// Create a copy of ManageMedicationsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MedicationFailureCopyWith<$Res> get failure {
    return $MedicationFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

// dart format on
