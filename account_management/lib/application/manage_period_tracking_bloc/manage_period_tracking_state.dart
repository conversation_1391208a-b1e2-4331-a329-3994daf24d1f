part of 'manage_period_tracking_bloc.dart';

@freezed
class ManagePeriodTrackingState with _$ManagePeriodTrackingState {
  const factory ManagePeriodTrackingState.initial() = _Initial;
  const factory ManagePeriodTrackingState.loading() = _Loading;
  const factory ManagePeriodTrackingState.success() = _Success;
  const factory ManagePeriodTrackingState.failure(
      PeriodTrackingFailure failure) = _Failure;
  const factory ManagePeriodTrackingState.dateSelectionChanged({
    required Set<DateTime> selectedDates,
    required Map<DateTime, int> flowLevels,
  }) = _DateSelectionChanged;

  /// Emitted when period dates are successfully updated in Firebase
  /// This triggers ovulation calculations to avoid racing conditions
  const factory ManagePeriodTrackingState.periodDatesUpdated({
    required Set<DateTime> newlySelected,
    required Set<DateTime> newlyDeselected,
    required Set<DateTime> allPeriodDates,
  }) = _PeriodDatesUpdated;
}
