import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';
import '../../domain/facade/period_tracking_facade.dart';
import '../../domain/failure/period_tracking_failure.dart';
part 'manage_period_tracking_event.dart';
part 'manage_period_tracking_state.dart';
part 'manage_period_tracking_bloc.freezed.dart';

@injectable
class ManagePeriodTrackingBloc
    extends Bloc<ManagePeriodTrackingEvent, ManagePeriodTrackingState> {
  final PeriodTrackingFacade _periodTrackingFacade;

  // Local state for period date selection
  Set<DateTime> _selectedDates = {};
  Map<DateTime, int> _flowLevels = {};

  // Track original state to calculate changes
  Set<DateTime> _originalSelectedDates = {};
  Map<DateTime, int> _originalFlowLevels = {};

  ManagePeriodTrackingBloc(this._periodTrackingFacade)
      : super(const ManagePeriodTrackingState.initial()) {
    on<_SelectPeriodDates>(_onSelectPeriodDates);
    on<_DeselectPeriodDates>(_onDeselectPeriodDates);
    on<_DateSelected>(_onDateSelected);
    on<_DateDeselected>(_onDateDeselected);
    on<_SetFlowLevel>(_onSetFlowLevel);
    on<_SavePeriodDates>(_onSavePeriodDates);
    on<_ClearSelection>(_onClearSelection);
    on<_InitializeFromData>(_onInitializeFromData);
  }

  // Getters for current state
  Set<DateTime> get selectedDates => Set.from(_selectedDates);
  Map<DateTime, int> get flowLevels => Map.from(_flowLevels);

  Future<void> _onSelectPeriodDates(
    _SelectPeriodDates event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    emit(const ManagePeriodTrackingState.loading());

    final result = await _periodTrackingFacade.selectPeriodDates(
      event.selectedDates,
      flowLevels: event.flowLevels,
    );

    result.fold(
      onFailure: (failure) => emit(ManagePeriodTrackingState.failure(failure)),
      onSuccess: (_) => emit(const ManagePeriodTrackingState.success()),
    );
  }

  Future<void> _onDeselectPeriodDates(
    _DeselectPeriodDates event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    emit(const ManagePeriodTrackingState.loading());

    final result =
        await _periodTrackingFacade.deselectPeriodDates(event.datesToDeselect);

    result.fold(
      onFailure: (failure) => emit(ManagePeriodTrackingState.failure(failure)),
      onSuccess: (_) => emit(const ManagePeriodTrackingState.success()),
    );
  }

  Future<void> _onDateSelected(
    _DateSelected event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    // Normalize the date to midnight
    final normalizedDate =
        DateTime(event.date.year, event.date.month, event.date.day);

    debugPrint('📅 Selecting date: $normalizedDate');
    _selectedDates.add(normalizedDate);
    debugPrint('📅 Total selected dates: ${_selectedDates.length}');

    emit(ManagePeriodTrackingState.dateSelectionChanged(
      selectedDates: Set.from(_selectedDates),
      flowLevels: Map.from(_flowLevels),
    ));
  }

  Future<void> _onDateDeselected(
    _DateDeselected event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    // Normalize the date to midnight
    final normalizedDate =
        DateTime(event.date.year, event.date.month, event.date.day);

    debugPrint('🗑️ Deselecting date: $normalizedDate');
    debugPrint('🗑️ Before removal - selected dates: ${_selectedDates.length}');

    // Remove the date using a more explicit approach
    _selectedDates.removeWhere((selectedDate) =>
        selectedDate.year == normalizedDate.year &&
        selectedDate.month == normalizedDate.month &&
        selectedDate.day == normalizedDate.day);

    // Remove flow level using the same approach
    _flowLevels.removeWhere((date, _) =>
        date.year == normalizedDate.year &&
        date.month == normalizedDate.month &&
        date.day == normalizedDate.day);

    debugPrint('🗑️ After removal - selected dates: ${_selectedDates.length}');

    emit(ManagePeriodTrackingState.dateSelectionChanged(
      selectedDates: Set.from(_selectedDates),
      flowLevels: Map.from(_flowLevels),
    ));
  }

  Future<void> _onSetFlowLevel(
    _SetFlowLevel event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    // Normalize the date to midnight
    final normalizedDate =
        DateTime(event.date.year, event.date.month, event.date.day);

    debugPrint(
        '💧 Setting flow level ${event.flowLevel} for date: $normalizedDate');
    _flowLevels[normalizedDate] = event.flowLevel;

    emit(ManagePeriodTrackingState.dateSelectionChanged(
      selectedDates: Set.from(_selectedDates),
      flowLevels: Map.from(_flowLevels),
    ));
  }

  Future<void> _onSavePeriodDates(
    _SavePeriodDates event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    emit(const ManagePeriodTrackingState.loading());

    try {
      // Calculate what changed
      final newlySelected = _selectedDates.difference(_originalSelectedDates);
      final newlyDeselected = _originalSelectedDates.difference(_selectedDates);

      debugPrint(
          '💾 Saving changes: ${newlySelected.length} new, ${newlyDeselected.length} removed');
      debugPrint('💾 Current selected dates: ${_selectedDates.length}');
      debugPrint(
          '💾 Original selected dates: ${_originalSelectedDates.length}');
      if (newlySelected.isNotEmpty) {
        debugPrint('💾 Newly selected dates: $newlySelected');
      }
      if (newlyDeselected.isNotEmpty) {
        debugPrint('💾 Newly deselected dates: $newlyDeselected');
      }

      // Handle newly selected dates
      if (newlySelected.isNotEmpty) {
        final flowLevelsForNewDates = <DateTime, int>{};
        for (final date in newlySelected) {
          final flowLevel = _flowLevels[date];
          if (flowLevel != null) {
            flowLevelsForNewDates[date] = flowLevel;
          }
        }

        debugPrint('📅 Selecting ${newlySelected.length} new period dates');
        debugPrint('🔄 About to call selectPeriodDates...');
        final selectResult = await _periodTrackingFacade.selectPeriodDates(
          newlySelected,
          flowLevels: flowLevelsForNewDates,
        );
        debugPrint('🔄 selectPeriodDates completed');

        final selectSuccess = selectResult.fold(
          onFailure: (failure) {
            debugPrint('❌ Failed to select new dates: $failure');
            emit(ManagePeriodTrackingState.failure(failure));
            return false;
          },
          onSuccess: (_) {
            debugPrint('✅ Successfully selected new dates');
            return true;
          },
        );

        if (!selectSuccess) {
          debugPrint('❌ selectSuccess failed, returning early');
          return;
        }
        debugPrint('✅ selectSuccess completed, continuing...');
      }

      // Handle newly deselected dates
      if (newlyDeselected.isNotEmpty) {
        debugPrint('🗑️ Deselecting ${newlyDeselected.length} period dates');
        debugPrint('🔄 About to call deselectPeriodDates...');
        final deselectResult =
            await _periodTrackingFacade.deselectPeriodDates(newlyDeselected);
        debugPrint('🔄 deselectPeriodDates completed');

        final deselectSuccess = deselectResult.fold(
          onFailure: (failure) {
            debugPrint('❌ Failed to deselect dates: $failure');
            emit(ManagePeriodTrackingState.failure(failure));
            return false;
          },
          onSuccess: (_) {
            debugPrint('✅ Successfully deselected dates');
            return true;
          },
        );

        if (!deselectSuccess) {
          debugPrint('❌ deselectSuccess failed, returning early');
          return;
        }
        debugPrint('✅ deselectSuccess completed, continuing...');
      } else {
        debugPrint('ℹ️ No dates to deselect, skipping deselection');
      }

      // Update original state to current state
      _originalSelectedDates = Set.from(_selectedDates);
      _originalFlowLevels = Map.from(_flowLevels);

      debugPrint('🎯 About to emit final state...');
      debugPrint('🎯 newlySelected.isNotEmpty: ${newlySelected.isNotEmpty}');
      debugPrint(
          '🎯 newlyDeselected.isNotEmpty: ${newlyDeselected.isNotEmpty}');

      // Emit success with period tracking changes for ovulation bloc to listen to
      if (newlySelected.isNotEmpty || newlyDeselected.isNotEmpty) {
        debugPrint(
            '📡 Emitting period tracking changes for ovulation calculation');
        emit(ManagePeriodTrackingState.periodDatesUpdated(
          newlySelected: newlySelected,
          newlyDeselected: newlyDeselected,
          allPeriodDates: Set.from(_selectedDates),
        ));
        debugPrint('✅ Successfully emitted periodDatesUpdated state');
      } else {
        debugPrint('📡 No changes detected, emitting success state');
        emit(const ManagePeriodTrackingState.success());
      }
    } catch (e) {
      debugPrint('❌ Error saving period dates: $e');
      emit(const ManagePeriodTrackingState.failure(
        PeriodTrackingFailure.unexpected(),
      ));
    }
  }

  Future<void> _onClearSelection(
    _ClearSelection event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    _selectedDates.clear();
    _flowLevels.clear();
    emit(ManagePeriodTrackingState.dateSelectionChanged(
      selectedDates: Set.from(_selectedDates),
      flowLevels: Map.from(_flowLevels),
    ));
  }

  Future<void> _onInitializeFromData(
    _InitializeFromData event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    // Normalize all dates to midnight
    _selectedDates = event.periodDates
        .map((date) => DateTime(date.year, date.month, date.day))
        .toSet();

    _flowLevels = event.flowLevels.map((date, flowLevel) =>
        MapEntry(DateTime(date.year, date.month, date.day), flowLevel));

    // Store original state for change tracking
    _originalSelectedDates = Set.from(_selectedDates);
    _originalFlowLevels = Map.from(_flowLevels);

    debugPrint('🔄 Initialized with ${_selectedDates.length} period dates');

    emit(ManagePeriodTrackingState.dateSelectionChanged(
      selectedDates: Set.from(_selectedDates),
      flowLevels: Map.from(_flowLevels),
    ));
  }
}
