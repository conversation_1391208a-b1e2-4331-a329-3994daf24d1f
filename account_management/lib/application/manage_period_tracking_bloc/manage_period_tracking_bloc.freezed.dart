// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'manage_period_tracking_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ManagePeriodTrackingEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ManagePeriodTrackingEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent()';
  }
}

/// @nodoc
class $ManagePeriodTrackingEventCopyWith<$Res> {
  $ManagePeriodTrackingEventCopyWith(
      ManagePeriodTrackingEvent _, $Res Function(ManagePeriodTrackingEvent) __);
}

/// Adds pattern-matching-related methods to [ManagePeriodTrackingEvent].
extension ManagePeriodTrackingEventPatterns on ManagePeriodTrackingEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_DateDeselected value)? dateDeselected,
    TResult Function(_SetFlowLevel value)? setFlowLevel,
    TResult Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult Function(_SavePeriodDates value)? savePeriodDates,
    TResult Function(_ClearSelection value)? clearSelection,
    TResult Function(_InitializeFromData value)? initializeFromData,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DateSelected() when dateSelected != null:
        return dateSelected(_that);
      case _DateDeselected() when dateDeselected != null:
        return dateDeselected(_that);
      case _SetFlowLevel() when setFlowLevel != null:
        return setFlowLevel(_that);
      case _SelectPeriodDates() when selectPeriodDates != null:
        return selectPeriodDates(_that);
      case _DeselectPeriodDates() when deselectPeriodDates != null:
        return deselectPeriodDates(_that);
      case _SavePeriodDates() when savePeriodDates != null:
        return savePeriodDates(_that);
      case _ClearSelection() when clearSelection != null:
        return clearSelection(_that);
      case _InitializeFromData() when initializeFromData != null:
        return initializeFromData(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_DateDeselected value) dateDeselected,
    required TResult Function(_SetFlowLevel value) setFlowLevel,
    required TResult Function(_SelectPeriodDates value) selectPeriodDates,
    required TResult Function(_DeselectPeriodDates value) deselectPeriodDates,
    required TResult Function(_SavePeriodDates value) savePeriodDates,
    required TResult Function(_ClearSelection value) clearSelection,
    required TResult Function(_InitializeFromData value) initializeFromData,
  }) {
    final _that = this;
    switch (_that) {
      case _DateSelected():
        return dateSelected(_that);
      case _DateDeselected():
        return dateDeselected(_that);
      case _SetFlowLevel():
        return setFlowLevel(_that);
      case _SelectPeriodDates():
        return selectPeriodDates(_that);
      case _DeselectPeriodDates():
        return deselectPeriodDates(_that);
      case _SavePeriodDates():
        return savePeriodDates(_that);
      case _ClearSelection():
        return clearSelection(_that);
      case _InitializeFromData():
        return initializeFromData(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_DateDeselected value)? dateDeselected,
    TResult? Function(_SetFlowLevel value)? setFlowLevel,
    TResult? Function(_SelectPeriodDates value)? selectPeriodDates,
    TResult? Function(_DeselectPeriodDates value)? deselectPeriodDates,
    TResult? Function(_SavePeriodDates value)? savePeriodDates,
    TResult? Function(_ClearSelection value)? clearSelection,
    TResult? Function(_InitializeFromData value)? initializeFromData,
  }) {
    final _that = this;
    switch (_that) {
      case _DateSelected() when dateSelected != null:
        return dateSelected(_that);
      case _DateDeselected() when dateDeselected != null:
        return dateDeselected(_that);
      case _SetFlowLevel() when setFlowLevel != null:
        return setFlowLevel(_that);
      case _SelectPeriodDates() when selectPeriodDates != null:
        return selectPeriodDates(_that);
      case _DeselectPeriodDates() when deselectPeriodDates != null:
        return deselectPeriodDates(_that);
      case _SavePeriodDates() when savePeriodDates != null:
        return savePeriodDates(_that);
      case _ClearSelection() when clearSelection != null:
        return clearSelection(_that);
      case _InitializeFromData() when initializeFromData != null:
        return initializeFromData(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime date)? dateDeselected,
    TResult Function(DateTime date, int flowLevel)? setFlowLevel,
    TResult Function(
            Set<DateTime> selectedDates, Map<DateTime, int>? flowLevels)?
        selectPeriodDates,
    TResult Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult Function()? savePeriodDates,
    TResult Function()? clearSelection,
    TResult Function(Set<DateTime> periodDates, Map<DateTime, int> flowLevels)?
        initializeFromData,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DateSelected() when dateSelected != null:
        return dateSelected(_that.date);
      case _DateDeselected() when dateDeselected != null:
        return dateDeselected(_that.date);
      case _SetFlowLevel() when setFlowLevel != null:
        return setFlowLevel(_that.date, _that.flowLevel);
      case _SelectPeriodDates() when selectPeriodDates != null:
        return selectPeriodDates(_that.selectedDates, _that.flowLevels);
      case _DeselectPeriodDates() when deselectPeriodDates != null:
        return deselectPeriodDates(_that.datesToDeselect);
      case _SavePeriodDates() when savePeriodDates != null:
        return savePeriodDates();
      case _ClearSelection() when clearSelection != null:
        return clearSelection();
      case _InitializeFromData() when initializeFromData != null:
        return initializeFromData(_that.periodDates, _that.flowLevels);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime date) dateDeselected,
    required TResult Function(DateTime date, int flowLevel) setFlowLevel,
    required TResult Function(
            Set<DateTime> selectedDates, Map<DateTime, int>? flowLevels)
        selectPeriodDates,
    required TResult Function(Set<DateTime> datesToDeselect)
        deselectPeriodDates,
    required TResult Function() savePeriodDates,
    required TResult Function() clearSelection,
    required TResult Function(
            Set<DateTime> periodDates, Map<DateTime, int> flowLevels)
        initializeFromData,
  }) {
    final _that = this;
    switch (_that) {
      case _DateSelected():
        return dateSelected(_that.date);
      case _DateDeselected():
        return dateDeselected(_that.date);
      case _SetFlowLevel():
        return setFlowLevel(_that.date, _that.flowLevel);
      case _SelectPeriodDates():
        return selectPeriodDates(_that.selectedDates, _that.flowLevels);
      case _DeselectPeriodDates():
        return deselectPeriodDates(_that.datesToDeselect);
      case _SavePeriodDates():
        return savePeriodDates();
      case _ClearSelection():
        return clearSelection();
      case _InitializeFromData():
        return initializeFromData(_that.periodDates, _that.flowLevels);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime date)? dateDeselected,
    TResult? Function(DateTime date, int flowLevel)? setFlowLevel,
    TResult? Function(
            Set<DateTime> selectedDates, Map<DateTime, int>? flowLevels)?
        selectPeriodDates,
    TResult? Function(Set<DateTime> datesToDeselect)? deselectPeriodDates,
    TResult? Function()? savePeriodDates,
    TResult? Function()? clearSelection,
    TResult? Function(Set<DateTime> periodDates, Map<DateTime, int> flowLevels)?
        initializeFromData,
  }) {
    final _that = this;
    switch (_that) {
      case _DateSelected() when dateSelected != null:
        return dateSelected(_that.date);
      case _DateDeselected() when dateDeselected != null:
        return dateDeselected(_that.date);
      case _SetFlowLevel() when setFlowLevel != null:
        return setFlowLevel(_that.date, _that.flowLevel);
      case _SelectPeriodDates() when selectPeriodDates != null:
        return selectPeriodDates(_that.selectedDates, _that.flowLevels);
      case _DeselectPeriodDates() when deselectPeriodDates != null:
        return deselectPeriodDates(_that.datesToDeselect);
      case _SavePeriodDates() when savePeriodDates != null:
        return savePeriodDates();
      case _ClearSelection() when clearSelection != null:
        return clearSelection();
      case _InitializeFromData() when initializeFromData != null:
        return initializeFromData(_that.periodDates, _that.flowLevels);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _DateSelected implements ManagePeriodTrackingEvent {
  const _DateSelected(this.date);

  final DateTime date;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DateSelectedCopyWith<_DateSelected> get copyWith =>
      __$DateSelectedCopyWithImpl<_DateSelected>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DateSelected &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date);

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.dateSelected(date: $date)';
  }
}

/// @nodoc
abstract mixin class _$DateSelectedCopyWith<$Res>
    implements $ManagePeriodTrackingEventCopyWith<$Res> {
  factory _$DateSelectedCopyWith(
          _DateSelected value, $Res Function(_DateSelected) _then) =
      __$DateSelectedCopyWithImpl;
  @useResult
  $Res call({DateTime date});
}

/// @nodoc
class __$DateSelectedCopyWithImpl<$Res>
    implements _$DateSelectedCopyWith<$Res> {
  __$DateSelectedCopyWithImpl(this._self, this._then);

  final _DateSelected _self;
  final $Res Function(_DateSelected) _then;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? date = null,
  }) {
    return _then(_DateSelected(
      null == date
          ? _self.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _DateDeselected implements ManagePeriodTrackingEvent {
  const _DateDeselected(this.date);

  final DateTime date;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DateDeselectedCopyWith<_DateDeselected> get copyWith =>
      __$DateDeselectedCopyWithImpl<_DateDeselected>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DateDeselected &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date);

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.dateDeselected(date: $date)';
  }
}

/// @nodoc
abstract mixin class _$DateDeselectedCopyWith<$Res>
    implements $ManagePeriodTrackingEventCopyWith<$Res> {
  factory _$DateDeselectedCopyWith(
          _DateDeselected value, $Res Function(_DateDeselected) _then) =
      __$DateDeselectedCopyWithImpl;
  @useResult
  $Res call({DateTime date});
}

/// @nodoc
class __$DateDeselectedCopyWithImpl<$Res>
    implements _$DateDeselectedCopyWith<$Res> {
  __$DateDeselectedCopyWithImpl(this._self, this._then);

  final _DateDeselected _self;
  final $Res Function(_DateDeselected) _then;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? date = null,
  }) {
    return _then(_DateDeselected(
      null == date
          ? _self.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _SetFlowLevel implements ManagePeriodTrackingEvent {
  const _SetFlowLevel({required this.date, required this.flowLevel});

  final DateTime date;
  final int flowLevel;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SetFlowLevelCopyWith<_SetFlowLevel> get copyWith =>
      __$SetFlowLevelCopyWithImpl<_SetFlowLevel>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SetFlowLevel &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.flowLevel, flowLevel) ||
                other.flowLevel == flowLevel));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date, flowLevel);

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.setFlowLevel(date: $date, flowLevel: $flowLevel)';
  }
}

/// @nodoc
abstract mixin class _$SetFlowLevelCopyWith<$Res>
    implements $ManagePeriodTrackingEventCopyWith<$Res> {
  factory _$SetFlowLevelCopyWith(
          _SetFlowLevel value, $Res Function(_SetFlowLevel) _then) =
      __$SetFlowLevelCopyWithImpl;
  @useResult
  $Res call({DateTime date, int flowLevel});
}

/// @nodoc
class __$SetFlowLevelCopyWithImpl<$Res>
    implements _$SetFlowLevelCopyWith<$Res> {
  __$SetFlowLevelCopyWithImpl(this._self, this._then);

  final _SetFlowLevel _self;
  final $Res Function(_SetFlowLevel) _then;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? date = null,
    Object? flowLevel = null,
  }) {
    return _then(_SetFlowLevel(
      date: null == date
          ? _self.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      flowLevel: null == flowLevel
          ? _self.flowLevel
          : flowLevel // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _SelectPeriodDates implements ManagePeriodTrackingEvent {
  const _SelectPeriodDates(final Set<DateTime> selectedDates,
      {final Map<DateTime, int>? flowLevels})
      : _selectedDates = selectedDates,
        _flowLevels = flowLevels;

  final Set<DateTime> _selectedDates;
  Set<DateTime> get selectedDates {
    if (_selectedDates is EqualUnmodifiableSetView) return _selectedDates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_selectedDates);
  }

  final Map<DateTime, int>? _flowLevels;
  Map<DateTime, int>? get flowLevels {
    final value = _flowLevels;
    if (value == null) return null;
    if (_flowLevels is EqualUnmodifiableMapView) return _flowLevels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SelectPeriodDatesCopyWith<_SelectPeriodDates> get copyWith =>
      __$SelectPeriodDatesCopyWithImpl<_SelectPeriodDates>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SelectPeriodDates &&
            const DeepCollectionEquality()
                .equals(other._selectedDates, _selectedDates) &&
            const DeepCollectionEquality()
                .equals(other._flowLevels, _flowLevels));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_selectedDates),
      const DeepCollectionEquality().hash(_flowLevels));

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.selectPeriodDates(selectedDates: $selectedDates, flowLevels: $flowLevels)';
  }
}

/// @nodoc
abstract mixin class _$SelectPeriodDatesCopyWith<$Res>
    implements $ManagePeriodTrackingEventCopyWith<$Res> {
  factory _$SelectPeriodDatesCopyWith(
          _SelectPeriodDates value, $Res Function(_SelectPeriodDates) _then) =
      __$SelectPeriodDatesCopyWithImpl;
  @useResult
  $Res call({Set<DateTime> selectedDates, Map<DateTime, int>? flowLevels});
}

/// @nodoc
class __$SelectPeriodDatesCopyWithImpl<$Res>
    implements _$SelectPeriodDatesCopyWith<$Res> {
  __$SelectPeriodDatesCopyWithImpl(this._self, this._then);

  final _SelectPeriodDates _self;
  final $Res Function(_SelectPeriodDates) _then;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedDates = null,
    Object? flowLevels = freezed,
  }) {
    return _then(_SelectPeriodDates(
      null == selectedDates
          ? _self._selectedDates
          : selectedDates // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
      flowLevels: freezed == flowLevels
          ? _self._flowLevels
          : flowLevels // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>?,
    ));
  }
}

/// @nodoc

class _DeselectPeriodDates implements ManagePeriodTrackingEvent {
  const _DeselectPeriodDates(final Set<DateTime> datesToDeselect)
      : _datesToDeselect = datesToDeselect;

  final Set<DateTime> _datesToDeselect;
  Set<DateTime> get datesToDeselect {
    if (_datesToDeselect is EqualUnmodifiableSetView) return _datesToDeselect;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_datesToDeselect);
  }

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DeselectPeriodDatesCopyWith<_DeselectPeriodDates> get copyWith =>
      __$DeselectPeriodDatesCopyWithImpl<_DeselectPeriodDates>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DeselectPeriodDates &&
            const DeepCollectionEquality()
                .equals(other._datesToDeselect, _datesToDeselect));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_datesToDeselect));

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.deselectPeriodDates(datesToDeselect: $datesToDeselect)';
  }
}

/// @nodoc
abstract mixin class _$DeselectPeriodDatesCopyWith<$Res>
    implements $ManagePeriodTrackingEventCopyWith<$Res> {
  factory _$DeselectPeriodDatesCopyWith(_DeselectPeriodDates value,
          $Res Function(_DeselectPeriodDates) _then) =
      __$DeselectPeriodDatesCopyWithImpl;
  @useResult
  $Res call({Set<DateTime> datesToDeselect});
}

/// @nodoc
class __$DeselectPeriodDatesCopyWithImpl<$Res>
    implements _$DeselectPeriodDatesCopyWith<$Res> {
  __$DeselectPeriodDatesCopyWithImpl(this._self, this._then);

  final _DeselectPeriodDates _self;
  final $Res Function(_DeselectPeriodDates) _then;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? datesToDeselect = null,
  }) {
    return _then(_DeselectPeriodDates(
      null == datesToDeselect
          ? _self._datesToDeselect
          : datesToDeselect // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ));
  }
}

/// @nodoc

class _SavePeriodDates implements ManagePeriodTrackingEvent {
  const _SavePeriodDates();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _SavePeriodDates);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.savePeriodDates()';
  }
}

/// @nodoc

class _ClearSelection implements ManagePeriodTrackingEvent {
  const _ClearSelection();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _ClearSelection);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.clearSelection()';
  }
}

/// @nodoc

class _InitializeFromData implements ManagePeriodTrackingEvent {
  const _InitializeFromData(
      {required final Set<DateTime> periodDates,
      required final Map<DateTime, int> flowLevels})
      : _periodDates = periodDates,
        _flowLevels = flowLevels;

  final Set<DateTime> _periodDates;
  Set<DateTime> get periodDates {
    if (_periodDates is EqualUnmodifiableSetView) return _periodDates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_periodDates);
  }

  final Map<DateTime, int> _flowLevels;
  Map<DateTime, int> get flowLevels {
    if (_flowLevels is EqualUnmodifiableMapView) return _flowLevels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_flowLevels);
  }

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InitializeFromDataCopyWith<_InitializeFromData> get copyWith =>
      __$InitializeFromDataCopyWithImpl<_InitializeFromData>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InitializeFromData &&
            const DeepCollectionEquality()
                .equals(other._periodDates, _periodDates) &&
            const DeepCollectionEquality()
                .equals(other._flowLevels, _flowLevels));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_periodDates),
      const DeepCollectionEquality().hash(_flowLevels));

  @override
  String toString() {
    return 'ManagePeriodTrackingEvent.initializeFromData(periodDates: $periodDates, flowLevels: $flowLevels)';
  }
}

/// @nodoc
abstract mixin class _$InitializeFromDataCopyWith<$Res>
    implements $ManagePeriodTrackingEventCopyWith<$Res> {
  factory _$InitializeFromDataCopyWith(
          _InitializeFromData value, $Res Function(_InitializeFromData) _then) =
      __$InitializeFromDataCopyWithImpl;
  @useResult
  $Res call({Set<DateTime> periodDates, Map<DateTime, int> flowLevels});
}

/// @nodoc
class __$InitializeFromDataCopyWithImpl<$Res>
    implements _$InitializeFromDataCopyWith<$Res> {
  __$InitializeFromDataCopyWithImpl(this._self, this._then);

  final _InitializeFromData _self;
  final $Res Function(_InitializeFromData) _then;

  /// Create a copy of ManagePeriodTrackingEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? periodDates = null,
    Object? flowLevels = null,
  }) {
    return _then(_InitializeFromData(
      periodDates: null == periodDates
          ? _self._periodDates
          : periodDates // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
      flowLevels: null == flowLevels
          ? _self._flowLevels
          : flowLevels // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
    ));
  }
}

/// @nodoc
mixin _$ManagePeriodTrackingState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ManagePeriodTrackingState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManagePeriodTrackingState()';
  }
}

/// @nodoc
class $ManagePeriodTrackingStateCopyWith<$Res> {
  $ManagePeriodTrackingStateCopyWith(
      ManagePeriodTrackingState _, $Res Function(ManagePeriodTrackingState) __);
}

/// Adds pattern-matching-related methods to [ManagePeriodTrackingState].
extension ManagePeriodTrackingStatePatterns on ManagePeriodTrackingState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Initial value)? initial,
    TResult Function(_Loading value)? loading,
    TResult Function(_Success value)? success,
    TResult Function(_Failure value)? failure,
    TResult Function(_DateSelectionChanged value)? dateSelectionChanged,
    TResult Function(_PeriodDatesUpdated value)? periodDatesUpdated,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Success() when success != null:
        return success(_that);
      case _Failure() when failure != null:
        return failure(_that);
      case _DateSelectionChanged() when dateSelectionChanged != null:
        return dateSelectionChanged(_that);
      case _PeriodDatesUpdated() when periodDatesUpdated != null:
        return periodDatesUpdated(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Initial value) initial,
    required TResult Function(_Loading value) loading,
    required TResult Function(_Success value) success,
    required TResult Function(_Failure value) failure,
    required TResult Function(_DateSelectionChanged value) dateSelectionChanged,
    required TResult Function(_PeriodDatesUpdated value) periodDatesUpdated,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial(_that);
      case _Loading():
        return loading(_that);
      case _Success():
        return success(_that);
      case _Failure():
        return failure(_that);
      case _DateSelectionChanged():
        return dateSelectionChanged(_that);
      case _PeriodDatesUpdated():
        return periodDatesUpdated(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Initial value)? initial,
    TResult? Function(_Loading value)? loading,
    TResult? Function(_Success value)? success,
    TResult? Function(_Failure value)? failure,
    TResult? Function(_DateSelectionChanged value)? dateSelectionChanged,
    TResult? Function(_PeriodDatesUpdated value)? periodDatesUpdated,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial(_that);
      case _Loading() when loading != null:
        return loading(_that);
      case _Success() when success != null:
        return success(_that);
      case _Failure() when failure != null:
        return failure(_that);
      case _DateSelectionChanged() when dateSelectionChanged != null:
        return dateSelectionChanged(_that);
      case _PeriodDatesUpdated() when periodDatesUpdated != null:
        return periodDatesUpdated(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? loading,
    TResult Function()? success,
    TResult Function(PeriodTrackingFailure failure)? failure,
    TResult Function(
            Set<DateTime> selectedDates, Map<DateTime, int> flowLevels)?
        dateSelectionChanged,
    TResult Function(Set<DateTime> newlySelected, Set<DateTime> newlyDeselected,
            Set<DateTime> allPeriodDates)?
        periodDatesUpdated,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _Success() when success != null:
        return success();
      case _Failure() when failure != null:
        return failure(_that.failure);
      case _DateSelectionChanged() when dateSelectionChanged != null:
        return dateSelectionChanged(_that.selectedDates, _that.flowLevels);
      case _PeriodDatesUpdated() when periodDatesUpdated != null:
        return periodDatesUpdated(
            _that.newlySelected, _that.newlyDeselected, _that.allPeriodDates);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() loading,
    required TResult Function() success,
    required TResult Function(PeriodTrackingFailure failure) failure,
    required TResult Function(
            Set<DateTime> selectedDates, Map<DateTime, int> flowLevels)
        dateSelectionChanged,
    required TResult Function(Set<DateTime> newlySelected,
            Set<DateTime> newlyDeselected, Set<DateTime> allPeriodDates)
        periodDatesUpdated,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial():
        return initial();
      case _Loading():
        return loading();
      case _Success():
        return success();
      case _Failure():
        return failure(_that.failure);
      case _DateSelectionChanged():
        return dateSelectionChanged(_that.selectedDates, _that.flowLevels);
      case _PeriodDatesUpdated():
        return periodDatesUpdated(
            _that.newlySelected, _that.newlyDeselected, _that.allPeriodDates);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? loading,
    TResult? Function()? success,
    TResult? Function(PeriodTrackingFailure failure)? failure,
    TResult? Function(
            Set<DateTime> selectedDates, Map<DateTime, int> flowLevels)?
        dateSelectionChanged,
    TResult? Function(Set<DateTime> newlySelected,
            Set<DateTime> newlyDeselected, Set<DateTime> allPeriodDates)?
        periodDatesUpdated,
  }) {
    final _that = this;
    switch (_that) {
      case _Initial() when initial != null:
        return initial();
      case _Loading() when loading != null:
        return loading();
      case _Success() when success != null:
        return success();
      case _Failure() when failure != null:
        return failure(_that.failure);
      case _DateSelectionChanged() when dateSelectionChanged != null:
        return dateSelectionChanged(_that.selectedDates, _that.flowLevels);
      case _PeriodDatesUpdated() when periodDatesUpdated != null:
        return periodDatesUpdated(
            _that.newlySelected, _that.newlyDeselected, _that.allPeriodDates);
      case _:
        return null;
    }
  }
}

/// @nodoc

class _Initial implements ManagePeriodTrackingState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManagePeriodTrackingState.initial()';
  }
}

/// @nodoc

class _Loading implements ManagePeriodTrackingState {
  const _Loading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Loading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManagePeriodTrackingState.loading()';
  }
}

/// @nodoc

class _Success implements ManagePeriodTrackingState {
  const _Success();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Success);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'ManagePeriodTrackingState.success()';
  }
}

/// @nodoc

class _Failure implements ManagePeriodTrackingState {
  const _Failure(this.failure);

  final PeriodTrackingFailure failure;

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FailureCopyWith<_Failure> get copyWith =>
      __$FailureCopyWithImpl<_Failure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Failure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'ManagePeriodTrackingState.failure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class _$FailureCopyWith<$Res>
    implements $ManagePeriodTrackingStateCopyWith<$Res> {
  factory _$FailureCopyWith(_Failure value, $Res Function(_Failure) _then) =
      __$FailureCopyWithImpl;
  @useResult
  $Res call({PeriodTrackingFailure failure});

  $PeriodTrackingFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$FailureCopyWithImpl<$Res> implements _$FailureCopyWith<$Res> {
  __$FailureCopyWithImpl(this._self, this._then);

  final _Failure _self;
  final $Res Function(_Failure) _then;

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(_Failure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as PeriodTrackingFailure,
    ));
  }

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PeriodTrackingFailureCopyWith<$Res> get failure {
    return $PeriodTrackingFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _DateSelectionChanged implements ManagePeriodTrackingState {
  const _DateSelectionChanged(
      {required final Set<DateTime> selectedDates,
      required final Map<DateTime, int> flowLevels})
      : _selectedDates = selectedDates,
        _flowLevels = flowLevels;

  final Set<DateTime> _selectedDates;
  Set<DateTime> get selectedDates {
    if (_selectedDates is EqualUnmodifiableSetView) return _selectedDates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_selectedDates);
  }

  final Map<DateTime, int> _flowLevels;
  Map<DateTime, int> get flowLevels {
    if (_flowLevels is EqualUnmodifiableMapView) return _flowLevels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_flowLevels);
  }

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DateSelectionChangedCopyWith<_DateSelectionChanged> get copyWith =>
      __$DateSelectionChangedCopyWithImpl<_DateSelectionChanged>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DateSelectionChanged &&
            const DeepCollectionEquality()
                .equals(other._selectedDates, _selectedDates) &&
            const DeepCollectionEquality()
                .equals(other._flowLevels, _flowLevels));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_selectedDates),
      const DeepCollectionEquality().hash(_flowLevels));

  @override
  String toString() {
    return 'ManagePeriodTrackingState.dateSelectionChanged(selectedDates: $selectedDates, flowLevels: $flowLevels)';
  }
}

/// @nodoc
abstract mixin class _$DateSelectionChangedCopyWith<$Res>
    implements $ManagePeriodTrackingStateCopyWith<$Res> {
  factory _$DateSelectionChangedCopyWith(_DateSelectionChanged value,
          $Res Function(_DateSelectionChanged) _then) =
      __$DateSelectionChangedCopyWithImpl;
  @useResult
  $Res call({Set<DateTime> selectedDates, Map<DateTime, int> flowLevels});
}

/// @nodoc
class __$DateSelectionChangedCopyWithImpl<$Res>
    implements _$DateSelectionChangedCopyWith<$Res> {
  __$DateSelectionChangedCopyWithImpl(this._self, this._then);

  final _DateSelectionChanged _self;
  final $Res Function(_DateSelectionChanged) _then;

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? selectedDates = null,
    Object? flowLevels = null,
  }) {
    return _then(_DateSelectionChanged(
      selectedDates: null == selectedDates
          ? _self._selectedDates
          : selectedDates // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
      flowLevels: null == flowLevels
          ? _self._flowLevels
          : flowLevels // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
    ));
  }
}

/// @nodoc

class _PeriodDatesUpdated implements ManagePeriodTrackingState {
  const _PeriodDatesUpdated(
      {required final Set<DateTime> newlySelected,
      required final Set<DateTime> newlyDeselected,
      required final Set<DateTime> allPeriodDates})
      : _newlySelected = newlySelected,
        _newlyDeselected = newlyDeselected,
        _allPeriodDates = allPeriodDates;

  final Set<DateTime> _newlySelected;
  Set<DateTime> get newlySelected {
    if (_newlySelected is EqualUnmodifiableSetView) return _newlySelected;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_newlySelected);
  }

  final Set<DateTime> _newlyDeselected;
  Set<DateTime> get newlyDeselected {
    if (_newlyDeselected is EqualUnmodifiableSetView) return _newlyDeselected;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_newlyDeselected);
  }

  final Set<DateTime> _allPeriodDates;
  Set<DateTime> get allPeriodDates {
    if (_allPeriodDates is EqualUnmodifiableSetView) return _allPeriodDates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_allPeriodDates);
  }

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PeriodDatesUpdatedCopyWith<_PeriodDatesUpdated> get copyWith =>
      __$PeriodDatesUpdatedCopyWithImpl<_PeriodDatesUpdated>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PeriodDatesUpdated &&
            const DeepCollectionEquality()
                .equals(other._newlySelected, _newlySelected) &&
            const DeepCollectionEquality()
                .equals(other._newlyDeselected, _newlyDeselected) &&
            const DeepCollectionEquality()
                .equals(other._allPeriodDates, _allPeriodDates));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_newlySelected),
      const DeepCollectionEquality().hash(_newlyDeselected),
      const DeepCollectionEquality().hash(_allPeriodDates));

  @override
  String toString() {
    return 'ManagePeriodTrackingState.periodDatesUpdated(newlySelected: $newlySelected, newlyDeselected: $newlyDeselected, allPeriodDates: $allPeriodDates)';
  }
}

/// @nodoc
abstract mixin class _$PeriodDatesUpdatedCopyWith<$Res>
    implements $ManagePeriodTrackingStateCopyWith<$Res> {
  factory _$PeriodDatesUpdatedCopyWith(
          _PeriodDatesUpdated value, $Res Function(_PeriodDatesUpdated) _then) =
      __$PeriodDatesUpdatedCopyWithImpl;
  @useResult
  $Res call(
      {Set<DateTime> newlySelected,
      Set<DateTime> newlyDeselected,
      Set<DateTime> allPeriodDates});
}

/// @nodoc
class __$PeriodDatesUpdatedCopyWithImpl<$Res>
    implements _$PeriodDatesUpdatedCopyWith<$Res> {
  __$PeriodDatesUpdatedCopyWithImpl(this._self, this._then);

  final _PeriodDatesUpdated _self;
  final $Res Function(_PeriodDatesUpdated) _then;

  /// Create a copy of ManagePeriodTrackingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? newlySelected = null,
    Object? newlyDeselected = null,
    Object? allPeriodDates = null,
  }) {
    return _then(_PeriodDatesUpdated(
      newlySelected: null == newlySelected
          ? _self._newlySelected
          : newlySelected // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
      newlyDeselected: null == newlyDeselected
          ? _self._newlyDeselected
          : newlyDeselected // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
      allPeriodDates: null == allPeriodDates
          ? _self._allPeriodDates
          : allPeriodDates // ignore: cast_nullable_to_non_nullable
              as Set<DateTime>,
    ));
  }
}

// dart format on
