part of 'manage_period_tracking_bloc.dart';

@freezed
class ManagePeriodTrackingEvent with _$ManagePeriodTrackingEvent {
  const factory ManagePeriodTrackingEvent.dateSelected(DateTime date) =
      _DateSelected;

  const factory ManagePeriodTrackingEvent.dateDeselected(DateTime date) =
      _DateDeselected;

  const factory ManagePeriodTrackingEvent.setFlowLevel({
    required DateTime date,
    required int flowLevel,
  }) = _SetFlowLevel;

  const factory ManagePeriodTrackingEvent.selectPeriodDates(
    Set<DateTime> selectedDates, {
    Map<DateTime, int>? flowLevels,
  }) = _SelectPeriodDates;

  const factory ManagePeriodTrackingEvent.deselectPeriodDates(
      Set<DateTime> datesToDeselect) = _DeselectPeriodDates;

  const factory ManagePeriodTrackingEvent.savePeriodDates() = _SavePeriodDates;

  const factory ManagePeriodTrackingEvent.clearSelection() = _ClearSelection;

  const factory ManagePeriodTrackingEvent.initializeFromData({
    required Set<DateTime> periodDates,
    required Map<DateTime, int> flowLevels,
  }) = _InitializeFromData;
}
