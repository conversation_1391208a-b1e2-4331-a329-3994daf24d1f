import 'package:doso/doso.dart';
import '../core/unit.dart';
import '../failure/ovulation_failure.dart';

abstract class OvulationFacade {
  /// Main method: Handle period tracking changes and update ovulation dates accordingly
  /// This is the primary entry point used by the UI
  Future<Do<OvulationFailure, Unit>> handlePeriodTrackingChanges({
    required Set<DateTime> newlySelected,
    required Set<DateTime> newlyDeselected,
    required Set<DateTime> allPeriodDates,
  });

  /// Get all existing ovulation dates from Firestore
  /// Returns a set of normalized DateTime objects representing ovulation dates
  Future<Do<OvulationFailure, Set<DateTime>>> getAllExistingOvulationDates();

  /// Clear all ovulation dates for a user (used for data cleanup)
  Future<Do<OvulationFailure, Unit>> clearAllOvulationDates();
}
