import 'package:doso/doso.dart';
import '../core/unit.dart';
import '../failure/period_tracking_failure.dart';
import '../model/period_tracking_model.dart';

abstract class PeriodTrackingFacade {
  /// Watches period tracking data for a specific year.
  /// Returns a stream of Do<PeriodTrackingFailure, Map<String, Map<String, PeriodTrackingModel>>>
  /// where the outer map key is month (e.g., "2025_01") and inner map key is day (e.g., "01")
  Stream<
      Do<PeriodTrackingFailure,
          Map<String, Map<String, PeriodTrackingModel>>>> watchYearData(
      int year);

  /// Selects period dates in Firestore (sets isPeriodDate: true) with optional flow levels
  Future<Do<PeriodTrackingFailure, Unit>> selectPeriodDates(
    Set<DateTime> selectedDates, {
    Map<DateTime, int>? flowLevels,
  });

  /// Deselects period dates in Firestore (sets isPeriodDate: false)
  Future<Do<PeriodTrackingFailure, Unit>> deselectPeriodDates(
      Set<DateTime> datesToDeselect);

  // ===== PREDICTION METHODS =====

  /// Updates prediction metadata when new period data is logged
  Future<Do<PeriodTrackingFailure, Unit>> updatePredictionMetadata(
    Map<String, Map<String, PeriodTrackingModel>> yearData,
  );

  /// Detects new period starts from flow symptoms and triggers prediction updates
  Future<Do<PeriodTrackingFailure, Unit>> handleFlowSymptomUpdate(
    DateTime date,
    int? flowLevel,
    Map<String, Map<String, PeriodTrackingModel>> yearData,
  );

  /// Generates future predictions based on current metadata
  Future<Do<PeriodTrackingFailure, Map<String, Set<DateTime>>>>
      generatePredictions({int monthsAhead = 6});

  /// Initializes predictions from onboarding data
  Future<Do<PeriodTrackingFailure, Unit>> initializePredictionsFromOnboarding(
    DateTime lastPeriodDate,
    int cycleLength,
    int periodLength,
  );
}
