import 'package:doso/doso.dart';
import '../core/unit.dart';
import '../failure/period_tracking_failure.dart';
import '../model/period_tracking_model.dart';
import '../model/symptom_model.dart';

abstract class SymptomTrackingFacade {
  /// Gets symptom data for a specific date
  Future<Do<PeriodTrackingFailure, PeriodTrackingModel?>> getSymptomData({
    required DateTime date,
  });

  /// Saves symptom data for a specific date
  Future<Do<PeriodTrackingFailure, Unit>> saveSymptomData({
    required DateTime date,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
  });

  /// Save flow level only for a specific date
  Future<Do<PeriodTrackingFailure, Unit>> saveFlowLevel({
    required DateTime date,
    required int flowLevel,
  });

  /// Save pain level only for a specific date
  Future<Do<PeriodTrackingFailure, Unit>> savePainLevel({
    required DateTime date,
    required int painLevel,
  });

  /// Get flow level only for a specific date
  Future<int?> getFlowLevel(DateTime date);

  /// Get pain level only for a specific date
  Future<int?> getPainLevel(DateTime date);

  /// Get symptoms only for a specific date
  Future<List<SymptomModel>?> getSymptoms(DateTime date);

  /// Check if a date has any symptom data
  Future<bool> hasSymptomData(DateTime date);

  /// Delete symptom data for a specific date
  Future<Do<PeriodTrackingFailure, Unit>> deleteSymptomData({
    required DateTime date,
  });

  /// Update existing symptom data by merging with new data
  Future<Do<PeriodTrackingFailure, Unit>> updateSymptomData({
    required DateTime date,
    List<SymptomModel>? symptoms,
    int? painLevel,
    int? flowLevel,
  });
}
