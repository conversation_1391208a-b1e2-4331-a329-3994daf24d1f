


import 'package:doso/doso.dart';

import '../../repository/menstrual_cycle_data_model.dart';
import '../failure/period_tracking_failure.dart';

abstract class MenstrualCycleFacade {
  // Get menstrual cycle stream
  Stream<Do<PeriodTrackingFailure, MenstrualCycleData>> getMenstrualCycle();

  // Get initial menstrual cycle data
  Future<Do<PeriodTrackingFailure, MenstrualCycleData>>
  getInitialMenstrualCycleData();
}