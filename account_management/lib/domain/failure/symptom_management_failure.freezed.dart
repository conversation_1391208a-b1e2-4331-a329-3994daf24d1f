// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'symptom_management_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$SymptomManagementFailure {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SymptomManagementFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomManagementFailure()';
  }
}

/// @nodoc
class $SymptomManagementFailureCopyWith<$Res> {
  $SymptomManagementFailureCopyWith(
      SymptomManagementFailure _, $Res Function(SymptomManagementFailure) __);
}

/// Adds pattern-matching-related methods to [SymptomManagementFailure].
extension SymptomManagementFailurePatterns on SymptomManagementFailure {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NetworkFailure value)? networkFailure,
    TResult Function(CacheFailure value)? cacheFailure,
    TResult Function(SyncFailure value)? syncFailure,
    TResult Function(DownloadFailure value)? downloadFailure,
    TResult Function(StorageFailure value)? storageFailure,
    TResult Function(ParseFailure value)? parseFailure,
    TResult Function(Unauthenticated value)? unauthenticated,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case NetworkFailure() when networkFailure != null:
        return networkFailure(_that);
      case CacheFailure() when cacheFailure != null:
        return cacheFailure(_that);
      case SyncFailure() when syncFailure != null:
        return syncFailure(_that);
      case DownloadFailure() when downloadFailure != null:
        return downloadFailure(_that);
      case StorageFailure() when storageFailure != null:
        return storageFailure(_that);
      case ParseFailure() when parseFailure != null:
        return parseFailure(_that);
      case Unauthenticated() when unauthenticated != null:
        return unauthenticated(_that);
      case Unexpected() when unexpected != null:
        return unexpected(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NetworkFailure value) networkFailure,
    required TResult Function(CacheFailure value) cacheFailure,
    required TResult Function(SyncFailure value) syncFailure,
    required TResult Function(DownloadFailure value) downloadFailure,
    required TResult Function(StorageFailure value) storageFailure,
    required TResult Function(ParseFailure value) parseFailure,
    required TResult Function(Unauthenticated value) unauthenticated,
    required TResult Function(Unexpected value) unexpected,
  }) {
    final _that = this;
    switch (_that) {
      case NetworkFailure():
        return networkFailure(_that);
      case CacheFailure():
        return cacheFailure(_that);
      case SyncFailure():
        return syncFailure(_that);
      case DownloadFailure():
        return downloadFailure(_that);
      case StorageFailure():
        return storageFailure(_that);
      case ParseFailure():
        return parseFailure(_that);
      case Unauthenticated():
        return unauthenticated(_that);
      case Unexpected():
        return unexpected(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NetworkFailure value)? networkFailure,
    TResult? Function(CacheFailure value)? cacheFailure,
    TResult? Function(SyncFailure value)? syncFailure,
    TResult? Function(DownloadFailure value)? downloadFailure,
    TResult? Function(StorageFailure value)? storageFailure,
    TResult? Function(ParseFailure value)? parseFailure,
    TResult? Function(Unauthenticated value)? unauthenticated,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    final _that = this;
    switch (_that) {
      case NetworkFailure() when networkFailure != null:
        return networkFailure(_that);
      case CacheFailure() when cacheFailure != null:
        return cacheFailure(_that);
      case SyncFailure() when syncFailure != null:
        return syncFailure(_that);
      case DownloadFailure() when downloadFailure != null:
        return downloadFailure(_that);
      case StorageFailure() when storageFailure != null:
        return storageFailure(_that);
      case ParseFailure() when parseFailure != null:
        return parseFailure(_that);
      case Unauthenticated() when unauthenticated != null:
        return unauthenticated(_that);
      case Unexpected() when unexpected != null:
        return unexpected(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? networkFailure,
    TResult Function()? cacheFailure,
    TResult Function()? syncFailure,
    TResult Function()? downloadFailure,
    TResult Function()? storageFailure,
    TResult Function()? parseFailure,
    TResult Function()? unauthenticated,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case NetworkFailure() when networkFailure != null:
        return networkFailure();
      case CacheFailure() when cacheFailure != null:
        return cacheFailure();
      case SyncFailure() when syncFailure != null:
        return syncFailure();
      case DownloadFailure() when downloadFailure != null:
        return downloadFailure();
      case StorageFailure() when storageFailure != null:
        return storageFailure();
      case ParseFailure() when parseFailure != null:
        return parseFailure();
      case Unauthenticated() when unauthenticated != null:
        return unauthenticated();
      case Unexpected() when unexpected != null:
        return unexpected();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() networkFailure,
    required TResult Function() cacheFailure,
    required TResult Function() syncFailure,
    required TResult Function() downloadFailure,
    required TResult Function() storageFailure,
    required TResult Function() parseFailure,
    required TResult Function() unauthenticated,
    required TResult Function() unexpected,
  }) {
    final _that = this;
    switch (_that) {
      case NetworkFailure():
        return networkFailure();
      case CacheFailure():
        return cacheFailure();
      case SyncFailure():
        return syncFailure();
      case DownloadFailure():
        return downloadFailure();
      case StorageFailure():
        return storageFailure();
      case ParseFailure():
        return parseFailure();
      case Unauthenticated():
        return unauthenticated();
      case Unexpected():
        return unexpected();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? networkFailure,
    TResult? Function()? cacheFailure,
    TResult? Function()? syncFailure,
    TResult? Function()? downloadFailure,
    TResult? Function()? storageFailure,
    TResult? Function()? parseFailure,
    TResult? Function()? unauthenticated,
    TResult? Function()? unexpected,
  }) {
    final _that = this;
    switch (_that) {
      case NetworkFailure() when networkFailure != null:
        return networkFailure();
      case CacheFailure() when cacheFailure != null:
        return cacheFailure();
      case SyncFailure() when syncFailure != null:
        return syncFailure();
      case DownloadFailure() when downloadFailure != null:
        return downloadFailure();
      case StorageFailure() when storageFailure != null:
        return storageFailure();
      case ParseFailure() when parseFailure != null:
        return parseFailure();
      case Unauthenticated() when unauthenticated != null:
        return unauthenticated();
      case Unexpected() when unexpected != null:
        return unexpected();
      case _:
        return null;
    }
  }
}

/// @nodoc

class NetworkFailure implements SymptomManagementFailure {
  const NetworkFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is NetworkFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomManagementFailure.networkFailure()';
  }
}

/// @nodoc

class CacheFailure implements SymptomManagementFailure {
  const CacheFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CacheFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomManagementFailure.cacheFailure()';
  }
}

/// @nodoc

class SyncFailure implements SymptomManagementFailure {
  const SyncFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SyncFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomManagementFailure.syncFailure()';
  }
}

/// @nodoc

class DownloadFailure implements SymptomManagementFailure {
  const DownloadFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DownloadFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomManagementFailure.downloadFailure()';
  }
}

/// @nodoc

class StorageFailure implements SymptomManagementFailure {
  const StorageFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is StorageFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomManagementFailure.storageFailure()';
  }
}

/// @nodoc

class ParseFailure implements SymptomManagementFailure {
  const ParseFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ParseFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomManagementFailure.parseFailure()';
  }
}

/// @nodoc

class Unauthenticated implements SymptomManagementFailure {
  const Unauthenticated();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Unauthenticated);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomManagementFailure.unauthenticated()';
  }
}

/// @nodoc

class Unexpected implements SymptomManagementFailure {
  const Unexpected();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Unexpected);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'SymptomManagementFailure.unexpected()';
  }
}

// dart format on
