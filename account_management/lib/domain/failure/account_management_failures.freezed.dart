// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_management_failures.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AccountManagementFailure {
  String get failure;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AccountManagementFailureCopyWith<AccountManagementFailure> get copyWith =>
      _$AccountManagementFailureCopyWithImpl<AccountManagementFailure>(
          this as AccountManagementFailure, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AccountManagementFailure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'AccountManagementFailure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class $AccountManagementFailureCopyWith<$Res> {
  factory $AccountManagementFailureCopyWith(AccountManagementFailure value,
          $Res Function(AccountManagementFailure) _then) =
      _$AccountManagementFailureCopyWithImpl;
  @useResult
  $Res call({String failure});
}

/// @nodoc
class _$AccountManagementFailureCopyWithImpl<$Res>
    implements $AccountManagementFailureCopyWith<$Res> {
  _$AccountManagementFailureCopyWithImpl(this._self, this._then);

  final AccountManagementFailure _self;
  final $Res Function(AccountManagementFailure) _then;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? failure = null,
  }) {
    return _then(_self.copyWith(
      failure: null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [AccountManagementFailure].
extension AccountManagementFailurePatterns on AccountManagementFailure {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case AccountDetailsLoadFailure() when accountDetailsLoadFailure != null:
        return accountDetailsLoadFailure(_that);
      case AccountDetailsUpdateFailure()
          when accountDetailsUpdateFailure != null:
        return accountDetailsUpdateFailure(_that);
      case AccountPasswordUpdateFailure()
          when accountPasswordUpdateFailure != null:
        return accountPasswordUpdateFailure(_that);
      case AccountEmailUpdateFailure() when accountEmailUpdateFailure != null:
        return accountEmailUpdateFailure(_that);
      case AccountPhoneNumberUpdateFailure()
          when accountPhoneNumberUpdateFailure != null:
        return accountPhoneNumberUpdateFailure(_that);
      case OnboardingDataAddFailure() when onboardingDataAddFailure != null:
        return onboardingDataAddFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(AccountDetailsLoadFailure value)
        accountDetailsLoadFailure,
    required TResult Function(AccountDetailsUpdateFailure value)
        accountDetailsUpdateFailure,
    required TResult Function(AccountPasswordUpdateFailure value)
        accountPasswordUpdateFailure,
    required TResult Function(AccountEmailUpdateFailure value)
        accountEmailUpdateFailure,
    required TResult Function(AccountPhoneNumberUpdateFailure value)
        accountPhoneNumberUpdateFailure,
    required TResult Function(OnboardingDataAddFailure value)
        onboardingDataAddFailure,
  }) {
    final _that = this;
    switch (_that) {
      case AccountDetailsLoadFailure():
        return accountDetailsLoadFailure(_that);
      case AccountDetailsUpdateFailure():
        return accountDetailsUpdateFailure(_that);
      case AccountPasswordUpdateFailure():
        return accountPasswordUpdateFailure(_that);
      case AccountEmailUpdateFailure():
        return accountEmailUpdateFailure(_that);
      case AccountPhoneNumberUpdateFailure():
        return accountPhoneNumberUpdateFailure(_that);
      case OnboardingDataAddFailure():
        return onboardingDataAddFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(AccountDetailsLoadFailure value)?
        accountDetailsLoadFailure,
    TResult? Function(AccountDetailsUpdateFailure value)?
        accountDetailsUpdateFailure,
    TResult? Function(AccountPasswordUpdateFailure value)?
        accountPasswordUpdateFailure,
    TResult? Function(AccountEmailUpdateFailure value)?
        accountEmailUpdateFailure,
    TResult? Function(AccountPhoneNumberUpdateFailure value)?
        accountPhoneNumberUpdateFailure,
    TResult? Function(OnboardingDataAddFailure value)? onboardingDataAddFailure,
  }) {
    final _that = this;
    switch (_that) {
      case AccountDetailsLoadFailure() when accountDetailsLoadFailure != null:
        return accountDetailsLoadFailure(_that);
      case AccountDetailsUpdateFailure()
          when accountDetailsUpdateFailure != null:
        return accountDetailsUpdateFailure(_that);
      case AccountPasswordUpdateFailure()
          when accountPasswordUpdateFailure != null:
        return accountPasswordUpdateFailure(_that);
      case AccountEmailUpdateFailure() when accountEmailUpdateFailure != null:
        return accountEmailUpdateFailure(_that);
      case AccountPhoneNumberUpdateFailure()
          when accountPhoneNumberUpdateFailure != null:
        return accountPhoneNumberUpdateFailure(_that);
      case OnboardingDataAddFailure() when onboardingDataAddFailure != null:
        return onboardingDataAddFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String failure)? accountDetailsLoadFailure,
    TResult Function(String failure)? accountDetailsUpdateFailure,
    TResult Function(String failure)? accountPasswordUpdateFailure,
    TResult Function(String failure)? accountEmailUpdateFailure,
    TResult Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult Function(String failure)? onboardingDataAddFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case AccountDetailsLoadFailure() when accountDetailsLoadFailure != null:
        return accountDetailsLoadFailure(_that.failure);
      case AccountDetailsUpdateFailure()
          when accountDetailsUpdateFailure != null:
        return accountDetailsUpdateFailure(_that.failure);
      case AccountPasswordUpdateFailure()
          when accountPasswordUpdateFailure != null:
        return accountPasswordUpdateFailure(_that.failure);
      case AccountEmailUpdateFailure() when accountEmailUpdateFailure != null:
        return accountEmailUpdateFailure(_that.failure);
      case AccountPhoneNumberUpdateFailure()
          when accountPhoneNumberUpdateFailure != null:
        return accountPhoneNumberUpdateFailure(_that.failure);
      case OnboardingDataAddFailure() when onboardingDataAddFailure != null:
        return onboardingDataAddFailure(_that.failure);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String failure) accountDetailsLoadFailure,
    required TResult Function(String failure) accountDetailsUpdateFailure,
    required TResult Function(String failure) accountPasswordUpdateFailure,
    required TResult Function(String failure) accountEmailUpdateFailure,
    required TResult Function(String failure) accountPhoneNumberUpdateFailure,
    required TResult Function(String failure) onboardingDataAddFailure,
  }) {
    final _that = this;
    switch (_that) {
      case AccountDetailsLoadFailure():
        return accountDetailsLoadFailure(_that.failure);
      case AccountDetailsUpdateFailure():
        return accountDetailsUpdateFailure(_that.failure);
      case AccountPasswordUpdateFailure():
        return accountPasswordUpdateFailure(_that.failure);
      case AccountEmailUpdateFailure():
        return accountEmailUpdateFailure(_that.failure);
      case AccountPhoneNumberUpdateFailure():
        return accountPhoneNumberUpdateFailure(_that.failure);
      case OnboardingDataAddFailure():
        return onboardingDataAddFailure(_that.failure);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String failure)? accountDetailsLoadFailure,
    TResult? Function(String failure)? accountDetailsUpdateFailure,
    TResult? Function(String failure)? accountPasswordUpdateFailure,
    TResult? Function(String failure)? accountEmailUpdateFailure,
    TResult? Function(String failure)? accountPhoneNumberUpdateFailure,
    TResult? Function(String failure)? onboardingDataAddFailure,
  }) {
    final _that = this;
    switch (_that) {
      case AccountDetailsLoadFailure() when accountDetailsLoadFailure != null:
        return accountDetailsLoadFailure(_that.failure);
      case AccountDetailsUpdateFailure()
          when accountDetailsUpdateFailure != null:
        return accountDetailsUpdateFailure(_that.failure);
      case AccountPasswordUpdateFailure()
          when accountPasswordUpdateFailure != null:
        return accountPasswordUpdateFailure(_that.failure);
      case AccountEmailUpdateFailure() when accountEmailUpdateFailure != null:
        return accountEmailUpdateFailure(_that.failure);
      case AccountPhoneNumberUpdateFailure()
          when accountPhoneNumberUpdateFailure != null:
        return accountPhoneNumberUpdateFailure(_that.failure);
      case OnboardingDataAddFailure() when onboardingDataAddFailure != null:
        return onboardingDataAddFailure(_that.failure);
      case _:
        return null;
    }
  }
}

/// @nodoc

class AccountDetailsLoadFailure implements AccountManagementFailure {
  const AccountDetailsLoadFailure(this.failure);

  @override
  final String failure;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AccountDetailsLoadFailureCopyWith<AccountDetailsLoadFailure> get copyWith =>
      _$AccountDetailsLoadFailureCopyWithImpl<AccountDetailsLoadFailure>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AccountDetailsLoadFailure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'AccountManagementFailure.accountDetailsLoadFailure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class $AccountDetailsLoadFailureCopyWith<$Res>
    implements $AccountManagementFailureCopyWith<$Res> {
  factory $AccountDetailsLoadFailureCopyWith(AccountDetailsLoadFailure value,
          $Res Function(AccountDetailsLoadFailure) _then) =
      _$AccountDetailsLoadFailureCopyWithImpl;
  @override
  @useResult
  $Res call({String failure});
}

/// @nodoc
class _$AccountDetailsLoadFailureCopyWithImpl<$Res>
    implements $AccountDetailsLoadFailureCopyWith<$Res> {
  _$AccountDetailsLoadFailureCopyWithImpl(this._self, this._then);

  final AccountDetailsLoadFailure _self;
  final $Res Function(AccountDetailsLoadFailure) _then;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(AccountDetailsLoadFailure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class AccountDetailsUpdateFailure implements AccountManagementFailure {
  const AccountDetailsUpdateFailure(this.failure);

  @override
  final String failure;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AccountDetailsUpdateFailureCopyWith<AccountDetailsUpdateFailure>
      get copyWith => _$AccountDetailsUpdateFailureCopyWithImpl<
          AccountDetailsUpdateFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AccountDetailsUpdateFailure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'AccountManagementFailure.accountDetailsUpdateFailure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class $AccountDetailsUpdateFailureCopyWith<$Res>
    implements $AccountManagementFailureCopyWith<$Res> {
  factory $AccountDetailsUpdateFailureCopyWith(
          AccountDetailsUpdateFailure value,
          $Res Function(AccountDetailsUpdateFailure) _then) =
      _$AccountDetailsUpdateFailureCopyWithImpl;
  @override
  @useResult
  $Res call({String failure});
}

/// @nodoc
class _$AccountDetailsUpdateFailureCopyWithImpl<$Res>
    implements $AccountDetailsUpdateFailureCopyWith<$Res> {
  _$AccountDetailsUpdateFailureCopyWithImpl(this._self, this._then);

  final AccountDetailsUpdateFailure _self;
  final $Res Function(AccountDetailsUpdateFailure) _then;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(AccountDetailsUpdateFailure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class AccountPasswordUpdateFailure implements AccountManagementFailure {
  const AccountPasswordUpdateFailure(this.failure);

  @override
  final String failure;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AccountPasswordUpdateFailureCopyWith<AccountPasswordUpdateFailure>
      get copyWith => _$AccountPasswordUpdateFailureCopyWithImpl<
          AccountPasswordUpdateFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AccountPasswordUpdateFailure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'AccountManagementFailure.accountPasswordUpdateFailure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class $AccountPasswordUpdateFailureCopyWith<$Res>
    implements $AccountManagementFailureCopyWith<$Res> {
  factory $AccountPasswordUpdateFailureCopyWith(
          AccountPasswordUpdateFailure value,
          $Res Function(AccountPasswordUpdateFailure) _then) =
      _$AccountPasswordUpdateFailureCopyWithImpl;
  @override
  @useResult
  $Res call({String failure});
}

/// @nodoc
class _$AccountPasswordUpdateFailureCopyWithImpl<$Res>
    implements $AccountPasswordUpdateFailureCopyWith<$Res> {
  _$AccountPasswordUpdateFailureCopyWithImpl(this._self, this._then);

  final AccountPasswordUpdateFailure _self;
  final $Res Function(AccountPasswordUpdateFailure) _then;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(AccountPasswordUpdateFailure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class AccountEmailUpdateFailure implements AccountManagementFailure {
  const AccountEmailUpdateFailure(this.failure);

  @override
  final String failure;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AccountEmailUpdateFailureCopyWith<AccountEmailUpdateFailure> get copyWith =>
      _$AccountEmailUpdateFailureCopyWithImpl<AccountEmailUpdateFailure>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AccountEmailUpdateFailure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'AccountManagementFailure.accountEmailUpdateFailure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class $AccountEmailUpdateFailureCopyWith<$Res>
    implements $AccountManagementFailureCopyWith<$Res> {
  factory $AccountEmailUpdateFailureCopyWith(AccountEmailUpdateFailure value,
          $Res Function(AccountEmailUpdateFailure) _then) =
      _$AccountEmailUpdateFailureCopyWithImpl;
  @override
  @useResult
  $Res call({String failure});
}

/// @nodoc
class _$AccountEmailUpdateFailureCopyWithImpl<$Res>
    implements $AccountEmailUpdateFailureCopyWith<$Res> {
  _$AccountEmailUpdateFailureCopyWithImpl(this._self, this._then);

  final AccountEmailUpdateFailure _self;
  final $Res Function(AccountEmailUpdateFailure) _then;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(AccountEmailUpdateFailure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class AccountPhoneNumberUpdateFailure implements AccountManagementFailure {
  const AccountPhoneNumberUpdateFailure(this.failure);

  @override
  final String failure;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AccountPhoneNumberUpdateFailureCopyWith<AccountPhoneNumberUpdateFailure>
      get copyWith => _$AccountPhoneNumberUpdateFailureCopyWithImpl<
          AccountPhoneNumberUpdateFailure>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AccountPhoneNumberUpdateFailure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'AccountManagementFailure.accountPhoneNumberUpdateFailure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class $AccountPhoneNumberUpdateFailureCopyWith<$Res>
    implements $AccountManagementFailureCopyWith<$Res> {
  factory $AccountPhoneNumberUpdateFailureCopyWith(
          AccountPhoneNumberUpdateFailure value,
          $Res Function(AccountPhoneNumberUpdateFailure) _then) =
      _$AccountPhoneNumberUpdateFailureCopyWithImpl;
  @override
  @useResult
  $Res call({String failure});
}

/// @nodoc
class _$AccountPhoneNumberUpdateFailureCopyWithImpl<$Res>
    implements $AccountPhoneNumberUpdateFailureCopyWith<$Res> {
  _$AccountPhoneNumberUpdateFailureCopyWithImpl(this._self, this._then);

  final AccountPhoneNumberUpdateFailure _self;
  final $Res Function(AccountPhoneNumberUpdateFailure) _then;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(AccountPhoneNumberUpdateFailure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class OnboardingDataAddFailure implements AccountManagementFailure {
  const OnboardingDataAddFailure(this.failure);

  @override
  final String failure;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OnboardingDataAddFailureCopyWith<OnboardingDataAddFailure> get copyWith =>
      _$OnboardingDataAddFailureCopyWithImpl<OnboardingDataAddFailure>(
          this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OnboardingDataAddFailure &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'AccountManagementFailure.onboardingDataAddFailure(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class $OnboardingDataAddFailureCopyWith<$Res>
    implements $AccountManagementFailureCopyWith<$Res> {
  factory $OnboardingDataAddFailureCopyWith(OnboardingDataAddFailure value,
          $Res Function(OnboardingDataAddFailure) _then) =
      _$OnboardingDataAddFailureCopyWithImpl;
  @override
  @useResult
  $Res call({String failure});
}

/// @nodoc
class _$OnboardingDataAddFailureCopyWithImpl<$Res>
    implements $OnboardingDataAddFailureCopyWith<$Res> {
  _$OnboardingDataAddFailureCopyWithImpl(this._self, this._then);

  final OnboardingDataAddFailure _self;
  final $Res Function(OnboardingDataAddFailure) _then;

  /// Create a copy of AccountManagementFailure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(OnboardingDataAddFailure(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
