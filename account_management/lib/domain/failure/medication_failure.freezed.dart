// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'medication_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MedicationFailure {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is MedicationFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MedicationFailure()';
  }
}

/// @nodoc
class $MedicationFailureCopyWith<$Res> {
  $MedicationFailureCopyWith(
      MedicationFailure _, $Res Function(MedicationFailure) __);
}

/// Adds pattern-matching-related methods to [MedicationFailure].
extension MedicationFailurePatterns on MedicationFailure {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetMedicationsFailure value)? getMedicationsFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case UpdateFailure() when updateFailure != null:
        return updateFailure(_that);
      case DeleteFailure() when deleteFailure != null:
        return deleteFailure(_that);
      case CreateFailure() when createFailure != null:
        return createFailure(_that);
      case Unexpected() when unexpected != null:
        return unexpected(_that);
      case GetMedicationsFailure() when getMedicationsFailure != null:
        return getMedicationsFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetMedicationsFailure value)
        getMedicationsFailure,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateFailure():
        return updateFailure(_that);
      case DeleteFailure():
        return deleteFailure(_that);
      case CreateFailure():
        return createFailure(_that);
      case Unexpected():
        return unexpected(_that);
      case GetMedicationsFailure():
        return getMedicationsFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetMedicationsFailure value)? getMedicationsFailure,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateFailure() when updateFailure != null:
        return updateFailure(_that);
      case DeleteFailure() when deleteFailure != null:
        return deleteFailure(_that);
      case CreateFailure() when createFailure != null:
        return createFailure(_that);
      case Unexpected() when unexpected != null:
        return unexpected(_that);
      case GetMedicationsFailure() when getMedicationsFailure != null:
        return getMedicationsFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getMedicationsFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case UpdateFailure() when updateFailure != null:
        return updateFailure();
      case DeleteFailure() when deleteFailure != null:
        return deleteFailure();
      case CreateFailure() when createFailure != null:
        return createFailure();
      case Unexpected() when unexpected != null:
        return unexpected();
      case GetMedicationsFailure() when getMedicationsFailure != null:
        return getMedicationsFailure();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getMedicationsFailure,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateFailure():
        return updateFailure();
      case DeleteFailure():
        return deleteFailure();
      case CreateFailure():
        return createFailure();
      case Unexpected():
        return unexpected();
      case GetMedicationsFailure():
        return getMedicationsFailure();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getMedicationsFailure,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateFailure() when updateFailure != null:
        return updateFailure();
      case DeleteFailure() when deleteFailure != null:
        return deleteFailure();
      case CreateFailure() when createFailure != null:
        return createFailure();
      case Unexpected() when unexpected != null:
        return unexpected();
      case GetMedicationsFailure() when getMedicationsFailure != null:
        return getMedicationsFailure();
      case _:
        return null;
    }
  }
}

/// @nodoc

class UpdateFailure implements MedicationFailure {
  const UpdateFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is UpdateFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MedicationFailure.updateFailure()';
  }
}

/// @nodoc

class DeleteFailure implements MedicationFailure {
  const DeleteFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeleteFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MedicationFailure.deleteFailure()';
  }
}

/// @nodoc

class CreateFailure implements MedicationFailure {
  const CreateFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CreateFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MedicationFailure.createFailure()';
  }
}

/// @nodoc

class Unexpected implements MedicationFailure {
  const Unexpected();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Unexpected);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MedicationFailure.unexpected()';
  }
}

/// @nodoc

class GetMedicationsFailure implements MedicationFailure {
  const GetMedicationsFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is GetMedicationsFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'MedicationFailure.getMedicationsFailure()';
  }
}

// dart format on
