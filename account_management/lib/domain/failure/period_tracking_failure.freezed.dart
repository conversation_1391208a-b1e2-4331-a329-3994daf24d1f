// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'period_tracking_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PeriodTrackingFailure {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is PeriodTrackingFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodTrackingFailure()';
  }
}

/// @nodoc
class $PeriodTrackingFailureCopyWith<$Res> {
  $PeriodTrackingFailureCopyWith(
      PeriodTrackingFailure _, $Res Function(PeriodTrackingFailure) __);
}

/// Adds pattern-matching-related methods to [PeriodTrackingFailure].
extension PeriodTrackingFailurePatterns on PeriodTrackingFailure {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult Function(NotFound value)? notFound,
    TResult Function(InvalidData value)? invalidData,
    TResult Function(Unauthenticated value)? unauthenticated,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case UpdateFailure() when updateFailure != null:
        return updateFailure(_that);
      case DeleteFailure() when deleteFailure != null:
        return deleteFailure(_that);
      case CreateFailure() when createFailure != null:
        return createFailure(_that);
      case Unexpected() when unexpected != null:
        return unexpected(_that);
      case GetPeriodTrackingsFailure() when getPeriodTrackingsFailure != null:
        return getPeriodTrackingsFailure(_that);
      case NotFound() when notFound != null:
        return notFound(_that);
      case InvalidData() when invalidData != null:
        return invalidData(_that);
      case Unauthenticated() when unauthenticated != null:
        return unauthenticated(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetPeriodTrackingsFailure value)
        getPeriodTrackingsFailure,
    required TResult Function(NotFound value) notFound,
    required TResult Function(InvalidData value) invalidData,
    required TResult Function(Unauthenticated value) unauthenticated,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateFailure():
        return updateFailure(_that);
      case DeleteFailure():
        return deleteFailure(_that);
      case CreateFailure():
        return createFailure(_that);
      case Unexpected():
        return unexpected(_that);
      case GetPeriodTrackingsFailure():
        return getPeriodTrackingsFailure(_that);
      case NotFound():
        return notFound(_that);
      case InvalidData():
        return invalidData(_that);
      case Unauthenticated():
        return unauthenticated(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetPeriodTrackingsFailure value)?
        getPeriodTrackingsFailure,
    TResult? Function(NotFound value)? notFound,
    TResult? Function(InvalidData value)? invalidData,
    TResult? Function(Unauthenticated value)? unauthenticated,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateFailure() when updateFailure != null:
        return updateFailure(_that);
      case DeleteFailure() when deleteFailure != null:
        return deleteFailure(_that);
      case CreateFailure() when createFailure != null:
        return createFailure(_that);
      case Unexpected() when unexpected != null:
        return unexpected(_that);
      case GetPeriodTrackingsFailure() when getPeriodTrackingsFailure != null:
        return getPeriodTrackingsFailure(_that);
      case NotFound() when notFound != null:
        return notFound(_that);
      case InvalidData() when invalidData != null:
        return invalidData(_that);
      case Unauthenticated() when unauthenticated != null:
        return unauthenticated(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getPeriodTrackingsFailure,
    TResult Function()? notFound,
    TResult Function()? invalidData,
    TResult Function()? unauthenticated,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case UpdateFailure() when updateFailure != null:
        return updateFailure();
      case DeleteFailure() when deleteFailure != null:
        return deleteFailure();
      case CreateFailure() when createFailure != null:
        return createFailure();
      case Unexpected() when unexpected != null:
        return unexpected();
      case GetPeriodTrackingsFailure() when getPeriodTrackingsFailure != null:
        return getPeriodTrackingsFailure();
      case NotFound() when notFound != null:
        return notFound();
      case InvalidData() when invalidData != null:
        return invalidData();
      case Unauthenticated() when unauthenticated != null:
        return unauthenticated();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getPeriodTrackingsFailure,
    required TResult Function() notFound,
    required TResult Function() invalidData,
    required TResult Function() unauthenticated,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateFailure():
        return updateFailure();
      case DeleteFailure():
        return deleteFailure();
      case CreateFailure():
        return createFailure();
      case Unexpected():
        return unexpected();
      case GetPeriodTrackingsFailure():
        return getPeriodTrackingsFailure();
      case NotFound():
        return notFound();
      case InvalidData():
        return invalidData();
      case Unauthenticated():
        return unauthenticated();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getPeriodTrackingsFailure,
    TResult? Function()? notFound,
    TResult? Function()? invalidData,
    TResult? Function()? unauthenticated,
  }) {
    final _that = this;
    switch (_that) {
      case UpdateFailure() when updateFailure != null:
        return updateFailure();
      case DeleteFailure() when deleteFailure != null:
        return deleteFailure();
      case CreateFailure() when createFailure != null:
        return createFailure();
      case Unexpected() when unexpected != null:
        return unexpected();
      case GetPeriodTrackingsFailure() when getPeriodTrackingsFailure != null:
        return getPeriodTrackingsFailure();
      case NotFound() when notFound != null:
        return notFound();
      case InvalidData() when invalidData != null:
        return invalidData();
      case Unauthenticated() when unauthenticated != null:
        return unauthenticated();
      case _:
        return null;
    }
  }
}

/// @nodoc

class UpdateFailure implements PeriodTrackingFailure {
  const UpdateFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is UpdateFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodTrackingFailure.updateFailure()';
  }
}

/// @nodoc

class DeleteFailure implements PeriodTrackingFailure {
  const DeleteFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeleteFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodTrackingFailure.deleteFailure()';
  }
}

/// @nodoc

class CreateFailure implements PeriodTrackingFailure {
  const CreateFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CreateFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodTrackingFailure.createFailure()';
  }
}

/// @nodoc

class Unexpected implements PeriodTrackingFailure {
  const Unexpected();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Unexpected);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodTrackingFailure.unexpected()';
  }
}

/// @nodoc

class GetPeriodTrackingsFailure implements PeriodTrackingFailure {
  const GetPeriodTrackingsFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GetPeriodTrackingsFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodTrackingFailure.getPeriodTrackingsFailure()';
  }
}

/// @nodoc

class NotFound implements PeriodTrackingFailure {
  const NotFound();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is NotFound);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodTrackingFailure.notFound()';
  }
}

/// @nodoc

class InvalidData implements PeriodTrackingFailure {
  const InvalidData();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is InvalidData);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodTrackingFailure.invalidData()';
  }
}

/// @nodoc

class Unauthenticated implements PeriodTrackingFailure {
  const Unauthenticated();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Unauthenticated);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PeriodTrackingFailure.unauthenticated()';
  }
}

// dart format on
