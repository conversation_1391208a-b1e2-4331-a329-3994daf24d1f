import 'package:freezed_annotation/freezed_annotation.dart';
part 'ovulation_failure.freezed.dart';

@freezed
class OvulationFailure with _$OvulationFailure {
  const factory OvulationFailure.calculationFailure() = CalculationFailure;
  const factory OvulationFailure.updateFailure() = UpdateFailure;
  const factory OvulationFailure.deleteFailure() = DeleteFailure;
  const factory OvulationFailure.createFailure() = CreateFailure;
  const factory OvulationFailure.unexpected() = Unexpected;
  const factory OvulationFailure.getOvulationDatesFailure() = GetOvulationDatesFailure;
  const factory OvulationFailure.notFound() = NotFound;
  const factory OvulationFailure.invalidData() = InvalidData;
  const factory OvulationFailure.unauthenticated() = Unauthenticated;
  const factory OvulationFailure.insufficientPeriodData() = InsufficientPeriodData;
  const factory OvulationFailure.invalidCycleData() = InvalidCycleData;
  const factory OvulationFailure.healthDataUnavailable() = HealthDataUnavailable;
}
