// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ovulation_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OvulationFailure {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OvulationFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OvulationFailure()';
  }
}

/// @nodoc
class $OvulationFailureCopyWith<$Res> {
  $OvulationFailureCopyWith(
      OvulationFailure _, $Res Function(OvulationFailure) __);
}

/// Adds pattern-matching-related methods to [OvulationFailure].
extension OvulationFailurePatterns on OvulationFailure {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(CalculationFailure value)? calculationFailure,
    TResult Function(UpdateFailure value)? updateFailure,
    TResult Function(DeleteFailure value)? deleteFailure,
    TResult Function(CreateFailure value)? createFailure,
    TResult Function(Unexpected value)? unexpected,
    TResult Function(GetOvulationDatesFailure value)? getOvulationDatesFailure,
    TResult Function(NotFound value)? notFound,
    TResult Function(InvalidData value)? invalidData,
    TResult Function(Unauthenticated value)? unauthenticated,
    TResult Function(InsufficientPeriodData value)? insufficientPeriodData,
    TResult Function(InvalidCycleData value)? invalidCycleData,
    TResult Function(HealthDataUnavailable value)? healthDataUnavailable,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case CalculationFailure() when calculationFailure != null:
        return calculationFailure(_that);
      case UpdateFailure() when updateFailure != null:
        return updateFailure(_that);
      case DeleteFailure() when deleteFailure != null:
        return deleteFailure(_that);
      case CreateFailure() when createFailure != null:
        return createFailure(_that);
      case Unexpected() when unexpected != null:
        return unexpected(_that);
      case GetOvulationDatesFailure() when getOvulationDatesFailure != null:
        return getOvulationDatesFailure(_that);
      case NotFound() when notFound != null:
        return notFound(_that);
      case InvalidData() when invalidData != null:
        return invalidData(_that);
      case Unauthenticated() when unauthenticated != null:
        return unauthenticated(_that);
      case InsufficientPeriodData() when insufficientPeriodData != null:
        return insufficientPeriodData(_that);
      case InvalidCycleData() when invalidCycleData != null:
        return invalidCycleData(_that);
      case HealthDataUnavailable() when healthDataUnavailable != null:
        return healthDataUnavailable(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(CalculationFailure value) calculationFailure,
    required TResult Function(UpdateFailure value) updateFailure,
    required TResult Function(DeleteFailure value) deleteFailure,
    required TResult Function(CreateFailure value) createFailure,
    required TResult Function(Unexpected value) unexpected,
    required TResult Function(GetOvulationDatesFailure value)
        getOvulationDatesFailure,
    required TResult Function(NotFound value) notFound,
    required TResult Function(InvalidData value) invalidData,
    required TResult Function(Unauthenticated value) unauthenticated,
    required TResult Function(InsufficientPeriodData value)
        insufficientPeriodData,
    required TResult Function(InvalidCycleData value) invalidCycleData,
    required TResult Function(HealthDataUnavailable value)
        healthDataUnavailable,
  }) {
    final _that = this;
    switch (_that) {
      case CalculationFailure():
        return calculationFailure(_that);
      case UpdateFailure():
        return updateFailure(_that);
      case DeleteFailure():
        return deleteFailure(_that);
      case CreateFailure():
        return createFailure(_that);
      case Unexpected():
        return unexpected(_that);
      case GetOvulationDatesFailure():
        return getOvulationDatesFailure(_that);
      case NotFound():
        return notFound(_that);
      case InvalidData():
        return invalidData(_that);
      case Unauthenticated():
        return unauthenticated(_that);
      case InsufficientPeriodData():
        return insufficientPeriodData(_that);
      case InvalidCycleData():
        return invalidCycleData(_that);
      case HealthDataUnavailable():
        return healthDataUnavailable(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(CalculationFailure value)? calculationFailure,
    TResult? Function(UpdateFailure value)? updateFailure,
    TResult? Function(DeleteFailure value)? deleteFailure,
    TResult? Function(CreateFailure value)? createFailure,
    TResult? Function(Unexpected value)? unexpected,
    TResult? Function(GetOvulationDatesFailure value)? getOvulationDatesFailure,
    TResult? Function(NotFound value)? notFound,
    TResult? Function(InvalidData value)? invalidData,
    TResult? Function(Unauthenticated value)? unauthenticated,
    TResult? Function(InsufficientPeriodData value)? insufficientPeriodData,
    TResult? Function(InvalidCycleData value)? invalidCycleData,
    TResult? Function(HealthDataUnavailable value)? healthDataUnavailable,
  }) {
    final _that = this;
    switch (_that) {
      case CalculationFailure() when calculationFailure != null:
        return calculationFailure(_that);
      case UpdateFailure() when updateFailure != null:
        return updateFailure(_that);
      case DeleteFailure() when deleteFailure != null:
        return deleteFailure(_that);
      case CreateFailure() when createFailure != null:
        return createFailure(_that);
      case Unexpected() when unexpected != null:
        return unexpected(_that);
      case GetOvulationDatesFailure() when getOvulationDatesFailure != null:
        return getOvulationDatesFailure(_that);
      case NotFound() when notFound != null:
        return notFound(_that);
      case InvalidData() when invalidData != null:
        return invalidData(_that);
      case Unauthenticated() when unauthenticated != null:
        return unauthenticated(_that);
      case InsufficientPeriodData() when insufficientPeriodData != null:
        return insufficientPeriodData(_that);
      case InvalidCycleData() when invalidCycleData != null:
        return invalidCycleData(_that);
      case HealthDataUnavailable() when healthDataUnavailable != null:
        return healthDataUnavailable(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? calculationFailure,
    TResult Function()? updateFailure,
    TResult Function()? deleteFailure,
    TResult Function()? createFailure,
    TResult Function()? unexpected,
    TResult Function()? getOvulationDatesFailure,
    TResult Function()? notFound,
    TResult Function()? invalidData,
    TResult Function()? unauthenticated,
    TResult Function()? insufficientPeriodData,
    TResult Function()? invalidCycleData,
    TResult Function()? healthDataUnavailable,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case CalculationFailure() when calculationFailure != null:
        return calculationFailure();
      case UpdateFailure() when updateFailure != null:
        return updateFailure();
      case DeleteFailure() when deleteFailure != null:
        return deleteFailure();
      case CreateFailure() when createFailure != null:
        return createFailure();
      case Unexpected() when unexpected != null:
        return unexpected();
      case GetOvulationDatesFailure() when getOvulationDatesFailure != null:
        return getOvulationDatesFailure();
      case NotFound() when notFound != null:
        return notFound();
      case InvalidData() when invalidData != null:
        return invalidData();
      case Unauthenticated() when unauthenticated != null:
        return unauthenticated();
      case InsufficientPeriodData() when insufficientPeriodData != null:
        return insufficientPeriodData();
      case InvalidCycleData() when invalidCycleData != null:
        return invalidCycleData();
      case HealthDataUnavailable() when healthDataUnavailable != null:
        return healthDataUnavailable();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() calculationFailure,
    required TResult Function() updateFailure,
    required TResult Function() deleteFailure,
    required TResult Function() createFailure,
    required TResult Function() unexpected,
    required TResult Function() getOvulationDatesFailure,
    required TResult Function() notFound,
    required TResult Function() invalidData,
    required TResult Function() unauthenticated,
    required TResult Function() insufficientPeriodData,
    required TResult Function() invalidCycleData,
    required TResult Function() healthDataUnavailable,
  }) {
    final _that = this;
    switch (_that) {
      case CalculationFailure():
        return calculationFailure();
      case UpdateFailure():
        return updateFailure();
      case DeleteFailure():
        return deleteFailure();
      case CreateFailure():
        return createFailure();
      case Unexpected():
        return unexpected();
      case GetOvulationDatesFailure():
        return getOvulationDatesFailure();
      case NotFound():
        return notFound();
      case InvalidData():
        return invalidData();
      case Unauthenticated():
        return unauthenticated();
      case InsufficientPeriodData():
        return insufficientPeriodData();
      case InvalidCycleData():
        return invalidCycleData();
      case HealthDataUnavailable():
        return healthDataUnavailable();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? calculationFailure,
    TResult? Function()? updateFailure,
    TResult? Function()? deleteFailure,
    TResult? Function()? createFailure,
    TResult? Function()? unexpected,
    TResult? Function()? getOvulationDatesFailure,
    TResult? Function()? notFound,
    TResult? Function()? invalidData,
    TResult? Function()? unauthenticated,
    TResult? Function()? insufficientPeriodData,
    TResult? Function()? invalidCycleData,
    TResult? Function()? healthDataUnavailable,
  }) {
    final _that = this;
    switch (_that) {
      case CalculationFailure() when calculationFailure != null:
        return calculationFailure();
      case UpdateFailure() when updateFailure != null:
        return updateFailure();
      case DeleteFailure() when deleteFailure != null:
        return deleteFailure();
      case CreateFailure() when createFailure != null:
        return createFailure();
      case Unexpected() when unexpected != null:
        return unexpected();
      case GetOvulationDatesFailure() when getOvulationDatesFailure != null:
        return getOvulationDatesFailure();
      case NotFound() when notFound != null:
        return notFound();
      case InvalidData() when invalidData != null:
        return invalidData();
      case Unauthenticated() when unauthenticated != null:
        return unauthenticated();
      case InsufficientPeriodData() when insufficientPeriodData != null:
        return insufficientPeriodData();
      case InvalidCycleData() when invalidCycleData != null:
        return invalidCycleData();
      case HealthDataUnavailable() when healthDataUnavailable != null:
        return healthDataUnavailable();
      case _:
        return null;
    }
  }
}

/// @nodoc

class CalculationFailure implements OvulationFailure {
  const CalculationFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CalculationFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OvulationFailure.calculationFailure()';
  }
}

/// @nodoc

class UpdateFailure implements OvulationFailure {
  const UpdateFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is UpdateFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OvulationFailure.updateFailure()';
  }
}

/// @nodoc

class DeleteFailure implements OvulationFailure {
  const DeleteFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeleteFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OvulationFailure.deleteFailure()';
  }
}

/// @nodoc

class CreateFailure implements OvulationFailure {
  const CreateFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CreateFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OvulationFailure.createFailure()';
  }
}

/// @nodoc

class Unexpected implements OvulationFailure {
  const Unexpected();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Unexpected);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OvulationFailure.unexpected()';
  }
}

/// @nodoc

class GetOvulationDatesFailure implements OvulationFailure {
  const GetOvulationDatesFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is GetOvulationDatesFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OvulationFailure.getOvulationDatesFailure()';
  }
}

/// @nodoc

class NotFound implements OvulationFailure {
  const NotFound();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is NotFound);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OvulationFailure.notFound()';
  }
}

/// @nodoc

class InvalidData implements OvulationFailure {
  const InvalidData();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is InvalidData);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OvulationFailure.invalidData()';
  }
}

/// @nodoc

class Unauthenticated implements OvulationFailure {
  const Unauthenticated();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Unauthenticated);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OvulationFailure.unauthenticated()';
  }
}

/// @nodoc

class InsufficientPeriodData implements OvulationFailure {
  const InsufficientPeriodData();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is InsufficientPeriodData);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OvulationFailure.insufficientPeriodData()';
  }
}

/// @nodoc

class InvalidCycleData implements OvulationFailure {
  const InvalidCycleData();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is InvalidCycleData);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OvulationFailure.invalidCycleData()';
  }
}

/// @nodoc

class HealthDataUnavailable implements OvulationFailure {
  const HealthDataUnavailable();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is HealthDataUnavailable);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OvulationFailure.healthDataUnavailable()';
  }
}

// dart format on
