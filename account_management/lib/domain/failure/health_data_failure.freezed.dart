// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'health_data_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$HealthDataFailure {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is HealthDataFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'HealthDataFailure()';
  }
}

/// @nodoc
class $HealthDataFailureCopyWith<$Res> {
  $HealthDataFailureCopyWith(
      HealthDataFailure _, $Res Function(HealthDataFailure) __);
}

/// Adds pattern-matching-related methods to [HealthDataFailure].
extension HealthDataFailurePatterns on HealthDataFailure {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult Function(Unexpected value)? unexpected,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case PeriodLengthFailure() when periodLengthFailure != null:
        return periodLengthFailure(_that);
      case CycleLengthFailure() when cycleLengthFailure != null:
        return cycleLengthFailure(_that);
      case OvulationDateFailure() when ovulationDateFailure != null:
        return ovulationDateFailure(_that);
      case ContraceptionTypeFailure() when contraceptionTypeFailure != null:
        return contraceptionTypeFailure(_that);
      case Unexpected() when unexpected != null:
        return unexpected(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(PeriodLengthFailure value) periodLengthFailure,
    required TResult Function(CycleLengthFailure value) cycleLengthFailure,
    required TResult Function(OvulationDateFailure value) ovulationDateFailure,
    required TResult Function(ContraceptionTypeFailure value)
        contraceptionTypeFailure,
    required TResult Function(Unexpected value) unexpected,
  }) {
    final _that = this;
    switch (_that) {
      case PeriodLengthFailure():
        return periodLengthFailure(_that);
      case CycleLengthFailure():
        return cycleLengthFailure(_that);
      case OvulationDateFailure():
        return ovulationDateFailure(_that);
      case ContraceptionTypeFailure():
        return contraceptionTypeFailure(_that);
      case Unexpected():
        return unexpected(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(PeriodLengthFailure value)? periodLengthFailure,
    TResult? Function(CycleLengthFailure value)? cycleLengthFailure,
    TResult? Function(OvulationDateFailure value)? ovulationDateFailure,
    TResult? Function(ContraceptionTypeFailure value)? contraceptionTypeFailure,
    TResult? Function(Unexpected value)? unexpected,
  }) {
    final _that = this;
    switch (_that) {
      case PeriodLengthFailure() when periodLengthFailure != null:
        return periodLengthFailure(_that);
      case CycleLengthFailure() when cycleLengthFailure != null:
        return cycleLengthFailure(_that);
      case OvulationDateFailure() when ovulationDateFailure != null:
        return ovulationDateFailure(_that);
      case ContraceptionTypeFailure() when contraceptionTypeFailure != null:
        return contraceptionTypeFailure(_that);
      case Unexpected() when unexpected != null:
        return unexpected(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? periodLengthFailure,
    TResult Function()? cycleLengthFailure,
    TResult Function()? ovulationDateFailure,
    TResult Function()? contraceptionTypeFailure,
    TResult Function()? unexpected,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case PeriodLengthFailure() when periodLengthFailure != null:
        return periodLengthFailure();
      case CycleLengthFailure() when cycleLengthFailure != null:
        return cycleLengthFailure();
      case OvulationDateFailure() when ovulationDateFailure != null:
        return ovulationDateFailure();
      case ContraceptionTypeFailure() when contraceptionTypeFailure != null:
        return contraceptionTypeFailure();
      case Unexpected() when unexpected != null:
        return unexpected();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() periodLengthFailure,
    required TResult Function() cycleLengthFailure,
    required TResult Function() ovulationDateFailure,
    required TResult Function() contraceptionTypeFailure,
    required TResult Function() unexpected,
  }) {
    final _that = this;
    switch (_that) {
      case PeriodLengthFailure():
        return periodLengthFailure();
      case CycleLengthFailure():
        return cycleLengthFailure();
      case OvulationDateFailure():
        return ovulationDateFailure();
      case ContraceptionTypeFailure():
        return contraceptionTypeFailure();
      case Unexpected():
        return unexpected();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? periodLengthFailure,
    TResult? Function()? cycleLengthFailure,
    TResult? Function()? ovulationDateFailure,
    TResult? Function()? contraceptionTypeFailure,
    TResult? Function()? unexpected,
  }) {
    final _that = this;
    switch (_that) {
      case PeriodLengthFailure() when periodLengthFailure != null:
        return periodLengthFailure();
      case CycleLengthFailure() when cycleLengthFailure != null:
        return cycleLengthFailure();
      case OvulationDateFailure() when ovulationDateFailure != null:
        return ovulationDateFailure();
      case ContraceptionTypeFailure() when contraceptionTypeFailure != null:
        return contraceptionTypeFailure();
      case Unexpected() when unexpected != null:
        return unexpected();
      case _:
        return null;
    }
  }
}

/// @nodoc

class PeriodLengthFailure implements HealthDataFailure {
  const PeriodLengthFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is PeriodLengthFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'HealthDataFailure.periodLengthFailure()';
  }
}

/// @nodoc

class CycleLengthFailure implements HealthDataFailure {
  const CycleLengthFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CycleLengthFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'HealthDataFailure.cycleLengthFailure()';
  }
}

/// @nodoc

class OvulationDateFailure implements HealthDataFailure {
  const OvulationDateFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OvulationDateFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'HealthDataFailure.ovulationDateFailure()';
  }
}

/// @nodoc

class ContraceptionTypeFailure implements HealthDataFailure {
  const ContraceptionTypeFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ContraceptionTypeFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'HealthDataFailure.contraceptionTypeFailure()';
  }
}

/// @nodoc

class Unexpected implements HealthDataFailure {
  const Unexpected();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Unexpected);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'HealthDataFailure.unexpected()';
  }
}

// dart format on
