import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'daily_medication_model.g.dart';

@JsonSerializable(explicitToJson: true)
class DailyMedicationModel extends Equatable {
  final String? medName;

  @JsonKey(
    fromJson: _timeOfDayFromJson,
    toJson: _timeOfDayToJson,
  )
  final TimeOfDay? time;
  final String? medFrequency;
  final String? medDosage;
  final String? medDosageUnit;
  final String? medNotes;

  @Json<PERSON>ey(
    fromJson: _timeOfDayFromJson,
    toJson: _timeOfDayToJson,
  )
  TimeOfDay? loggedTime;

  DailyMedicationModel({
    this.medName,
    this.time,
    this.medFrequency,
    this.medDosage,
    this.medDosageUnit,
    this.medNotes,
    this.loggedTime,
  });

  factory DailyMedicationModel.empty() => DailyMedicationModel(
    medName: '',
    time: TimeOfDay.now(),
    medFrequency: 'daily',
    medDosage: '10',
    medDosageUnit: 'mg',
    medNotes: '',
    loggedTime: null,
  );

  /// `copyWith` method for immutability.
  DailyMedicationModel copyWith({
    String? medName,
    TimeOfDay? time,
    String? medFrequency,
    String? medDosage,
    String? medDosageUnit,
    String? medNotes,
    TimeOfDay? loggedTime,
  }) {
    return DailyMedicationModel(
      medName: medName ?? this.medName,
      time: time ?? this.time,
      medFrequency: medFrequency ?? this.medFrequency,
      medDosage: medDosage ?? this.medDosage,
      medDosageUnit: medDosageUnit ?? this.medDosageUnit,
      medNotes: medNotes ?? this.medNotes,
      loggedTime: loggedTime ?? this.loggedTime,
    );
  }

  @override
  List<Object?> get props => [
    medName,
    time,
    medFrequency,
    medDosage,
    medDosageUnit,
    medNotes,
    loggedTime,
  ];

  Map<String, dynamic> toJson() => _$DailyMedicationModelToJson(this);

  factory DailyMedicationModel.fromJson(Map<String, dynamic> json) =>
      _$DailyMedicationModelFromJson(json);

  // Helper function to convert TimeOfDay to String (HH:mm)
  // static String? _timeOfDayToJson(TimeOfDay? time) =>
  //     time != null ? '${time.hour}:${time.minute}' : null;
  static String? _timeOfDayToJson(TimeOfDay? time) =>
      time != null ? '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}' : null;


  // Helper function to convert String (HH:mm) to TimeOfDay
  static TimeOfDay? _timeOfDayFromJson(String? time) {
    if (time == null) return null;
    final parts = time.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }
}
