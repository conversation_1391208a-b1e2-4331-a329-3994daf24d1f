// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'health_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HealthDataModel _$HealthDataModelFromJson(Map<String, dynamic> json) =>
    HealthDataModel(
      periodLength: (json['periodLength'] as num?)?.toInt(),
      cycleLength: (json['cycleLength'] as num?)?.toInt(),
      contraceptionType: (json['contraceptionType'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      ovulationDate:
          firestoreTimestampFromJson(json['ovulationDate'] as Timestamp?),
      lastPeriodDate:
          firestoreTimestampFromJson(json['lastPeriodDate'] as Timestamp?),
      nextPeriodStartDate:
          firestoreTimestampFromJson(json['nextPeriodStartDate'] as Timestamp?),
      nextOvulationDate:
          firestoreTimestampFromJson(json['nextOvulationDate'] as Timestamp?),
      periodReminderSettings: json['periodReminderSettings'] == null
          ? null
          : PeriodReminderSettings.fromJson(
              json['periodReminderSettings'] as Map<String, dynamic>),
      predictionMetadata: json['predictionMetadata'] == null
          ? null
          : PeriodPredictionMetadata.fromJson(
              json['predictionMetadata'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$HealthDataModelToJson(HealthDataModel instance) =>
    <String, dynamic>{
      'periodLength': instance.periodLength,
      'cycleLength': instance.cycleLength,
      'contraceptionType': instance.contraceptionType,
      'lastPeriodDate': firestoreTimestampToJson(instance.lastPeriodDate),
      'ovulationDate': firestoreTimestampToJson(instance.ovulationDate),
      'nextPeriodStartDate':
          firestoreTimestampToJson(instance.nextPeriodStartDate),
      'nextOvulationDate': firestoreTimestampToJson(instance.nextOvulationDate),
      'periodReminderSettings': instance.periodReminderSettings?.toJson(),
      'predictionMetadata': instance.predictionMetadata?.toJson(),
    };
