// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'period_prediction_metadata.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PeriodPredictionMetadata _$PeriodPredictionMetadataFromJson(
        Map<String, dynamic> json) =>
    PeriodPredictionMetadata(
      averageCycleLength: (json['averageCycleLength'] as num?)?.toDouble(),
      averagePeriodLength: (json['averagePeriodLength'] as num?)?.toDouble(),
      cycleVariance: (json['cycleVariance'] as num?)?.toDouble(),
      totalCyclesLogged: (json['totalCyclesLogged'] as num?)?.toInt(),
      hasIrregularCycles: json['hasIrregularCycles'] as bool?,
      lastPredictionUpdate: firestoreTimestampFromJson(
          json['lastPredictionUpdate'] as Timestamp?),
      lastActualPeriodStart: firestoreTimestampFromJson(
          json['lastActualPeriodStart'] as Timestamp?),
      activeHealthWarnings: (json['activeHealthWarnings'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      consecutiveMissedCycles:
          (json['consecutiveMissedCycles'] as num?)?.toInt(),
      lastMissedCycleDate:
          firestoreTimestampFromJson(json['lastMissedCycleDate'] as Timestamp?),
    );

Map<String, dynamic> _$PeriodPredictionMetadataToJson(
        PeriodPredictionMetadata instance) =>
    <String, dynamic>{
      'averageCycleLength': instance.averageCycleLength,
      'averagePeriodLength': instance.averagePeriodLength,
      'cycleVariance': instance.cycleVariance,
      'totalCyclesLogged': instance.totalCyclesLogged,
      'hasIrregularCycles': instance.hasIrregularCycles,
      'lastPredictionUpdate':
          firestoreTimestampToJson(instance.lastPredictionUpdate),
      'lastActualPeriodStart':
          firestoreTimestampToJson(instance.lastActualPeriodStart),
      'activeHealthWarnings': instance.activeHealthWarnings,
      'consecutiveMissedCycles': instance.consecutiveMissedCycles,
      'lastMissedCycleDate':
          firestoreTimestampToJson(instance.lastMissedCycleDate),
    };

DetectedCycle _$DetectedCycleFromJson(Map<String, dynamic> json) =>
    DetectedCycle(
      startDate: firestoreTimestampFromJson(json['startDate'] as Timestamp?),
      endDate: firestoreTimestampFromJson(json['endDate'] as Timestamp?),
      cycleLength: (json['cycleLength'] as num?)?.toInt(),
      periodLength: (json['periodLength'] as num?)?.toInt(),
      isComplete: json['isComplete'] as bool? ?? false,
    );

Map<String, dynamic> _$DetectedCycleToJson(DetectedCycle instance) =>
    <String, dynamic>{
      'startDate': firestoreTimestampToJson(instance.startDate),
      'endDate': firestoreTimestampToJson(instance.endDate),
      'cycleLength': instance.cycleLength,
      'periodLength': instance.periodLength,
      'isComplete': instance.isComplete,
    };
