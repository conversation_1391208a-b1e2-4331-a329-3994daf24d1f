import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

part 'period_prediction_metadata.g.dart';

// Helper functions to convert between DateTime and Firestore Timestamp
DateTime? firestoreTimestampFromJson(Timestamp? timestamp) =>
    timestamp?.toDate();

Timestamp? firestoreTimestampToJson(DateTime? dateTime) =>
    dateTime != null ? Timestamp.fromDate(dateTime) : null;

/// Metadata for period prediction calculations and reliability indicators
@JsonSerializable(explicitToJson: true)
class PeriodPredictionMetadata {
  // Running averages for adaptive learning
  double? averageCycleLength;
  double? averagePeriodLength;

  // Confidence and reliability indicators
  double? cycleVariance; // Variance in cycle lengths
  int? totalCyclesLogged; // Total number of complete cycles recorded
  bool? hasIrregularCycles; // Flag for irregular cycle warning

  // Prediction state
  @JsonKey(
    fromJson: firestoreTimestampFromJson,
    toJson: firestoreTimestampToJson,
  )
  DateTime? lastPredictionUpdate;

  @JsonKey(
    fromJson: firestoreTimestampFromJson,
    toJson: firestoreTimestampToJson,
  )
  DateTime? lastActualPeriodStart;

  // Health warnings
  List<String>? activeHealthWarnings;

  // Missed cycles tracking
  int? consecutiveMissedCycles;

  @JsonKey(
    fromJson: firestoreTimestampFromJson,
    toJson: firestoreTimestampToJson,
  )
  DateTime? lastMissedCycleDate;

  PeriodPredictionMetadata({
    this.averageCycleLength,
    this.averagePeriodLength,
    this.cycleVariance,
    this.totalCyclesLogged,
    this.hasIrregularCycles,
    this.lastPredictionUpdate,
    this.lastActualPeriodStart,
    this.activeHealthWarnings,
    this.consecutiveMissedCycles,
    this.lastMissedCycleDate,
  });

  factory PeriodPredictionMetadata.initial({
    required double initialCycleLength,
    required double initialPeriodLength,
    DateTime? lastPeriodDate,
  }) {
    return PeriodPredictionMetadata(
      averageCycleLength: initialCycleLength,
      averagePeriodLength: initialPeriodLength,
      cycleVariance: 0.0,
      totalCyclesLogged: 0,
      hasIrregularCycles: false,
      lastPredictionUpdate: DateTime.now(),
      lastActualPeriodStart: lastPeriodDate,
      activeHealthWarnings: [],
      consecutiveMissedCycles: 0,
    );
  }

  factory PeriodPredictionMetadata.fromJson(Map<String, dynamic> json) =>
      _$PeriodPredictionMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$PeriodPredictionMetadataToJson(this);

  PeriodPredictionMetadata copyWith({
    double? averageCycleLength,
    double? averagePeriodLength,
    double? cycleVariance,
    int? totalCyclesLogged,
    bool? hasIrregularCycles,
    DateTime? lastPredictionUpdate,
    DateTime? lastActualPeriodStart,
    List<String>? activeHealthWarnings,
    int? consecutiveMissedCycles,
    DateTime? lastMissedCycleDate,
  }) {
    return PeriodPredictionMetadata(
      averageCycleLength: averageCycleLength ?? this.averageCycleLength,
      averagePeriodLength: averagePeriodLength ?? this.averagePeriodLength,
      cycleVariance: cycleVariance ?? this.cycleVariance,
      totalCyclesLogged: totalCyclesLogged ?? this.totalCyclesLogged,
      hasIrregularCycles: hasIrregularCycles ?? this.hasIrregularCycles,
      lastPredictionUpdate: lastPredictionUpdate ?? this.lastPredictionUpdate,
      lastActualPeriodStart:
          lastActualPeriodStart ?? this.lastActualPeriodStart,
      activeHealthWarnings: activeHealthWarnings ?? this.activeHealthWarnings,
      consecutiveMissedCycles:
          consecutiveMissedCycles ?? this.consecutiveMissedCycles,
      lastMissedCycleDate: lastMissedCycleDate ?? this.lastMissedCycleDate,
    );
  }

  /// Check if predictions should be considered reliable
  bool get isPredictionReliable {
    if (totalCyclesLogged == null || totalCyclesLogged! < 2) return false;
    if (cycleVariance == null) return true;
    return cycleVariance! <= 7.0; // Variance threshold for reliability
  }

  /// Get reliability warning message if applicable
  String? get reliabilityWarning {
    if (totalCyclesLogged == null || totalCyclesLogged! < 2) {
      return "Not enough data for reliable predictions";
    }
    if (cycleVariance != null && cycleVariance! > 7.0) {
      return "Your cycle varies significantly. Predictions may be less reliable.";
    }
    return null;
  }

  /// Check if cycle length is outside normal range
  bool get hasCycleLengthWarning {
    if (averageCycleLength == null) return false;
    return averageCycleLength! < 20 || averageCycleLength! > 40;
  }

  /// Check if period length is concerning
  bool get hasPeriodLengthWarning {
    if (averagePeriodLength == null) return false;
    return averagePeriodLength! > 10;
  }

  /// Get health warning messages
  List<String> get healthWarnings {
    final warnings = <String>[];

    if (hasCycleLengthWarning) {
      warnings.add(
          "Your cycles are outside the typical range. Consider consulting a healthcare provider.");
    }

    if (hasPeriodLengthWarning) {
      warnings.add(
          "Your periods are longer than typical. Consider consulting a healthcare provider.");
    }

    return warnings;
  }
}

/// Represents a detected menstrual cycle with start/end dates and calculated metrics
@JsonSerializable(explicitToJson: true)
class DetectedCycle {
  @JsonKey(
    fromJson: firestoreTimestampFromJson,
    toJson: firestoreTimestampToJson,
  )
  DateTime? startDate;

  @JsonKey(
    fromJson: firestoreTimestampFromJson,
    toJson: firestoreTimestampToJson,
  )
  DateTime? endDate;

  int? cycleLength; // Days from start of this cycle to start of next
  int? periodLength; // Days of flow in this cycle
  bool isComplete; // Whether this cycle has ended (next cycle started)

  DetectedCycle({
    this.startDate,
    this.endDate,
    this.cycleLength,
    this.periodLength,
    this.isComplete = false,
  });

  factory DetectedCycle.fromJson(Map<String, dynamic> json) =>
      _$DetectedCycleFromJson(json);

  Map<String, dynamic> toJson() => _$DetectedCycleToJson(this);
}
