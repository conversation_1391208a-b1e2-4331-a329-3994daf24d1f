import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';
import 'period_reminder_settings.dart';
import 'period_prediction_metadata.dart';

part 'health_data.g.dart';

// Helper functions to convert between DateTime and Firestore Timestamp
DateTime? firestoreTimestampFromJson(Timestamp? timestamp) =>
    timestamp?.toDate();

Timestamp? firestoreTimestampToJson(DateTime? dateTime) =>
    dateTime != null ? Timestamp.fromDate(dateTime) : null;

// HealthData model class
@JsonSerializable(explicitToJson: true)
class HealthDataModel {
  int? periodLength;
  int? cycleLength;
  List<String>? contraceptionType;

  @JsonKey(
    fromJson: firestoreTimestampFromJson,
    toJson: firestoreTimestampToJson,
  )
  DateTime? lastPeriodDate;

  @JsonKey(
    fromJson: firestoreTimestampFromJson,
    toJson: firestoreTimestampToJson,
  )
  DateTime? ovulationDate;

  @JsonKey(
    fromJson: firestoreTimestampFromJson,
    toJson: firestoreTimestampToJson,
  )
  DateTime? nextPeriodStartDate;

  @JsonKey(
    fromJson: firestoreTimestampFromJson,
    toJson: firestoreTimestampToJson,
  )
  DateTime? nextOvulationDate;

  // Period reminder settings
  PeriodReminderSettings? periodReminderSettings;

  // Prediction metadata
  PeriodPredictionMetadata? predictionMetadata;

  HealthDataModel({
    this.periodLength,
    this.cycleLength,
    this.contraceptionType,
    this.ovulationDate,
    this.lastPeriodDate,
    this.nextPeriodStartDate,
    this.nextOvulationDate,
    this.periodReminderSettings,
    this.predictionMetadata,
  });

  // toJson and fromJson methods
  Map<String, dynamic> toJson() => _$HealthDataModelToJson(this);

  factory HealthDataModel.fromJson(Map<String, dynamic> json) =>
      _$HealthDataModelFromJson(json);

  HealthDataModel copyWith({
    int? periodLength,
    int? cycleLength,
    List<String>? contraceptionType,
    DateTime? lastPeriodDate,
    DateTime? ovulationDate,
    DateTime? nextPeriodStartDate,
    DateTime? nextOvulationDate,
    PeriodReminderSettings? periodReminderSettings,
    PeriodPredictionMetadata? predictionMetadata,
  }) {
    return HealthDataModel(
      periodLength: periodLength ?? this.periodLength,
      cycleLength: cycleLength ?? this.cycleLength,
      contraceptionType: contraceptionType ?? this.contraceptionType,
      lastPeriodDate: lastPeriodDate ?? this.lastPeriodDate,
      ovulationDate: ovulationDate ?? this.ovulationDate,
      nextPeriodStartDate: nextPeriodStartDate ?? this.nextPeriodStartDate,
      nextOvulationDate: nextOvulationDate ?? this.nextOvulationDate,
      periodReminderSettings:
          periodReminderSettings ?? this.periodReminderSettings,
      predictionMetadata: predictionMetadata ?? this.predictionMetadata,
    );
  }
}
