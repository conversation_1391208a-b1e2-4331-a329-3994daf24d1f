import 'dart:math';
import 'package:account_management/domain/model/period_tracking_model.dart';
import 'package:account_management/domain/model/period_prediction_metadata.dart';
import 'package:account_management/domain/model/health_data.dart';

/// Core engine for period prediction calculations and cycle analysis
class PeriodPredictionEngine {
  /// Detects period starts from flow symptom data (P2 - New Period Detection)
  static List<DateTime> detectPeriodStarts(
    Map<String, Map<String, PeriodTrackingModel>> yearData,
  ) {
    final periodStarts = <DateTime>[];
    final allFlowDates = <DateTime>[];

    // Extract all dates with flow symptoms
    for (final monthData in yearData.values) {
      for (final dayData in monthData.values) {
        if (dayData.flowLevel != null &&
            dayData.flowLevel! > 0 &&
            dayData.date != null) {
          allFlowDates.add(dayData.date!);
        }
      }
    }

    if (allFlowDates.isEmpty) return periodStarts;

    // Sort dates chronologically
    allFlowDates.sort();

    // Group consecutive flow dates into cycles
    List<List<DateTime>> flowCycles = [];
    List<DateTime> currentCycle = [allFlowDates.first];

    for (int i = 1; i < allFlowDates.length; i++) {
      final currentDate = allFlowDates[i];
      final previousDate = allFlowDates[i - 1];
      final daysDifference = currentDate.difference(previousDate).inDays;

      // If gap is more than 2 days, start new cycle
      if (daysDifference > 2) {
        flowCycles.add(currentCycle);
        currentCycle = [currentDate];
      } else {
        currentCycle.add(currentDate);
      }
    }
    flowCycles.add(currentCycle);

    // Fill single-day gaps within cycles (P2 continuation)
    for (var cycle in flowCycles) {
      cycle.sort();
      final filledCycle = <DateTime>[];

      for (int i = 0; i < cycle.length; i++) {
        filledCycle.add(cycle[i]);

        // Check for single-day gap
        if (i < cycle.length - 1) {
          final nextDate = cycle[i + 1];
          final currentDate = cycle[i];
          final gap = nextDate.difference(currentDate).inDays;

          if (gap == 2) {
            // Single day gap
            filledCycle.add(currentDate.add(const Duration(days: 1)));
          }
        }
      }

      // First date of each cycle is a period start
      if (filledCycle.isNotEmpty) {
        periodStarts.add(filledCycle.first);
      }
    }

    return periodStarts;
  }

  /// Calculates cycle lengths from detected period starts
  static List<int> calculateCycleLengths(List<DateTime> periodStarts) {
    if (periodStarts.length < 2) return [];

    final cycleLengths = <int>[];
    periodStarts.sort();

    for (int i = 1; i < periodStarts.length; i++) {
      final cycleLength =
          periodStarts[i].difference(periodStarts[i - 1]).inDays;

      // Only include reasonable cycle lengths (20-45 days)
      if (cycleLength >= 20 && cycleLength <= 45) {
        cycleLengths.add(cycleLength);
      }
    }

    return cycleLengths;
  }

  /// Calculates period lengths from flow data
  static List<int> calculatePeriodLengths(
    Map<String, Map<String, PeriodTrackingModel>> yearData,
    List<DateTime> periodStarts,
  ) {
    final periodLengths = <int>[];

    for (final periodStart in periodStarts) {
      int periodLength = 0;
      DateTime currentDate = periodStart;

      // Count consecutive days with flow from period start
      while (periodLength < 15) {
        // Max 15 days to prevent infinite loop
        final hasFlow = hasFlowOnDate(yearData, currentDate);

        if (hasFlow) {
          periodLength++;
          currentDate = currentDate.add(const Duration(days: 1));
        } else {
          // Check if this is end of period (2 consecutive days without flow)
          final nextDay = currentDate.add(const Duration(days: 1));
          final hasFlowNextDay = hasFlowOnDate(yearData, nextDay);

          if (!hasFlowNextDay) {
            break; // Period has ended
          } else {
            // Single day gap, continue counting
            periodLength++;
            currentDate = nextDay;
          }
        }
      }

      if (periodLength > 0 && periodLength <= 10) {
        periodLengths.add(periodLength);
      }
    }

    return periodLengths;
  }

  /// Helper method to check if a date has flow symptoms
  static bool hasFlowOnDate(
    Map<String, Map<String, PeriodTrackingModel>> yearData,
    DateTime date,
  ) {
    final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
    final dayKey = date.day.toString().padLeft(2, '0');

    final monthData = yearData[monthKey];
    if (monthData == null) return false;

    final dayData = monthData[dayKey];
    if (dayData == null) return false;

    return dayData.flowLevel != null && dayData.flowLevel! > 0;
  }

  /// Updates running averages using weighted calculation (A1 & A2 - Adaptive Learning)
  static PeriodPredictionMetadata updateAverages(
    PeriodPredictionMetadata currentMetadata,
    List<int> newCycleLengths,
    List<int> newPeriodLengths,
  ) {
    final totalCycles = currentMetadata.totalCyclesLogged ?? 0;
    final isEarlyLearning =
        totalCycles < 3; // A1: Aggressive learning for <3 cycles

    double newAvgCycleLength = currentMetadata.averageCycleLength ?? 28.0;
    double newAvgPeriodLength = currentMetadata.averagePeriodLength ?? 5.0;
    double newVariance = currentMetadata.cycleVariance ?? 0.0;

    if (newCycleLengths.isNotEmpty) {
      final newCycleAvg =
          newCycleLengths.reduce((a, b) => a + b) / newCycleLengths.length;

      if (isEarlyLearning) {
        // A1: Immediate update for early learning
        newAvgCycleLength = newCycleAvg;
      } else {
        // A2: Weighted average for established cycles
        const weight = 0.3; // 30% weight to new data
        newAvgCycleLength =
            (newAvgCycleLength * (1 - weight)) + (newCycleAvg * weight);
      }

      // Calculate variance for reliability indicator
      if (newCycleLengths.length > 1) {
        final variance = newCycleLengths
                .map((length) => pow(length - newAvgCycleLength, 2))
                .reduce((a, b) => a + b) /
            newCycleLengths.length;
        newVariance =
            isEarlyLearning ? variance : (newVariance * 0.7) + (variance * 0.3);
      }
    }

    if (newPeriodLengths.isNotEmpty) {
      final newPeriodAvg =
          newPeriodLengths.reduce((a, b) => a + b) / newPeriodLengths.length;

      if (isEarlyLearning) {
        newAvgPeriodLength = newPeriodAvg;
      } else {
        const weight = 0.3;
        newAvgPeriodLength =
            (newAvgPeriodLength * (1 - weight)) + (newPeriodAvg * weight);
      }
    }

    return currentMetadata.copyWith(
      averageCycleLength: newAvgCycleLength,
      averagePeriodLength: newAvgPeriodLength,
      cycleVariance: newVariance,
      totalCyclesLogged: totalCycles + newCycleLengths.length,
      lastPredictionUpdate: DateTime.now(),
      hasIrregularCycles: newVariance > 7.0,
    );
  }

  /// Generates future predictions based on current averages (P1 - Initial Setup)
  /// Handles cycle skipping by widening uncertainty window when 2+ cycles are missed
  static Map<String, Set<DateTime>> generateFuturePredictions(
      PeriodPredictionMetadata metadata, DateTime? lastPeriodStart,
      {int monthsAhead = 6}) {
    final futurePeriods = <DateTime>{};
    final futureOvulations = <DateTime>{};

    if (lastPeriodStart == null ||
        metadata.averageCycleLength == null ||
        metadata.averagePeriodLength == null) {
      return {'periods': futurePeriods, 'ovulations': futureOvulations};
    }

    final cycleLength = metadata.averageCycleLength!.round();
    final periodLength = metadata.averagePeriodLength!.round();
    final now = DateTime.now();
    final endDate = now.add(Duration(days: monthsAhead * 30));

    // M1: Handle cycle skipping - widen uncertainty window if 2+ cycles missed
    final missedCycles = metadata.consecutiveMissedCycles ?? 0;
    final hasSkippedCycles = missedCycles >= 2;

    // Widen uncertainty window based on missed cycles
    final uncertaintyDays =
        hasSkippedCycles ? (missedCycles * 3).clamp(3, 14) : 0;
    final ovulationWindowDays =
        hasSkippedCycles ? 4 : 2; // ±4 days instead of ±2 when cycles skipped

    DateTime nextPeriodStart = lastPeriodStart.add(Duration(days: cycleLength));

    // Generate predictions until end date
    while (nextPeriodStart.isBefore(endDate)) {
      if (nextPeriodStart.isAfter(now)) {
        // Add period dates with uncertainty window for skipped cycles
        final periodStartRange = hasSkippedCycles
            ? [
                nextPeriodStart.subtract(Duration(days: uncertaintyDays)),
                nextPeriodStart,
                nextPeriodStart.add(Duration(days: uncertaintyDays))
              ]
            : [nextPeriodStart];

        for (final periodStart in periodStartRange) {
          for (int i = 0; i < periodLength; i++) {
            final periodDate = periodStart.add(Duration(days: i));
            if (periodDate.isAfter(now) && periodDate.isBefore(endDate)) {
              futurePeriods.add(periodDate);
            }
          }
        }

        // Add ovulation window with widened range for skipped cycles
        final ovulationDate =
            nextPeriodStart.add(Duration(days: cycleLength - 14));
        for (int i = -ovulationWindowDays; i <= ovulationWindowDays; i++) {
          final ovulationDay = ovulationDate.add(Duration(days: i));
          if (ovulationDay.isAfter(now) && ovulationDay.isBefore(endDate)) {
            futureOvulations.add(ovulationDay);
          }
        }
      }

      nextPeriodStart = nextPeriodStart.add(Duration(days: cycleLength));
    }

    return {'periods': futurePeriods, 'ovulations': futureOvulations};
  }

  /// Handles missed cycles detection (M1)
  static PeriodPredictionMetadata handleMissedCycles(
    PeriodPredictionMetadata metadata,
    DateTime? lastActualPeriod,
  ) {
    if (lastActualPeriod == null || metadata.averageCycleLength == null) {
      return metadata;
    }

    final now = DateTime.now();
    final daysSinceLastPeriod = now.difference(lastActualPeriod).inDays;
    final expectedCycleLength = metadata.averageCycleLength!.round();
    final missedCycles = (daysSinceLastPeriod / expectedCycleLength).floor();

    if (missedCycles >= 2) {
      return metadata.copyWith(
        consecutiveMissedCycles: missedCycles,
        lastMissedCycleDate: now,
      );
    }

    return metadata.copyWith(consecutiveMissedCycles: 0);
  }

  /// Recalculates all predictions when data is edited (X1 & X2)
  static PeriodPredictionMetadata recalculateFromScratch(
    Map<String, Map<String, PeriodTrackingModel>> yearData,
    HealthDataModel? healthData,
  ) {
    // Start with initial values from health data or defaults
    final initialCycleLength = healthData?.cycleLength?.toDouble() ?? 28.0;
    final initialPeriodLength = healthData?.periodLength?.toDouble() ?? 5.0;
    final lastPeriodDate = healthData?.lastPeriodDate;

    var metadata = PeriodPredictionMetadata.initial(
      initialCycleLength: initialCycleLength,
      initialPeriodLength: initialPeriodLength,
      lastPeriodDate: lastPeriodDate,
    );

    // Detect all period starts from flow data
    final periodStarts = detectPeriodStarts(yearData);

    if (periodStarts.isNotEmpty) {
      // Calculate cycle and period lengths
      final cycleLengths = calculateCycleLengths(periodStarts);
      final periodLengths = calculatePeriodLengths(yearData, periodStarts);

      // Update averages with all available data
      metadata = updateAverages(metadata, cycleLengths, periodLengths);

      // Update last actual period start
      periodStarts.sort();
      metadata = metadata.copyWith(lastActualPeriodStart: periodStarts.last);
    }

    // Check for missed cycles
    metadata = handleMissedCycles(metadata, metadata.lastActualPeriodStart);

    // Update health warnings
    metadata = updateHealthWarnings(metadata);

    return metadata;
  }

  /// Updates health warnings based on current data (H2 & H3)
  static PeriodPredictionMetadata updateHealthWarnings(
    PeriodPredictionMetadata metadata,
  ) {
    final warnings = <String>[];

    // H2: Cycle length warnings
    if (metadata.averageCycleLength != null) {
      final avgCycle = metadata.averageCycleLength!;
      if (avgCycle < 20 || avgCycle > 40) {
        warnings.add(
            "Your cycles are outside the typical range. Consider consulting a healthcare provider.");
      }
    }

    // H3: Period length warnings
    if (metadata.averagePeriodLength != null) {
      final avgPeriod = metadata.averagePeriodLength!;
      if (avgPeriod > 10) {
        warnings.add(
            "Your periods are longer than typical. Consider consulting a healthcare provider.");
      }
    }

    // IR1: Reliability warnings
    if (metadata.cycleVariance != null && metadata.cycleVariance! > 7.0) {
      warnings.add(
          "Your cycle varies significantly. Predictions may be less reliable.");
    }

    return metadata.copyWith(
      activeHealthWarnings: warnings,
      hasIrregularCycles:
          metadata.cycleVariance != null && metadata.cycleVariance! > 7.0,
    );
  }

  /// Handles multiple period starts in same cycle (X2)
  static List<DateTime> resolveDuplicatePeriodStarts(
    List<DateTime> periodStarts,
    double averageCycleLength,
  ) {
    if (periodStarts.length < 2) return periodStarts;

    final resolvedStarts = <DateTime>[];
    periodStarts.sort();

    DateTime? lastValidStart;

    for (final start in periodStarts) {
      if (lastValidStart == null) {
        resolvedStarts.add(start);
        lastValidStart = start;
      } else {
        final daysSinceLastStart = start.difference(lastValidStart).inDays;

        // If starts are too close (less than half average cycle), use earliest
        if (daysSinceLastStart < (averageCycleLength / 2)) {
          // Keep the earlier start, skip this one
          continue;
        } else {
          resolvedStarts.add(start);
          lastValidStart = start;
        }
      }
    }

    return resolvedStarts;
  }

  /// Initializes predictions from onboarding data (P1)
  static Map<String, Set<DateTime>> initializePredictionsFromOnboarding(
    DateTime lastPeriodDate,
    int cycleLength,
    int periodLength,
  ) {
    final futurePeriods = <DateTime>{};
    final futureOvulations = <DateTime>{};

    // Calculate next period start
    final nextPeriodStart = lastPeriodDate.add(Duration(days: cycleLength));
    final now = DateTime.now();

    if (nextPeriodStart.isAfter(now)) {
      // Add predicted period days
      for (int i = 0; i < periodLength; i++) {
        futurePeriods.add(nextPeriodStart.add(Duration(days: i)));
      }

      // Add predicted ovulation window (14 days before next period ±2 days)
      final ovulationDate = nextPeriodStart.subtract(const Duration(days: 14));
      for (int i = -2; i <= 2; i++) {
        final ovulationDay = ovulationDate.add(Duration(days: i));
        if (ovulationDay.isAfter(now)) {
          futureOvulations.add(ovulationDay);
        }
      }
    }

    return {'periods': futurePeriods, 'ovulations': futureOvulations};
  }
}
