// import 'package:bloc_test/bloc_test.dart';
// import 'package:mocktail/mocktail.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:account_management/application/account_management_bloc/account_management_bloc.dart';
// import 'package:account_management/domain/facade/account_management_facade.dart';
// import 'package:account_management/domain/failure/account_management_failures.dart';
// import 'package:account_management/domain/model/account_details_model.dart';
// import 'package:fpdart/fpdart.dart';
// import 'package:image_picker/image_picker.dart';
//
// class MockAccountManagementFacade extends Mock implements AccountManagementFacade {}
//
// void main() {
//   late AccountManagementBloc accountManagementBloc;
//   late MockAccountManagementFacade mockAccountManagementFacade;
//
//   setUp(() {
//     mockAccountManagementFacade = MockAccountManagementFacade();
//     accountManagementBloc = AccountManagementBloc(mockAccountManagementFacade);
//   });
//
//   group('AccountManagementBloc', () {
//     var accountDetails = AccountDetailsModel(userName: 'Test User', userEmail: '<EMAIL>');
//     const password = 'newPassword';
//     const email = '<EMAIL>';
//     const phoneNumber = '**********';
//     final profilePicture = XFile('path/to/profile_picture.png');
//     const failure = AccountManagementFailure.accountDetailsUpdateFailure('Failed to update account details');
//
//     blocTest<AccountManagementBloc, AccountManagementState>(
//       'emits [updating, updated] when updateAccountDetails is successful',
//       build: () {
//         when(() => mockAccountManagementFacade.updateAccountDetails(accountDetails))
//             .thenAnswer((_) async => const Right(unit));
//         return accountManagementBloc;
//       },
//       act: (bloc) => bloc.add( AccountManagementEvent.updateAccountDetails(accountDetails)),
//       expect: () => [
//         const AccountManagementState.updating(),
//         const AccountManagementState.updated(),
//       ],
//     );
//
//     blocTest<AccountManagementBloc, AccountManagementState>(
//       'emits [updating, updateFailure] when updateAccountDetails fails',
//       build: () {
//         when(() => mockAccountManagementFacade.updateAccountDetails(accountDetails))
//             .thenAnswer((_) async => const Left(failure));
//         return accountManagementBloc;
//       },
//       act: (bloc) => bloc.add( AccountManagementEvent.updateAccountDetails(accountDetails)),
//       expect: () => [
//         const AccountManagementState.updating(),
//         const AccountManagementState.updateFailure(failure),
//       ],
//     );
//
//     blocTest<AccountManagementBloc, AccountManagementState>(
//       'emits [updating, updated] when updateAccountPassword is successful',
//       build: () {
//         when(() => mockAccountManagementFacade.updateAccountPassword(password))
//             .thenAnswer((_) async => const Right(unit));
//         return accountManagementBloc;
//       },
//       act: (bloc) => bloc.add(const AccountManagementEvent.updateAccountPassword(password)),
//       expect: () => [
//         const AccountManagementState.updating(),
//         const AccountManagementState.updated(),
//       ],
//     );
//
//     blocTest<AccountManagementBloc, AccountManagementState>(
//       'emits [updating, updateFailure] when updateAccountPassword fails',
//       build: () {
//         when(() => mockAccountManagementFacade.updateAccountPassword(password))
//             .thenAnswer((_) async => const Left(failure));
//         return accountManagementBloc;
//       },
//       act: (bloc) => bloc.add(const AccountManagementEvent.updateAccountPassword(password)),
//       expect: () => [
//         const AccountManagementState.updating(),
//         const AccountManagementState.updateFailure(failure),
//       ],
//     );
//
//     blocTest<AccountManagementBloc, AccountManagementState>(
//       'emits [updating, updated] when updateAccountEmail is successful',
//       build: () {
//         when(() => mockAccountManagementFacade.updateAccountEmail(email))
//             .thenAnswer((_) async => const Right(unit));
//         return accountManagementBloc;
//       },
//       act: (bloc) => bloc.add(const AccountManagementEvent.updateAccountEmail(email)),
//       expect: () => [
//         const AccountManagementState.updating(),
//         const AccountManagementState.updated(),
//       ],
//     );
//
//     blocTest<AccountManagementBloc, AccountManagementState>(
//       'emits [updating, updateFailure] when updateAccountEmail fails',
//       build: () {
//         when(() => mockAccountManagementFacade.updateAccountEmail(email))
//             .thenAnswer((_) async => const Left(failure));
//         return accountManagementBloc;
//       },
//       act: (bloc) => bloc.add(const AccountManagementEvent.updateAccountEmail(email)),
//       expect: () => [
//         const AccountManagementState.updating(),
//         const AccountManagementState.updateFailure(failure),
//       ],
//     );
//
//     blocTest<AccountManagementBloc, AccountManagementState>(
//       'emits [updating, updated] when updateAccountPhoneNumber is successful',
//       build: () {
//         when(() => mockAccountManagementFacade.updateAccountPhoneNumber(phoneNumber))
//             .thenAnswer((_) async => const Right(unit));
//         return accountManagementBloc;
//       },
//       act: (bloc) => bloc.add(const AccountManagementEvent.updateAccountPhoneNumber(phoneNumber)),
//       expect: () => [
//         const AccountManagementState.updating(),
//         const AccountManagementState.updated(),
//       ],
//     );
//
//     blocTest<AccountManagementBloc, AccountManagementState>(
//       'emits [updating, updateFailure] when updateAccountPhoneNumber fails',
//       build: () {
//         when(() => mockAccountManagementFacade.updateAccountPhoneNumber(phoneNumber))
//             .thenAnswer((_) async => const Left(failure));
//         return accountManagementBloc;
//       },
//       act: (bloc) => bloc.add(const AccountManagementEvent.updateAccountPhoneNumber(phoneNumber)),
//       expect: () => [
//         const AccountManagementState.updating(),
//         const AccountManagementState.updateFailure(failure),
//       ],
//     );
//
//     blocTest<AccountManagementBloc, AccountManagementState>(
//       'emits [updating, updated] when updateProfilePicture is successful',
//       build: () {
//         when(() => mockAccountManagementFacade.updateProfilePicture(profilePicture))
//             .thenAnswer((_) async => const Right(unit));
//         return accountManagementBloc;
//       },
//       act: (bloc) => bloc.add(AccountManagementEvent.updateProfilePicture(profilePicture)),
//       expect: () => [
//         const AccountManagementState.updating(),
//         const AccountManagementState.updated(),
//       ],
//     );
//
//     blocTest<AccountManagementBloc, AccountManagementState>(
//       'emits [updating, updateFailure] when updateProfilePicture fails',
//       build: () {
//         when(() => mockAccountManagementFacade.updateProfilePicture(profilePicture))
//             .thenAnswer((_) async => const Left(failure));
//         return accountManagementBloc;
//       },
//       act: (bloc) => bloc.add(AccountManagementEvent.updateProfilePicture(profilePicture)),
//       expect: () => [
//         const AccountManagementState.updating(),
//         const AccountManagementState.updateFailure(failure),
//       ],
//     );
//
//     blocTest<AccountManagementBloc, AccountManagementState>(
//       'emits [updating, updated] when deleteProfilePicture is successful',
//       build: () {
//         when(() => mockAccountManagementFacade.deleteProfilePicture())
//             .thenAnswer((_) async => const Right(unit));
//         return accountManagementBloc;
//       },
//       act: (bloc) => bloc.add(const AccountManagementEvent.deleteProfilePicture()),
//       expect: () => [
//         const AccountManagementState.updating(),
//         const AccountManagementState.updated(),
//       ],
//     );
//
//     blocTest<AccountManagementBloc, AccountManagementState>(
//       'emits [updating, updateFailure] when deleteProfilePicture fails',
//       build: () {
//         when(() => mockAccountManagementFacade.deleteProfilePicture())
//             .thenAnswer((_) async => const Left(failure));
//         return accountManagementBloc;
//       },
//       act: (bloc) => bloc.add(const AccountManagementEvent.deleteProfilePicture()),
//       expect: () => [
//         const AccountManagementState.updating(),
//         const AccountManagementState.updateFailure(failure),
//       ],
//     );
//   });
// }