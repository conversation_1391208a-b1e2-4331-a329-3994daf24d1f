import 'package:flutter_test/flutter_test.dart';
import 'package:account_management/domain/service/period_prediction_engine.dart';

void main() {
  group('P1 - Onboarding Prediction Test', () {
    test('should generate correct predictions from onboarding data', () {
      // Simulate onboarding data
      final lastPeriodDate = DateTime(2024, 1, 1); // January 1st
      const cycleLength = 28; // 28-day cycle
      const periodLength = 5; // 5-day period

      // Generate predictions using the engine
      final predictions = PeriodPredictionEngine.initializePredictionsFromOnboarding(
        lastPeriodDate,
        cycleLength,
        periodLength,
      );

      final futurePeriods = predictions['periods']!;
      final futureOvulations = predictions['ovulations']!;

      // Verify predictions are generated
      expect(futurePeriods.isNotEmpty, isTrue, reason: 'Should generate future period dates');
      expect(futureOvulations.isNotEmpty, isTrue, reason: 'Should generate future ovulation dates');

      // Expected next period start: January 1 + 28 days = January 29
      final expectedNextPeriodStart = DateTime(2024, 1, 29);
      expect(futurePeriods.contains(expectedNextPeriodStart), isTrue,
          reason: 'Should predict next period start on January 29');

      // Expected period dates: January 29, 30, 31, February 1, 2
      final expectedPeriodDates = [
        DateTime(2024, 1, 29),
        DateTime(2024, 1, 30),
        DateTime(2024, 1, 31),
        DateTime(2024, 2, 1),
        DateTime(2024, 2, 2),
      ];

      for (final expectedDate in expectedPeriodDates) {
        expect(futurePeriods.contains(expectedDate), isTrue,
            reason: 'Should predict period on ${expectedDate.toString()}');
      }

      // Expected ovulation date: January 29 - 14 days = January 15
      final expectedOvulationDate = DateTime(2024, 1, 15);
      expect(futureOvulations.contains(expectedOvulationDate), isTrue,
          reason: 'Should predict ovulation on January 15');

      // Expected ovulation window: January 13, 14, 15, 16, 17 (±2 days)
      final expectedOvulationWindow = [
        DateTime(2024, 1, 13),
        DateTime(2024, 1, 14),
        DateTime(2024, 1, 15),
        DateTime(2024, 1, 16),
        DateTime(2024, 1, 17),
      ];

      for (final expectedDate in expectedOvulationWindow) {
        expect(futureOvulations.contains(expectedDate), isTrue,
            reason: 'Should predict ovulation window on ${expectedDate.toString()}');
      }

      print('✅ P1 Test Results:');
      print('📅 Future periods: ${futurePeriods.length} dates');
      print('🥚 Future ovulations: ${futureOvulations.length} dates');
      print('🔮 Next period start: $expectedNextPeriodStart');
      print('🔮 Ovulation date: $expectedOvulationDate');
    });

    test('should handle different cycle lengths correctly', () {
      final lastPeriodDate = DateTime(2024, 1, 1);
      const cycleLength = 30; // 30-day cycle
      const periodLength = 4; // 4-day period

      final predictions = PeriodPredictionEngine.initializePredictionsFromOnboarding(
        lastPeriodDate,
        cycleLength,
        periodLength,
      );

      final futurePeriods = predictions['periods']!;
      final futureOvulations = predictions['ovulations']!;

      // Expected next period start: January 1 + 30 days = January 31
      final expectedNextPeriodStart = DateTime(2024, 1, 31);
      expect(futurePeriods.contains(expectedNextPeriodStart), isTrue);

      // Expected ovulation date: January 31 - 14 days = January 17
      final expectedOvulationDate = DateTime(2024, 1, 17);
      expect(futureOvulations.contains(expectedOvulationDate), isTrue);

      print('✅ Different cycle length test passed');
      print('📅 30-day cycle next period: $expectedNextPeriodStart');
      print('🥚 30-day cycle ovulation: $expectedOvulationDate');
    });

    test('should not generate predictions for past dates', () {
      // Use a past date as last period
      final lastPeriodDate = DateTime.now().subtract(const Duration(days: 60));
      const cycleLength = 28;
      const periodLength = 5;

      final predictions = PeriodPredictionEngine.initializePredictionsFromOnboarding(
        lastPeriodDate,
        cycleLength,
        periodLength,
      );

      final futurePeriods = predictions['periods']!;
      final futureOvulations = predictions['ovulations']!;
      final now = DateTime.now();

      // All predictions should be in the future
      for (final periodDate in futurePeriods) {
        expect(periodDate.isAfter(now), isTrue,
            reason: 'Period prediction $periodDate should be in the future');
      }

      for (final ovulationDate in futureOvulations) {
        expect(ovulationDate.isAfter(now), isTrue,
            reason: 'Ovulation prediction $ovulationDate should be in the future');
      }

      print('✅ Future-only predictions test passed');
    });
  });
}
