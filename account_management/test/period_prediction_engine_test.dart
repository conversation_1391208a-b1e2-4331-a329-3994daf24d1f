import 'package:flutter_test/flutter_test.dart';
import 'package:account_management/domain/service/period_prediction_engine.dart';
import 'package:account_management/domain/model/period_prediction_metadata.dart';
import 'package:account_management/domain/model/period_tracking_model.dart';
import 'package:account_management/domain/model/health_data.dart';

void main() {
  group('PeriodPredictionEngine', () {
    late Map<String, Map<String, PeriodTrackingModel>> testYearData;

    setUp(() {
      testYearData = {};
    });

    group('Period Start Detection (P2)', () {
      test('should detect period starts from flow symptoms', () {
        // Create test data with flow symptoms
        final testData = _createTestDataWithFlow([
          DateTime(2024, 1, 1), // Period start
          DateTime(2024, 1, 2),
          DateTime(2024, 1, 3),
          DateTime(2024, 1, 28), // Next period start
          DateTime(2024, 1, 29),
          DateTime(2024, 1, 30),
        ]);

        final periodStarts =
            PeriodPredictionEngine.detectPeriodStarts(testData);

        expect(periodStarts.length, equals(2));
        expect(periodStarts[0], equals(DateTime(2024, 1, 1)));
        expect(periodStarts[1], equals(DateTime(2024, 1, 28)));
      });

      test('should handle irregular flow with gaps', () {
        // Create test data with single-day gaps
        final testData = _createTestDataWithFlow([
          DateTime(2024, 1, 1), // Period start
          DateTime(2024, 1, 2),
          // Gap on day 3
          DateTime(2024, 1, 4), // Should be filled
          DateTime(2024, 1, 5),
        ]);

        final periodStarts =
            PeriodPredictionEngine.detectPeriodStarts(testData);

        expect(periodStarts.length, equals(1));
        expect(periodStarts[0], equals(DateTime(2024, 1, 1)));
      });

      test('should not fill gaps longer than 2 days', () {
        // Create test data with multi-day gaps
        final testData = _createTestDataWithFlow([
          DateTime(2024, 1, 1), // First period
          DateTime(2024, 1, 2),
          // 3-day gap
          DateTime(2024, 1, 6), // Should be new period
          DateTime(2024, 1, 7),
        ]);

        final periodStarts =
            PeriodPredictionEngine.detectPeriodStarts(testData);

        expect(periodStarts.length, equals(2));
        expect(periodStarts[0], equals(DateTime(2024, 1, 1)));
        expect(periodStarts[1], equals(DateTime(2024, 1, 6)));
      });
    });

    group('Cycle Length Calculation', () {
      test('should calculate cycle lengths from period starts', () {
        final periodStarts = [
          DateTime(2024, 1, 1),
          DateTime(2024, 1, 29), // 28-day cycle
          DateTime(2024, 2, 26), // 28-day cycle
          DateTime(2024, 3, 25), // 28-day cycle
        ];

        final cycleLengths =
            PeriodPredictionEngine.calculateCycleLengths(periodStarts);

        expect(cycleLengths.length, equals(3));
        expect(cycleLengths[0], equals(28));
        expect(cycleLengths[1], equals(28));
        expect(cycleLengths[2], equals(27));
      });

      test('should filter out unreasonable cycle lengths', () {
        final periodStarts = [
          DateTime(2024, 1, 1),
          DateTime(2024, 1, 10), // 9-day cycle (too short)
          DateTime(2024, 3, 1), // 51-day cycle (too long)
          DateTime(2024, 3, 29), // 28-day cycle (normal)
        ];

        final cycleLengths =
            PeriodPredictionEngine.calculateCycleLengths(periodStarts);

        expect(cycleLengths.length, equals(1));
        expect(cycleLengths[0], equals(27));
      });
    });

    group('Period Length Calculation', () {
      test('should calculate period lengths from flow data', () {
        final testData = _createTestDataWithFlow([
          DateTime(2024, 1, 1), // 5-day period
          DateTime(2024, 1, 2),
          DateTime(2024, 1, 3),
          DateTime(2024, 1, 4),
          DateTime(2024, 1, 5),
          DateTime(2024, 1, 29), // 3-day period
          DateTime(2024, 1, 30),
          DateTime(2024, 1, 31),
        ]);

        final periodStarts = [DateTime(2024, 1, 1), DateTime(2024, 1, 29)];
        final periodLengths = PeriodPredictionEngine.calculatePeriodLengths(
          testData,
          periodStarts,
        );

        expect(periodLengths.length, equals(2));
        expect(periodLengths[0], equals(5));
        expect(periodLengths[1], equals(3));
      });
    });

    group('Adaptive Learning (A1 & A2)', () {
      test('should use aggressive learning for early cycles', () {
        final metadata = PeriodPredictionMetadata.initial(
          initialCycleLength: 28.0,
          initialPeriodLength: 5.0,
        );

        final newCycleLengths = [30, 32]; // New data
        final newPeriodLengths = [4, 6];

        final updatedMetadata = PeriodPredictionEngine.updateAverages(
          metadata,
          newCycleLengths,
          newPeriodLengths,
        );

        // Should use new averages directly (aggressive learning)
        expect(updatedMetadata.averageCycleLength, equals(31.0));
        expect(updatedMetadata.averagePeriodLength, equals(5.0));
        expect(updatedMetadata.totalCyclesLogged, equals(2));
      });

      test('should use weighted averages for established cycles', () {
        final metadata = PeriodPredictionMetadata(
          averageCycleLength: 28.0,
          averagePeriodLength: 5.0,
          totalCyclesLogged: 5, // Established cycles
          cycleVariance: 2.0,
        );

        final newCycleLengths = [32]; // Significantly different
        final newPeriodLengths = [7];

        final updatedMetadata = PeriodPredictionEngine.updateAverages(
          metadata,
          newCycleLengths,
          newPeriodLengths,
        );

        // Should use weighted average (less aggressive)
        expect(updatedMetadata.averageCycleLength,
            closeTo(29.2, 0.1)); // 70% old + 30% new
        expect(updatedMetadata.averagePeriodLength, closeTo(5.6, 0.1));
        expect(updatedMetadata.totalCyclesLogged, equals(6));
      });
    });

    group('Future Predictions (P1)', () {
      test('should generate future predictions based on metadata', () {
        final metadata = PeriodPredictionMetadata(
          averageCycleLength: 28.0,
          averagePeriodLength: 5.0,
        );

        final now = DateTime.now();
        final lastPeriodStart =
            now.subtract(const Duration(days: 14)); // 2 weeks ago
        final predictions = PeriodPredictionEngine.generateFuturePredictions(
          metadata,
          lastPeriodStart,
          monthsAhead: 2,
        );

        final futurePeriods = predictions['periods']!;
        final futureOvulations = predictions['ovulations']!;

        expect(futurePeriods.isNotEmpty, isTrue);
        expect(futureOvulations.isNotEmpty, isTrue);
      });

      test('should handle empty metadata gracefully', () {
        final metadata = PeriodPredictionMetadata();

        final predictions = PeriodPredictionEngine.generateFuturePredictions(
          metadata,
          null,
        );

        expect(predictions['periods']!.isEmpty, isTrue);
        expect(predictions['ovulations']!.isEmpty, isTrue);
      });
    });

    group('Missed Cycles (M1)', () {
      test('should detect missed cycles', () {
        final metadata = PeriodPredictionMetadata(
          averageCycleLength: 28.0,
        );

        final lastActualPeriod = DateTime.now()
            .subtract(const Duration(days: 70)); // ~2.5 cycles ago

        final updatedMetadata = PeriodPredictionEngine.handleMissedCycles(
          metadata,
          lastActualPeriod,
        );

        expect(updatedMetadata.consecutiveMissedCycles, equals(2));
        expect(updatedMetadata.lastMissedCycleDate, isNotNull);
      });
    });

    group('Data Recalculation (X1 & X2)', () {
      test('should recalculate from scratch when data changes', () {
        final testData = _createTestDataWithFlow([
          DateTime(2024, 1, 1),
          DateTime(2024, 1, 2),
          DateTime(2024, 1, 29),
          DateTime(2024, 1, 30),
        ]);

        final healthData = HealthDataModel(
          cycleLength: 30,
          periodLength: 4,
        );

        final metadata = PeriodPredictionEngine.recalculateFromScratch(
          testData,
          healthData,
        );

        expect(
            metadata.averageCycleLength, equals(28.0)); // Calculated from data
        expect(metadata.totalCyclesLogged, equals(1));
      });

      test('should resolve duplicate period starts', () {
        final periodStarts = [
          DateTime(2024, 1, 1),
          DateTime(2024, 1, 3), // Too close to first
          DateTime(2024, 1, 29), // Valid next cycle
        ];

        final resolved = PeriodPredictionEngine.resolveDuplicatePeriodStarts(
          periodStarts,
          28.0,
        );

        expect(resolved.length, equals(2));
        expect(resolved[0], equals(DateTime(2024, 1, 1)));
        expect(resolved[1], equals(DateTime(2024, 1, 29)));
      });
    });

    group('Health Warnings (H2 & H3)', () {
      test('should detect cycle length warnings', () {
        final metadata = PeriodPredictionMetadata(
          averageCycleLength: 18.0, // Too short
        );

        final updatedMetadata =
            PeriodPredictionEngine.updateHealthWarnings(metadata);

        expect(updatedMetadata.hasCycleLengthWarning, isTrue);
        expect(updatedMetadata.healthWarnings.isNotEmpty, isTrue);
        expect(
          updatedMetadata.healthWarnings.first,
          contains('cycles are outside the typical range'),
        );
      });

      test('should detect period length warnings', () {
        final metadata = PeriodPredictionMetadata(
          averagePeriodLength: 12.0, // Too long
        );

        final updatedMetadata =
            PeriodPredictionEngine.updateHealthWarnings(metadata);

        expect(updatedMetadata.hasPeriodLengthWarning, isTrue);
        expect(updatedMetadata.healthWarnings.isNotEmpty, isTrue);
        expect(
          updatedMetadata.healthWarnings.first,
          contains('periods are longer than typical'),
        );
      });
    });

    group('Reliability Indicators (IR1)', () {
      test('should indicate unreliable predictions for high variance', () {
        final metadata = PeriodPredictionMetadata(
          cycleVariance: 10.0, // High variance
          totalCyclesLogged: 5,
        );

        expect(metadata.isPredictionReliable, isFalse);
        expect(metadata.reliabilityWarning, isNotNull);
        expect(
          metadata.reliabilityWarning!,
          contains('cycle varies significantly'),
        );
      });

      test('should indicate reliable predictions for low variance', () {
        final metadata = PeriodPredictionMetadata(
          cycleVariance: 3.0, // Low variance
          totalCyclesLogged: 5,
        );

        expect(metadata.isPredictionReliable, isTrue);
        expect(metadata.reliabilityWarning, isNull);
      });
    });
  });
}

/// Helper method to create test data with flow symptoms
Map<String, Map<String, PeriodTrackingModel>> _createTestDataWithFlow(
  List<DateTime> flowDates,
) {
  final testData = <String, Map<String, PeriodTrackingModel>>{};

  for (final date in flowDates) {
    final monthKey = '${date.year}_${date.month.toString().padLeft(2, '0')}';
    final dayKey = date.day.toString().padLeft(2, '0');

    testData[monthKey] ??= {};
    testData[monthKey]![dayKey] = PeriodTrackingModel(
      date: date,
      flowLevel: 2, // Medium flow
      isPeriodDate: true,
    );
  }

  return testData;
}
