// import 'dart:async';
//
// import 'package:bloc_test/bloc_test.dart';
// import 'package:mocktail/mocktail.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:account_management/application/account_watcher_bloc/account_watcher_bloc.dart';
// import 'package:account_management/domain/facade/account_management_facade.dart';
// import 'package:account_management/domain/failure/account_management_failures.dart';
// import 'package:account_management/domain/model/account_details_model.dart';
// import 'package:fpdart/fpdart.dart';
//
// class MockAccountManagementFacade extends Mock implements AccountManagementFacade {}
//
// void main() {
//   late MockAccountManagementFacade mockAccountManagementFacade;
//   late AccountWatcherBloc accountWatcherBloc;
//
//   setUp(() {
//     mockAccountManagementFacade = MockAccountManagementFacade();
//     accountWatcherBloc = AccountWatcherBloc(mockAccountManagementFacade);
//   });
//
//   tearDown(() {
//     accountWatcherBloc.close();
//   });
//
//   group('AccountWatcherBloc', () {
//     var accountDetails = AccountDetailsModel(userName: 'Test User', userEmail: '<EMAIL>');
//     const failure = AccountManagementFailure.accountDetailsLoadFailure('Failed to load account details');
//
//     blocTest<AccountWatcherBloc, AccountWatcherState>(
//       'emits [loading, loadSuccess] when accounts are received successfully',
//       setUp: () {
//         final streamController = StreamController<Either<AccountManagementFailure, AccountDetailsModel>>();
//         when(() => mockAccountManagementFacade.getAccountDetails())
//             .thenAnswer((_) => streamController.stream);
//         streamController.add(Right(accountDetails));
//         addTearDown(streamController.close);
//       },
//       build: () => accountWatcherBloc,
//       act: (bloc) => bloc.add(const AccountWatcherEvent.watchAllStarted()),
//       expect: () => [
//         const AccountWatcherState.loading(),
//         AccountWatcherState.loadSuccess(accountDetails),
//       ],
//     );
//
//     blocTest<AccountWatcherBloc, AccountWatcherState>(
//       'emits [loading, loadFailure] when accounts fail to load',
//       setUp: () {
//         final streamController = StreamController<Either<AccountManagementFailure, AccountDetailsModel>>();
//         when(() => mockAccountManagementFacade.getAccountDetails())
//             .thenAnswer((_) => streamController.stream);
//         streamController.add(const Left(failure));
//         addTearDown(streamController.close);
//       },
//       build: () => accountWatcherBloc,
//       act: (bloc) => bloc.add(const AccountWatcherEvent.watchAllStarted()),
//       expect: () => [
//         const AccountWatcherState.loading(),
//         const AccountWatcherState.loadFailure(failure),
//       ],
//     );
//
//     blocTest<AccountWatcherBloc, AccountWatcherState>(
//       'cancels previous subscription when a new watchAllStarted event is added',
//       setUp: () {
//         final controller1 = StreamController<Either<AccountManagementFailure, AccountDetailsModel>>();
//         final controller2 = StreamController<Either<AccountManagementFailure, AccountDetailsModel>>();
//
//         var callCount = 0;
//         when(() => mockAccountManagementFacade.getAccountDetails()).thenAnswer((_) {
//           callCount++;
//           return callCount == 1 ? controller1.stream : controller2.stream;
//         });
//
//         addTearDown(() {
//           controller1.close();
//           controller2.close();
//         });
//       },
//       build: () => accountWatcherBloc,
//       act: (bloc) async {
//         bloc.add(const AccountWatcherEvent.watchAllStarted());
//         await Future.delayed(const Duration(milliseconds: 10));
//         bloc.add(const AccountWatcherEvent.watchAllStarted());
//       },
//       verify: (_) {
//         verify(() => mockAccountManagementFacade.getAccountDetails()).called(2);
//       },
//     );
//   });
// }