{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "changeToProd": "firebase use prod", "changeToDev": "firebase use default"}, "engines": {"node": "20"}, "main": "index.js", "dependencies": {"@sendgrid/mail": "^8.1.3", "firebase-admin": "^12.1.0", "firebase-functions": "^6.4.0"}, "devDependencies": {"eslint": "^8.15.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0"}, "private": true}