const functionsV1 = require("firebase-functions/v1");
const { logger } = require("firebase-functions");
const { onCall } = require("firebase-functions/v2/https");
const { setGlobalOptions } = require("firebase-functions/v2");
const admin = require("firebase-admin");
const sgMail = require("@sendgrid/mail");

// Initialize Admin SDK
admin.initializeApp();
const db = admin.firestore();

// Set global options for all v2 functions
setGlobalOptions({ region: "us-central1" });

/**
 * Helper function to create user document structure
 * @param {Object} user - Firebase user object
 * @return {Object} User document data
 */
function createUserDocumentData(user) {
  // Helper function to filter out undefined values
  const filterUndefined = (obj) => {
    const filtered = {};
    Object.keys(obj).forEach(key => {
      if (obj[key] !== undefined && obj[key] !== null) {
        filtered[key] = obj[key];
      }
    });
    return filtered;
  };

  // Properly serialize providerData to avoid Firestore serialization issues
  const serializedProviderData = user.providerData ? user.providerData.map(provider =>
    filterUndefined({
      uid: provider.uid,
      displayName: provider.displayName,
      email: provider.email,
      phoneNumber: provider.phoneNumber,
      photoURL: provider.photoURL,
      providerId: provider.providerId
    })
  ) : [];

  return filterUndefined({
    uid: user.uid,
    userEmail: user.email,
    userName: user.displayName || (user.email ? user.email.split("@")[0] : "User"),
    photoURL: user.photoURL,
    providerData: serializedProviderData,
    signUpTimeStamp: user.metadata?.creationTime ? new Date(user.metadata.creationTime) : undefined,
    loginTimeStamp: user.metadata?.lastSignInTime ? new Date(user.metadata.lastSignInTime) : undefined,
    isOnboarded: false, // Initialize onboarding status as false
  });
}

// Auth user create trigger (keep 1st gen; v2 doesn't support this trigger)
exports.sendWelcomeEmailAndCreateUserDoc = functionsV1.auth.user().onCreate(async (user) => {
  const userEmail = user.email;
  const userName = user.displayName || "User";
  const userDoc = createUserDocumentData(user);

  // Attempt to create the user document in Firestore
  try {
    const usersCollection = db.collection("users");
    await usersCollection.doc(userDoc.uid).set(userDoc, { merge: true });
    logger.info("User document created successfully for:", user.uid);
  } catch (error) {
    logger.error("Error during Firestore operation:", error);
    logger.error("Failed Firestore path:", `users/${user.uid}`);
  }

  // Configure and send the email
  const sendgridKey = (functionsV1.config().sendgrid && functionsV1.config().sendgrid.api_key) || process.env.SENDGRID_API_KEY;
  if (!sendgridKey) {
    logger.warn("SENDGRID API key is not configured. Skipping welcome email.");
    return;
  }
  sgMail.setApiKey(sendgridKey);

  const msg = {
    to: userEmail,
    from: { email: "<EMAIL>", name: "Juno Technologies" },
    templateId: "d-4cc107f257aa4434aced7519b56eeef5",
    dynamicTemplateData: {
      username: userName,
    },
  };

  try {
    await sgMail.send(msg);
    logger.info("Welcome email sent successfully to:", userEmail);
  } catch (error) {
    logger.error("Failed to send welcome email:", error?.response?.body || error);
  }
});

/**
 * Callable function to create user document for existing authenticated users (v2)
 */
exports.createMissingUserDocument = onCall(async (request) => {
  // Log request details for debugging
  logger.info("createMissingUserDocument called");
  logger.info("Request auth:", request.auth ? "Present" : "Missing");
  logger.info("Request data:", request.data);

  // Check if user is authenticated
  if (!request.auth) {
    logger.error("Authentication context missing in request");
    // In v2, throw HttpsError by requiring from v1 namespace
    throw new functionsV1.https.HttpsError("unauthenticated", "User must be authenticated to call this function.");  
  }

  const uid = request.auth.uid;
  logger.info("Authenticated user UID:", uid);

  try {
    // Check if user document already exists
    const userDocRef = db.collection("users").doc(uid);
    const userDocSnapshot = await userDocRef.get();

    if (userDocSnapshot.exists) {
      logger.info("User document already exists for:", uid);
      return { success: true, message: "User document already exists", created: false };
    }

    // Get user record from Firebase Auth to get complete user data
    const userRecord = await admin.auth().getUser(uid);

    // Create user document data
    const userDoc = createUserDocumentData(userRecord);

    // Create the user document in Firestore
    await userDocRef.set(userDoc, { merge: true });

    logger.info("Missing user document created successfully for:", uid);
    return {
      success: true,
      message: "User document created successfully",
      created: true,
      userData: userDoc,
    };
  } catch (error) {
    logger.error("Error creating missing user document:", error);
    logger.error("User ID:", uid);

    throw new functionsV1.https.HttpsError("internal", "Failed to create user document", {
      uid: uid,
      error: error.message,
    });
  }
});
