// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'email_verification_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$EmailVerificationEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is EmailVerificationEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationEvent()';
  }
}

/// @nodoc
class $EmailVerificationEventCopyWith<$Res> {
  $EmailVerificationEventCopyWith(
      EmailVerificationEvent _, $Res Function(EmailVerificationEvent) __);
}

/// Adds pattern-matching-related methods to [EmailVerificationEvent].
extension EmailVerificationEventPatterns on EmailVerificationEvent {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(RequestVerificationEmail value)? requestVerificationEmail,
    TResult Function(CheckEmailVerified value)? checkEmailVerified,
    TResult Function(ResendVerificationEmail value)? resendVerificationEmail,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case RequestVerificationEmail() when requestVerificationEmail != null:
        return requestVerificationEmail(_that);
      case CheckEmailVerified() when checkEmailVerified != null:
        return checkEmailVerified(_that);
      case ResendVerificationEmail() when resendVerificationEmail != null:
        return resendVerificationEmail(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(RequestVerificationEmail value)
        requestVerificationEmail,
    required TResult Function(CheckEmailVerified value) checkEmailVerified,
    required TResult Function(ResendVerificationEmail value)
        resendVerificationEmail,
  }) {
    final _that = this;
    switch (_that) {
      case RequestVerificationEmail():
        return requestVerificationEmail(_that);
      case CheckEmailVerified():
        return checkEmailVerified(_that);
      case ResendVerificationEmail():
        return resendVerificationEmail(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(RequestVerificationEmail value)? requestVerificationEmail,
    TResult? Function(CheckEmailVerified value)? checkEmailVerified,
    TResult? Function(ResendVerificationEmail value)? resendVerificationEmail,
  }) {
    final _that = this;
    switch (_that) {
      case RequestVerificationEmail() when requestVerificationEmail != null:
        return requestVerificationEmail(_that);
      case CheckEmailVerified() when checkEmailVerified != null:
        return checkEmailVerified(_that);
      case ResendVerificationEmail() when resendVerificationEmail != null:
        return resendVerificationEmail(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? requestVerificationEmail,
    TResult Function()? checkEmailVerified,
    TResult Function()? resendVerificationEmail,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case RequestVerificationEmail() when requestVerificationEmail != null:
        return requestVerificationEmail();
      case CheckEmailVerified() when checkEmailVerified != null:
        return checkEmailVerified();
      case ResendVerificationEmail() when resendVerificationEmail != null:
        return resendVerificationEmail();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() requestVerificationEmail,
    required TResult Function() checkEmailVerified,
    required TResult Function() resendVerificationEmail,
  }) {
    final _that = this;
    switch (_that) {
      case RequestVerificationEmail():
        return requestVerificationEmail();
      case CheckEmailVerified():
        return checkEmailVerified();
      case ResendVerificationEmail():
        return resendVerificationEmail();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? requestVerificationEmail,
    TResult? Function()? checkEmailVerified,
    TResult? Function()? resendVerificationEmail,
  }) {
    final _that = this;
    switch (_that) {
      case RequestVerificationEmail() when requestVerificationEmail != null:
        return requestVerificationEmail();
      case CheckEmailVerified() when checkEmailVerified != null:
        return checkEmailVerified();
      case ResendVerificationEmail() when resendVerificationEmail != null:
        return resendVerificationEmail();
      case _:
        return null;
    }
  }
}

/// @nodoc

class RequestVerificationEmail implements EmailVerificationEvent {
  const RequestVerificationEmail();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is RequestVerificationEmail);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationEvent.requestVerificationEmail()';
  }
}

/// @nodoc

class CheckEmailVerified implements EmailVerificationEvent {
  const CheckEmailVerified();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CheckEmailVerified);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationEvent.checkEmailVerified()';
  }
}

/// @nodoc

class ResendVerificationEmail implements EmailVerificationEvent {
  const ResendVerificationEmail();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ResendVerificationEmail);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationEvent.resendVerificationEmail()';
  }
}

/// @nodoc
mixin _$EmailVerificationState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is EmailVerificationState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationState()';
  }
}

/// @nodoc
class $EmailVerificationStateCopyWith<$Res> {
  $EmailVerificationStateCopyWith(
      EmailVerificationState _, $Res Function(EmailVerificationState) __);
}

/// Adds pattern-matching-related methods to [EmailVerificationState].
extension EmailVerificationStatePatterns on EmailVerificationState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Initial value)? initial,
    TResult Function(VerificationEmailSent value)? verificationEmailSent,
    TResult Function(VerificationInProgress value)? verificationInProgress,
    TResult Function(EmailVerified value)? emailVerified,
    TResult Function(EmailNotVerified value)? emailNotVerified,
    TResult Function(VerificationFailed value)? verificationFailed,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial(_that);
      case VerificationEmailSent() when verificationEmailSent != null:
        return verificationEmailSent(_that);
      case VerificationInProgress() when verificationInProgress != null:
        return verificationInProgress(_that);
      case EmailVerified() when emailVerified != null:
        return emailVerified(_that);
      case EmailNotVerified() when emailNotVerified != null:
        return emailNotVerified(_that);
      case VerificationFailed() when verificationFailed != null:
        return verificationFailed(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Initial value) initial,
    required TResult Function(VerificationEmailSent value)
        verificationEmailSent,
    required TResult Function(VerificationInProgress value)
        verificationInProgress,
    required TResult Function(EmailVerified value) emailVerified,
    required TResult Function(EmailNotVerified value) emailNotVerified,
    required TResult Function(VerificationFailed value) verificationFailed,
  }) {
    final _that = this;
    switch (_that) {
      case Initial():
        return initial(_that);
      case VerificationEmailSent():
        return verificationEmailSent(_that);
      case VerificationInProgress():
        return verificationInProgress(_that);
      case EmailVerified():
        return emailVerified(_that);
      case EmailNotVerified():
        return emailNotVerified(_that);
      case VerificationFailed():
        return verificationFailed(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Initial value)? initial,
    TResult? Function(VerificationEmailSent value)? verificationEmailSent,
    TResult? Function(VerificationInProgress value)? verificationInProgress,
    TResult? Function(EmailVerified value)? emailVerified,
    TResult? Function(EmailNotVerified value)? emailNotVerified,
    TResult? Function(VerificationFailed value)? verificationFailed,
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial(_that);
      case VerificationEmailSent() when verificationEmailSent != null:
        return verificationEmailSent(_that);
      case VerificationInProgress() when verificationInProgress != null:
        return verificationInProgress(_that);
      case EmailVerified() when emailVerified != null:
        return emailVerified(_that);
      case EmailNotVerified() when emailNotVerified != null:
        return emailNotVerified(_that);
      case VerificationFailed() when verificationFailed != null:
        return verificationFailed(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? initial,
    TResult Function()? verificationEmailSent,
    TResult Function()? verificationInProgress,
    TResult Function()? emailVerified,
    TResult Function()? emailNotVerified,
    TResult Function(String message)? verificationFailed,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial();
      case VerificationEmailSent() when verificationEmailSent != null:
        return verificationEmailSent();
      case VerificationInProgress() when verificationInProgress != null:
        return verificationInProgress();
      case EmailVerified() when emailVerified != null:
        return emailVerified();
      case EmailNotVerified() when emailNotVerified != null:
        return emailNotVerified();
      case VerificationFailed() when verificationFailed != null:
        return verificationFailed(_that.message);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() initial,
    required TResult Function() verificationEmailSent,
    required TResult Function() verificationInProgress,
    required TResult Function() emailVerified,
    required TResult Function() emailNotVerified,
    required TResult Function(String message) verificationFailed,
  }) {
    final _that = this;
    switch (_that) {
      case Initial():
        return initial();
      case VerificationEmailSent():
        return verificationEmailSent();
      case VerificationInProgress():
        return verificationInProgress();
      case EmailVerified():
        return emailVerified();
      case EmailNotVerified():
        return emailNotVerified();
      case VerificationFailed():
        return verificationFailed(_that.message);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? initial,
    TResult? Function()? verificationEmailSent,
    TResult? Function()? verificationInProgress,
    TResult? Function()? emailVerified,
    TResult? Function()? emailNotVerified,
    TResult? Function(String message)? verificationFailed,
  }) {
    final _that = this;
    switch (_that) {
      case Initial() when initial != null:
        return initial();
      case VerificationEmailSent() when verificationEmailSent != null:
        return verificationEmailSent();
      case VerificationInProgress() when verificationInProgress != null:
        return verificationInProgress();
      case EmailVerified() when emailVerified != null:
        return emailVerified();
      case EmailNotVerified() when emailNotVerified != null:
        return emailNotVerified();
      case VerificationFailed() when verificationFailed != null:
        return verificationFailed(_that.message);
      case _:
        return null;
    }
  }
}

/// @nodoc

class Initial implements EmailVerificationState {
  const Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationState.initial()';
  }
}

/// @nodoc

class VerificationEmailSent implements EmailVerificationState {
  const VerificationEmailSent();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is VerificationEmailSent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationState.verificationEmailSent()';
  }
}

/// @nodoc

class VerificationInProgress implements EmailVerificationState {
  const VerificationInProgress();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is VerificationInProgress);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationState.verificationInProgress()';
  }
}

/// @nodoc

class EmailVerified implements EmailVerificationState {
  const EmailVerified();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is EmailVerified);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationState.emailVerified()';
  }
}

/// @nodoc

class EmailNotVerified implements EmailVerificationState {
  const EmailNotVerified();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is EmailNotVerified);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'EmailVerificationState.emailNotVerified()';
  }
}

/// @nodoc

class VerificationFailed implements EmailVerificationState {
  const VerificationFailed({required this.message});

  final String message;

  /// Create a copy of EmailVerificationState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $VerificationFailedCopyWith<VerificationFailed> get copyWith =>
      _$VerificationFailedCopyWithImpl<VerificationFailed>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is VerificationFailed &&
            (identical(other.message, message) || other.message == message));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message);

  @override
  String toString() {
    return 'EmailVerificationState.verificationFailed(message: $message)';
  }
}

/// @nodoc
abstract mixin class $VerificationFailedCopyWith<$Res>
    implements $EmailVerificationStateCopyWith<$Res> {
  factory $VerificationFailedCopyWith(
          VerificationFailed value, $Res Function(VerificationFailed) _then) =
      _$VerificationFailedCopyWithImpl;
  @useResult
  $Res call({String message});
}

/// @nodoc
class _$VerificationFailedCopyWithImpl<$Res>
    implements $VerificationFailedCopyWith<$Res> {
  _$VerificationFailedCopyWithImpl(this._self, this._then);

  final VerificationFailed _self;
  final $Res Function(VerificationFailed) _then;

  /// Create a copy of EmailVerificationState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? message = null,
  }) {
    return _then(VerificationFailed(
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

// dart format on
