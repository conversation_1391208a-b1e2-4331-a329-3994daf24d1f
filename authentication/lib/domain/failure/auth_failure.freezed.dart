// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'auth_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AuthFailure {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is AuthFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AuthFailure()';
  }
}

/// @nodoc
class $AuthFailureCopyWith<$Res> {
  $AuthFailureCopyWith(AuthFailure _, $Res Function(AuthFailure) __);
}

/// Adds pattern-matching-related methods to [AuthFailure].
extension AuthFailurePatterns on AuthFailure {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(UserNotFound value)? userNotFound,
    TResult Function(CancelledByUser value)? cancelledByUser,
    TResult Function(ServerError value)? serverError,
    TResult Function(EmailAlreadyInUse value)? emailAlreadyInUse,
    TResult Function(InvalidEmailAndPasswordCombination value)?
        invalidEmailAndPasswordCombination,
    TResult Function(EmailVerificationFailed value)? emailVerificationFailed,
    TResult Function(EmailVerificationSendFailure value)?
        emailVerificationSendFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case UserNotFound() when userNotFound != null:
        return userNotFound(_that);
      case CancelledByUser() when cancelledByUser != null:
        return cancelledByUser(_that);
      case ServerError() when serverError != null:
        return serverError(_that);
      case EmailAlreadyInUse() when emailAlreadyInUse != null:
        return emailAlreadyInUse(_that);
      case InvalidEmailAndPasswordCombination()
          when invalidEmailAndPasswordCombination != null:
        return invalidEmailAndPasswordCombination(_that);
      case EmailVerificationFailed() when emailVerificationFailed != null:
        return emailVerificationFailed(_that);
      case EmailVerificationSendFailure()
          when emailVerificationSendFailure != null:
        return emailVerificationSendFailure(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(UserNotFound value) userNotFound,
    required TResult Function(CancelledByUser value) cancelledByUser,
    required TResult Function(ServerError value) serverError,
    required TResult Function(EmailAlreadyInUse value) emailAlreadyInUse,
    required TResult Function(InvalidEmailAndPasswordCombination value)
        invalidEmailAndPasswordCombination,
    required TResult Function(EmailVerificationFailed value)
        emailVerificationFailed,
    required TResult Function(EmailVerificationSendFailure value)
        emailVerificationSendFailure,
  }) {
    final _that = this;
    switch (_that) {
      case UserNotFound():
        return userNotFound(_that);
      case CancelledByUser():
        return cancelledByUser(_that);
      case ServerError():
        return serverError(_that);
      case EmailAlreadyInUse():
        return emailAlreadyInUse(_that);
      case InvalidEmailAndPasswordCombination():
        return invalidEmailAndPasswordCombination(_that);
      case EmailVerificationFailed():
        return emailVerificationFailed(_that);
      case EmailVerificationSendFailure():
        return emailVerificationSendFailure(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(UserNotFound value)? userNotFound,
    TResult? Function(CancelledByUser value)? cancelledByUser,
    TResult? Function(ServerError value)? serverError,
    TResult? Function(EmailAlreadyInUse value)? emailAlreadyInUse,
    TResult? Function(InvalidEmailAndPasswordCombination value)?
        invalidEmailAndPasswordCombination,
    TResult? Function(EmailVerificationFailed value)? emailVerificationFailed,
    TResult? Function(EmailVerificationSendFailure value)?
        emailVerificationSendFailure,
  }) {
    final _that = this;
    switch (_that) {
      case UserNotFound() when userNotFound != null:
        return userNotFound(_that);
      case CancelledByUser() when cancelledByUser != null:
        return cancelledByUser(_that);
      case ServerError() when serverError != null:
        return serverError(_that);
      case EmailAlreadyInUse() when emailAlreadyInUse != null:
        return emailAlreadyInUse(_that);
      case InvalidEmailAndPasswordCombination()
          when invalidEmailAndPasswordCombination != null:
        return invalidEmailAndPasswordCombination(_that);
      case EmailVerificationFailed() when emailVerificationFailed != null:
        return emailVerificationFailed(_that);
      case EmailVerificationSendFailure()
          when emailVerificationSendFailure != null:
        return emailVerificationSendFailure(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? userNotFound,
    TResult Function()? cancelledByUser,
    TResult Function()? serverError,
    TResult Function()? emailAlreadyInUse,
    TResult Function()? invalidEmailAndPasswordCombination,
    TResult Function()? emailVerificationFailed,
    TResult Function()? emailVerificationSendFailure,
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case UserNotFound() when userNotFound != null:
        return userNotFound();
      case CancelledByUser() when cancelledByUser != null:
        return cancelledByUser();
      case ServerError() when serverError != null:
        return serverError();
      case EmailAlreadyInUse() when emailAlreadyInUse != null:
        return emailAlreadyInUse();
      case InvalidEmailAndPasswordCombination()
          when invalidEmailAndPasswordCombination != null:
        return invalidEmailAndPasswordCombination();
      case EmailVerificationFailed() when emailVerificationFailed != null:
        return emailVerificationFailed();
      case EmailVerificationSendFailure()
          when emailVerificationSendFailure != null:
        return emailVerificationSendFailure();
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() userNotFound,
    required TResult Function() cancelledByUser,
    required TResult Function() serverError,
    required TResult Function() emailAlreadyInUse,
    required TResult Function() invalidEmailAndPasswordCombination,
    required TResult Function() emailVerificationFailed,
    required TResult Function() emailVerificationSendFailure,
  }) {
    final _that = this;
    switch (_that) {
      case UserNotFound():
        return userNotFound();
      case CancelledByUser():
        return cancelledByUser();
      case ServerError():
        return serverError();
      case EmailAlreadyInUse():
        return emailAlreadyInUse();
      case InvalidEmailAndPasswordCombination():
        return invalidEmailAndPasswordCombination();
      case EmailVerificationFailed():
        return emailVerificationFailed();
      case EmailVerificationSendFailure():
        return emailVerificationSendFailure();
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? userNotFound,
    TResult? Function()? cancelledByUser,
    TResult? Function()? serverError,
    TResult? Function()? emailAlreadyInUse,
    TResult? Function()? invalidEmailAndPasswordCombination,
    TResult? Function()? emailVerificationFailed,
    TResult? Function()? emailVerificationSendFailure,
  }) {
    final _that = this;
    switch (_that) {
      case UserNotFound() when userNotFound != null:
        return userNotFound();
      case CancelledByUser() when cancelledByUser != null:
        return cancelledByUser();
      case ServerError() when serverError != null:
        return serverError();
      case EmailAlreadyInUse() when emailAlreadyInUse != null:
        return emailAlreadyInUse();
      case InvalidEmailAndPasswordCombination()
          when invalidEmailAndPasswordCombination != null:
        return invalidEmailAndPasswordCombination();
      case EmailVerificationFailed() when emailVerificationFailed != null:
        return emailVerificationFailed();
      case EmailVerificationSendFailure()
          when emailVerificationSendFailure != null:
        return emailVerificationSendFailure();
      case _:
        return null;
    }
  }
}

/// @nodoc

class UserNotFound implements AuthFailure {
  const UserNotFound();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is UserNotFound);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AuthFailure.userNotFound()';
  }
}

/// @nodoc

class CancelledByUser implements AuthFailure {
  const CancelledByUser();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is CancelledByUser);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AuthFailure.cancelledByUser()';
  }
}

/// @nodoc

class ServerError implements AuthFailure {
  const ServerError();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ServerError);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AuthFailure.serverError()';
  }
}

/// @nodoc

class EmailAlreadyInUse implements AuthFailure {
  const EmailAlreadyInUse();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is EmailAlreadyInUse);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AuthFailure.emailAlreadyInUse()';
  }
}

/// @nodoc

class InvalidEmailAndPasswordCombination implements AuthFailure {
  const InvalidEmailAndPasswordCombination();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InvalidEmailAndPasswordCombination);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AuthFailure.invalidEmailAndPasswordCombination()';
  }
}

/// @nodoc

class EmailVerificationFailed implements AuthFailure {
  const EmailVerificationFailed();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is EmailVerificationFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AuthFailure.emailVerificationFailed()';
  }
}

/// @nodoc

class EmailVerificationSendFailure implements AuthFailure {
  const EmailVerificationSendFailure();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is EmailVerificationSendFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'AuthFailure.emailVerificationSendFailure()';
  }
}

// dart format on
