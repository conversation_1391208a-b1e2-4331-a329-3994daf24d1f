import 'package:firebase_auth/firebase_auth.dart'; // Importing FirebaseAuth for Firebase authentication
import 'package:google_sign_in/google_sign_in.dart'; // Importing GoogleSignIn for Google authentication
import 'package:injectable/injectable.dart'; // Importing injectable for dependency injection

// Annotation to mark the class as a module for dependency injection
@module
abstract class FirebaseInjectableModule {
  // Defining a lazy singleton for GoogleSignIn
  @lazySingleton
  GoogleSignIn get googleSignIn {
    final signIn = GoogleSignIn.instance;
    // Initialize with proper serverClientId configuration
    // For dev flavor, use the dev client ID from the dev google-services.json
    signIn.initialize(
      serverClientId:
          '741053870499-41f5n352h8289bpad66h1n2tfitj6ev4.apps.googleusercontent.com', // Dev web client ID
    );
    return signIn;
  }

  // Defining a lazy singleton for FirebaseAuth
  @lazySingleton
  FirebaseAuth get firebaseAuth => FirebaseAuth.instance;
}
