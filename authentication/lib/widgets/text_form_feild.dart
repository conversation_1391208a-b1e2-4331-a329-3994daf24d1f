import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';

class MyTextFormField extends StatelessWidget {
  final String? hintText;
  final String? heading;
  final bool? isMandatory;
  final String? Function(String?)? validator;
  final void Function(String?)? onSaved;
  final void Function(String?)? onchanged;
  final void Function(PointerDownEvent?)? onTapOutside;
  final bool? isPassword;
  final TextStyle? style;
  final TextStyle? inputStyle;
  final bool? isEmail;
  final double? opacity;
  final String? initialvalue;
  final TextEditingController? controller;
  final Key? refkey;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final Function()? ontap;
  final int? maxText;
  final int? maxLines;
  final TextInputAction? textAction;
  final Function(String)? onFieldsubmitted;
  final FocusNode? focusNode;
  final List<TextInputFormatter>? inputFormatters;
  final bool? isEnabled;
  final TextInputType? keyboardtype;

  MyTextFormField(
      {this.hintText,
      this.initialvalue,
      this.heading,
      this.validator,
      this.onSaved,
      this.isMandatory = true,
      this.isPassword = false,
      this.isEmail = false,
      this.isEnabled = true,
      this.controller,
      this.onchanged,
      this.opacity,
      this.refkey,
      this.suffixIcon,
      this.ontap,
      this.style,
        this.inputStyle,
      this.maxText,
      this.textAction,
      this.onFieldsubmitted,
      this.keyboardtype,
      this.focusNode,
        this.prefixIcon,
      this.inputFormatters,
      this.maxLines,
      this.onTapOutside
      }
  );

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(heading??"",style: Theme.of(context).textTheme.bodySmall!.copyWith(color:Color(0xff30285D), ),),
        SizedBox(height: 4,),

        TextFormField(
          autovalidateMode: AutovalidateMode.onUserInteraction,
          inputFormatters: inputFormatters,
          autocorrect: false,
          maxLength: maxText,
          maxLines: maxLines ?? 1,
          minLines: 1,
          onTap: ontap,
          onTapOutside: onTapOutside,
          style: inputStyle,
          decoration: InputDecoration(
            suffixIcon: suffixIcon,
            prefixIcon: prefixIcon,

            // labelText: heading,
            // labelStyle: GoogleFonts.poppins(color: const Color(0xff97AFDE)),
            floatingLabelAlignment: FloatingLabelAlignment.start,
            hintText: hintText,
            counterText: '',
            filled: true,
            contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 0),
            errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                borderSide: BorderSide(color: Colors.red,width: 1) ),
            disabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                borderSide: BorderSide(color: Color(0xff554C9F),width: 1) ),
            enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                borderSide: BorderSide(color: Color(0xff554C9F),width: 1) ),
            border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                borderSide: BorderSide(color: Color(0xff554C9F),width: 1) ),
            focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(10)),
                borderSide: BorderSide(color: Color(0xff554C9F),width: 1) ),
            errorMaxLines: 1,
            floatingLabelStyle: GoogleFonts.poppins(
              color: Colors.black,
            ),

            fillColor: Colors.white,
          ),
          obscureText: isPassword! ? true : false,
          onChanged: onchanged,
          initialValue: initialvalue,
          controller: controller,
          key: refkey,
          onFieldSubmitted: onFieldsubmitted,
          validator: validator,
          onSaved: onSaved,
          enabled: isEnabled,
          keyboardType: keyboardtype != null ? keyboardtype : TextInputType.text,
          textInputAction: textAction,

        ),
      ],
    );
  }
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}

class MaskedTextInputFormatter extends TextInputFormatter {
  final String mask;
  final String separator;

  MaskedTextInputFormatter({
    required this.mask,
    required this.separator,
  });

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.length > 0) {
      if (newValue.text.length > oldValue.text.length) {
        if (newValue.text.length > mask.length) return oldValue;
        if (newValue.text.length < mask.length &&
            mask[newValue.text.length - 1] == separator) {
          return TextEditingValue(
            text:
                '${oldValue.text}$separator${newValue.text.substring(newValue.text.length - 1)}',
            selection: TextSelection.collapsed(
              offset: newValue.selection.end + 1,
            ),
          );
        }
      }
    }
    return newValue;
  }
}
