import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'ota_firmware_model.g.dart';

/// OTA Firmware Model for Firebase Storage integration
/// This model represents firmware information stored in Firestore
/// and provides metadata for OTA updates
@JsonSerializable(explicitToJson: true)
class OtaFirmwareModel {
  /// Unique identifier for the firmware
  final String firmwareId;
  
  /// Device type this firmware is compatible with
  /// Currently using a dummy string since there's only one device type
  /// Future: This will support multiple device types like "juno_v1", "juno_v2", etc.
  final String deviceType;
  
  /// Firebase Storage download URL for the firmware file
  final String downloadUrl;
  
  /// Firmware version string (e.g., "2.1.0")
  final String version;
  
  /// DateTime when this firmware was created/uploaded
  @JsonKey(
    fromJson: _timestampFromJson,
    toJson: _timestampToJson,
  )
  final DateTime createdAt;
  
  /// File size in bytes
  final int fileSize;
  
  /// SHA-256 checksum for integrity verification
  final String checksum;
  
  /// Build date of the firmware
  final String buildDate;
  
  /// Hardware compatibility information
  final String hardwareVersion;
  
  /// Whether this firmware is marked as stable/production ready
  final bool isStable;
  
  /// Release notes or description
  final String? releaseNotes;
  
  /// Minimum required bootloader version
  final String? minBootloaderVersion;
  
  /// Additional metadata that can be extended in the future
  final Map<String, dynamic> metadata;

  const OtaFirmwareModel({
    required this.firmwareId,
    required this.deviceType,
    required this.downloadUrl,
    required this.version,
    required this.createdAt,
    required this.fileSize,
    required this.checksum,
    required this.buildDate,
    required this.hardwareVersion,
    this.isStable = true,
    this.releaseNotes,
    this.minBootloaderVersion,
    this.metadata = const {},
  });

  /// Create a copy with updated fields
  OtaFirmwareModel copyWith({
    String? firmwareId,
    String? deviceType,
    String? downloadUrl,
    String? version,
    DateTime? createdAt,
    int? fileSize,
    String? checksum,
    String? buildDate,
    String? hardwareVersion,
    bool? isStable,
    String? releaseNotes,
    String? minBootloaderVersion,
    Map<String, dynamic>? metadata,
  }) {
    return OtaFirmwareModel(
      firmwareId: firmwareId ?? this.firmwareId,
      deviceType: deviceType ?? this.deviceType,
      downloadUrl: downloadUrl ?? this.downloadUrl,
      version: version ?? this.version,
      createdAt: createdAt ?? this.createdAt,
      fileSize: fileSize ?? this.fileSize,
      checksum: checksum ?? this.checksum,
      buildDate: buildDate ?? this.buildDate,
      hardwareVersion: hardwareVersion ?? this.hardwareVersion,
      isStable: isStable ?? this.isStable,
      releaseNotes: releaseNotes ?? this.releaseNotes,
      minBootloaderVersion: minBootloaderVersion ?? this.minBootloaderVersion,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert from JSON (Firestore document)
  factory OtaFirmwareModel.fromJson(Map<String, dynamic> json) =>
      _$OtaFirmwareModelFromJson(json);

  /// Convert to JSON (for Firestore storage)
  Map<String, dynamic> toJson() => _$OtaFirmwareModelToJson(this);

  /// Check if this firmware is newer than another based on createdAt timestamp
  bool isNewerThan(OtaFirmwareModel other) {
    return createdAt.isAfter(other.createdAt);
  }

  /// Check if this firmware is compatible with the given device info
  bool isCompatibleWith({
    required String deviceType,
    required String hardwareVersion,
    String? bootloaderVersion,
  }) {
    // Check device type compatibility
    if (this.deviceType != deviceType) {
      return false;
    }

    // Check hardware version compatibility
    if (this.hardwareVersion != hardwareVersion) {
      return false;
    }

    // Check bootloader version if specified
    if (minBootloaderVersion != null && bootloaderVersion != null) {
      // In a real implementation, you'd do proper version comparison
      // For now, just check if bootloader version is provided
      return bootloaderVersion.isNotEmpty;
    }

    return true;
  }

  @override
  String toString() {
    return 'OtaFirmwareModel(firmwareId: $firmwareId, deviceType: $deviceType, version: $version, createdAt: $createdAt, isStable: $isStable)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OtaFirmwareModel && other.firmwareId == firmwareId;
  }

  @override
  int get hashCode => firmwareId.hashCode;
}

/// Helper functions for Firestore Timestamp conversion
DateTime _timestampFromJson(dynamic value) {
  if (value == null) return DateTime.now();
  if (value is Timestamp) return value.toDate();
  if (value is String) return DateTime.tryParse(value) ?? DateTime.now();
  return DateTime.now();
}

Timestamp _timestampToJson(DateTime dateTime) => Timestamp.fromDate(dateTime);
