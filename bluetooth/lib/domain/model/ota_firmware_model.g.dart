// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ota_firmware_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OtaFirmwareModel _$OtaFirmwareModelFromJson(Map<String, dynamic> json) =>
    OtaFirmwareModel(
      firmwareId: json['firmwareId'] as String,
      deviceType: json['deviceType'] as String,
      downloadUrl: json['downloadUrl'] as String,
      version: json['version'] as String,
      createdAt: _timestampFromJson(json['createdAt']),
      fileSize: (json['fileSize'] as num).toInt(),
      checksum: json['checksum'] as String,
      buildDate: json['buildDate'] as String,
      hardwareVersion: json['hardwareVersion'] as String,
      isStable: json['isStable'] as bool? ?? true,
      releaseNotes: json['releaseNotes'] as String?,
      minBootloaderVersion: json['minBootloaderVersion'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$OtaFirmwareModelToJson(OtaFirmwareModel instance) =>
    <String, dynamic>{
      'firmwareId': instance.firmwareId,
      'deviceType': instance.deviceType,
      'downloadUrl': instance.downloadUrl,
      'version': instance.version,
      'createdAt': _timestampToJson(instance.createdAt),
      'fileSize': instance.fileSize,
      'checksum': instance.checksum,
      'buildDate': instance.buildDate,
      'hardwareVersion': instance.hardwareVersion,
      'isStable': instance.isStable,
      'releaseNotes': instance.releaseNotes,
      'minBootloaderVersion': instance.minBootloaderVersion,
      'metadata': instance.metadata,
    };
