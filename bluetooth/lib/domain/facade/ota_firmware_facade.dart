import 'dart:typed_data';
import 'package:fpdart/fpdart.dart';

import '../failure/bluetooth_failure.dart';
import '../model/ota_firmware_model.dart';

/// Facade interface for OTA firmware operations
/// Handles fetching firmware metadata from Firestore and downloading firmware files from Firebase Storage
abstract class IOtaFirmwareFacade {
  /// Get the latest firmware for a specific device type
  /// Returns the most recent firmware based on createdAt timestamp
  /// 
  /// [deviceType] - The type of device to get firmware for (e.g., "juno_v1")
  /// [includeUnstable] - Whether to include unstable/beta firmware versions
  Future<Either<BluetoothFailure, OtaFirmwareModel?>> getLatestFirmware({
    required String deviceType,
    bool includeUnstable = false,
  });

  /// Get all available firmware versions for a device type
  /// Returns list sorted by createdAt timestamp (newest first)
  /// 
  /// [deviceType] - The type of device to get firmware for
  /// [includeUnstable] - Whether to include unstable/beta firmware versions
  /// [limit] - Maximum number of firmware versions to return
  Future<Either<BluetoothFailure, List<OtaFirmwareModel>>> getAllFirmware({
    required String deviceType,
    bool includeUnstable = false,
    int limit = 10,
  });

  /// Get a specific firmware by its ID
  /// 
  /// [firmwareId] - The unique identifier of the firmware
  Future<Either<BluetoothFailure, OtaFirmwareModel?>> getFirmwareById(
    String firmwareId,
  );

  /// Download firmware file from Firebase Storage
  /// Returns the firmware data as bytes
  /// 
  /// [firmware] - The firmware model containing download URL and metadata
  /// [onProgress] - Optional callback for download progress (0.0 to 1.0)
  Future<Either<BluetoothFailure, Uint8List>> downloadFirmware(
    OtaFirmwareModel firmware, {
    void Function(double progress)? onProgress,
  });

  /// Check if firmware is cached locally
  /// Returns true if the firmware file exists in local cache
  /// 
  /// [firmware] - The firmware model to check
  Future<bool> isFirmwareCached(OtaFirmwareModel firmware);

  /// Get cached firmware data
  /// Returns cached firmware bytes if available
  /// 
  /// [firmware] - The firmware model to retrieve from cache
  Future<Either<BluetoothFailure, Uint8List?>> getCachedFirmware(
    OtaFirmwareModel firmware,
  );

  /// Cache firmware data locally
  /// Stores firmware bytes in local storage for offline access
  /// 
  /// [firmware] - The firmware model metadata
  /// [data] - The firmware bytes to cache
  Future<Either<BluetoothFailure, Unit>> cacheFirmware(
    OtaFirmwareModel firmware,
    Uint8List data,
  );

  /// Clear cached firmware files
  /// Removes old or all cached firmware files to free up storage
  /// 
  /// [olderThan] - Optional: Remove files older than this duration
  /// [deviceType] - Optional: Remove files only for specific device type
  Future<Either<BluetoothFailure, Unit>> clearCache({
    Duration? olderThan,
    String? deviceType,
  });

  /// Verify firmware integrity
  /// Checks if downloaded firmware matches expected checksum
  /// 
  /// [firmware] - The firmware model with expected checksum
  /// [data] - The firmware bytes to verify
  Future<Either<BluetoothFailure, bool>> verifyFirmwareIntegrity(
    OtaFirmwareModel firmware,
    Uint8List data,
  );

  /// Check for firmware updates
  /// Compares current device firmware with latest available firmware
  /// 
  /// [deviceType] - The type of device to check updates for
  /// [currentFirmwareDate] - The creation date of current device firmware
  /// [hardwareVersion] - The hardware version of the device
  /// [bootloaderVersion] - Optional bootloader version for compatibility check
  Future<Either<BluetoothFailure, OtaFirmwareModel?>> checkForUpdates({
    required String deviceType,
    required DateTime currentFirmwareDate,
    required String hardwareVersion,
    String? bootloaderVersion,
  });
}
