// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bluetooth_failure.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BluetoothFailure {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is BluetoothFailure);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure()';
  }
}

/// @nodoc
class $BluetoothFailureCopyWith<$Res> {
  $BluetoothFailureCopyWith(
      BluetoothFailure _, $Res Function(BluetoothFailure) __);
}

/// @nodoc

class BluetoothDisabled implements BluetoothFailure {
  const BluetoothDisabled();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is BluetoothDisabled);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.bluetoothDisabled()';
  }
}

/// @nodoc

class DeviceConnectionFailed implements BluetoothFailure {
  const DeviceConnectionFailed();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeviceConnectionFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.deviceConnectionFailed()';
  }
}

/// @nodoc

class NoDevicesFound implements BluetoothFailure {
  const NoDevicesFound();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is NoDevicesFound);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.noDevicesFound()';
  }
}

/// @nodoc

class SearchTimeout implements BluetoothFailure {
  const SearchTimeout();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SearchTimeout);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.searchTimeout()';
  }
}

/// @nodoc

class PermissionDenied implements BluetoothFailure {
  const PermissionDenied();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is PermissionDenied);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.permissionDenied()';
  }
}

/// @nodoc

class Unexpected implements BluetoothFailure {
  const Unexpected();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is Unexpected);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.unexpected()';
  }
}

/// @nodoc

class SaveDeviceFailed implements BluetoothFailure {
  const SaveDeviceFailed();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SaveDeviceFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.saveDeviceFailed()';
  }
}

/// @nodoc

class GetSavedDeviceFailed implements BluetoothFailure {
  const GetSavedDeviceFailed();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is GetSavedDeviceFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.getSavedDeviceFailed()';
  }
}

/// @nodoc

class SendCommandFailed implements BluetoothFailure {
  const SendCommandFailed();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is SendCommandFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.sendCommandFailed()';
  }
}

/// @nodoc

class GetCommandFailed implements BluetoothFailure {
  const GetCommandFailed();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is GetCommandFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.getCommandFailed()';
  }
}

/// @nodoc

class ReconnectFailed implements BluetoothFailure {
  const ReconnectFailed();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is ReconnectFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.reconnectFailed()';
  }
}

/// @nodoc

class DisconnectionFailed implements BluetoothFailure {
  const DisconnectionFailed();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DisconnectionFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.disconnectionFailed()';
  }
}

/// @nodoc

class DeviceUnpairFailed implements BluetoothFailure {
  const DeviceUnpairFailed();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeviceUnpairFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.deviceUnpairFailed()';
  }
}

/// @nodoc

class DeviceConnectionLost implements BluetoothFailure {
  const DeviceConnectionLost();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is DeviceConnectionLost);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.deviceConnectionLost()';
  }
}

/// @nodoc

class StopScanningFailed implements BluetoothFailure {
  const StopScanningFailed();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is StopScanningFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.stopScanningFailed()';
  }
}

/// @nodoc

class OtaUpdateFailed implements BluetoothFailure {
  const OtaUpdateFailed();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OtaUpdateFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.otaUpdateFailed()';
  }
}

/// @nodoc

class OtaFileNotFound implements BluetoothFailure {
  const OtaFileNotFound();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OtaFileNotFound);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.otaFileNotFound()';
  }
}

/// @nodoc

class OtaInvalidFirmware implements BluetoothFailure {
  const OtaInvalidFirmware();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OtaInvalidFirmware);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.otaInvalidFirmware()';
  }
}

/// @nodoc

class OtaTransferFailed implements BluetoothFailure {
  const OtaTransferFailed();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OtaTransferFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.otaTransferFailed()';
  }
}

/// @nodoc

class OtaVerificationFailed implements BluetoothFailure {
  const OtaVerificationFailed();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OtaVerificationFailed);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.otaVerificationFailed()';
  }
}

/// @nodoc

class OtaInsufficientSpace implements BluetoothFailure {
  const OtaInsufficientSpace();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OtaInsufficientSpace);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.otaInsufficientSpace()';
  }
}

/// @nodoc

class OtaDeviceNotSupported implements BluetoothFailure {
  const OtaDeviceNotSupported();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OtaDeviceNotSupported);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'BluetoothFailure.otaDeviceNotSupported()';
  }
}

// dart format on
