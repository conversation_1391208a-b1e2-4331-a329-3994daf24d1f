import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:fpdart/fpdart.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';

import '../domain/facade/ota_firmware_facade.dart';
import '../domain/failure/bluetooth_failure.dart';
import '../domain/model/ota_firmware_model.dart';

/// Implementation of OTA firmware facade with Firebase integration
class OtaFirmwareFacadeImpl implements IOtaFirmwareFacade {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;

  // Collection name for OTA firmware documents
  static const String _collectionName = 'ota_firmwares';

  // Local cache directory name
  static const String _cacheDirectoryName = 'ota_firmware_cache';

  OtaFirmwareFacadeImpl({
    FirebaseFirestore? firestore,
    FirebaseStorage? storage,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _storage = storage ?? FirebaseStorage.instance;

  @override
  Future<Either<BluetoothFailure, OtaFirmwareModel?>> getLatestFirmware({
    required String deviceType,
    bool includeUnstable = false,
  }) async {
    try {
      print(
          '🔍 [OTA FIRMWARE] Getting latest firmware for device type: $deviceType');

      Query query = _firestore
          .collection(_collectionName)
          .where('deviceType', isEqualTo: deviceType)
          .orderBy('createdAt', descending: true)
          .limit(1);

      if (!includeUnstable) {
        query = query.where('isStable', isEqualTo: true);
      }

      final querySnapshot = await query.get();

      if (querySnapshot.docs.isEmpty) {
        print(
            'ℹ️ [OTA FIRMWARE] No firmware found for device type: $deviceType');
        return const Right(null);
      }

      final doc = querySnapshot.docs.first;
      final firmware =
          OtaFirmwareModel.fromJson(doc.data() as Map<String, dynamic>);

      print(
          '✅ [OTA FIRMWARE] Found latest firmware: ${firmware.version} (${firmware.createdAt})');
      return Right(firmware);
    } catch (e) {
      print('❌ [OTA FIRMWARE] Error getting latest firmware: $e');
      return const Left(BluetoothFailure.otaUpdateFailed());
    }
  }

  @override
  Future<Either<BluetoothFailure, List<OtaFirmwareModel>>> getAllFirmware({
    required String deviceType,
    bool includeUnstable = false,
    int limit = 10,
  }) async {
    try {
      print(
          '🔍 [OTA FIRMWARE] Getting all firmware for device type: $deviceType (limit: $limit)');

      Query query = _firestore
          .collection(_collectionName)
          .where('deviceType', isEqualTo: deviceType)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (!includeUnstable) {
        query = query.where('isStable', isEqualTo: true);
      }

      final querySnapshot = await query.get();

      final firmwareList = querySnapshot.docs
          .map((doc) =>
              OtaFirmwareModel.fromJson(doc.data() as Map<String, dynamic>))
          .toList();

      print('✅ [OTA FIRMWARE] Found ${firmwareList.length} firmware versions');
      return Right(firmwareList);
    } catch (e) {
      print('❌ [OTA FIRMWARE] Error getting all firmware: $e');
      return const Left(BluetoothFailure.otaUpdateFailed());
    }
  }

  @override
  Future<Either<BluetoothFailure, OtaFirmwareModel?>> getFirmwareById(
    String firmwareId,
  ) async {
    try {
      print('🔍 [OTA FIRMWARE] Getting firmware by ID: $firmwareId');

      final doc =
          await _firestore.collection(_collectionName).doc(firmwareId).get();

      if (!doc.exists) {
        print('ℹ️ [OTA FIRMWARE] Firmware not found: $firmwareId');
        return const Right(null);
      }

      final firmware = OtaFirmwareModel.fromJson(doc.data()!);
      print('✅ [OTA FIRMWARE] Found firmware: ${firmware.version}');
      return Right(firmware);
    } catch (e) {
      print('❌ [OTA FIRMWARE] Error getting firmware by ID: $e');
      return const Left(BluetoothFailure.otaUpdateFailed());
    }
  }

  @override
  Future<Either<BluetoothFailure, Uint8List>> downloadFirmware(
    OtaFirmwareModel firmware, {
    void Function(double progress)? onProgress,
  }) async {
    try {
      print('🔍 [OTA FIRMWARE] Downloading firmware: ${firmware.firmwareId}');

      // Check if firmware is cached first
      final cachedResult = await getCachedFirmware(firmware);

      // Handle cached result using mapBoth pattern
      Uint8List? cachedData;
      cachedResult.mapBoth(
        onLeft: (failure) => cachedData = null,
        onRight: (data) => cachedData = data,
      );

      if (cachedData != null) {
        print('✅ [OTA FIRMWARE] Using cached firmware');
        return Right(cachedData!);
      }

      // Download from Firebase Storage
      print(
          '📥 [OTA FIRMWARE] Downloading from Firebase Storage: ${firmware.downloadUrl}');

      final response = await http.get(Uri.parse(firmware.downloadUrl));

      if (response.statusCode != 200) {
        print(
            '❌ [OTA FIRMWARE] Download failed with status: ${response.statusCode}');
        return const Left(BluetoothFailure.otaFileNotFound());
      }

      final data = response.bodyBytes;
      print('✅ [OTA FIRMWARE] Downloaded ${data.length} bytes');

      // Verify integrity
      final verifyResult = await verifyFirmwareIntegrity(firmware, data);

      // Handle verification result using mapBoth pattern
      bool isValid = false;
      verifyResult.mapBoth(
        onLeft: (failure) {
          print(
              '❌ [OTA FIRMWARE] Firmware integrity verification failed: $failure');
          isValid = false;
        },
        onRight: (valid) => isValid = valid,
      );

      if (!isValid) {
        print('❌ [OTA FIRMWARE] Firmware integrity verification failed');
        return const Left(BluetoothFailure.otaInvalidFirmware());
      }

      // Cache the firmware
      await cacheFirmware(firmware, data);

      return Right(data);
    } catch (e) {
      print('❌ [OTA FIRMWARE] Error downloading firmware: $e');
      return const Left(BluetoothFailure.otaFileNotFound());
    }
  }

  @override
  Future<bool> isFirmwareCached(OtaFirmwareModel firmware) async {
    try {
      final cacheDir = await _getCacheDirectory();
      final file = File('${cacheDir.path}/${firmware.firmwareId}.zip');
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<Either<BluetoothFailure, Uint8List?>> getCachedFirmware(
    OtaFirmwareModel firmware,
  ) async {
    try {
      final cacheDir = await _getCacheDirectory();
      final file = File('${cacheDir.path}/${firmware.firmwareId}.zip');

      if (!await file.exists()) {
        return const Right(null);
      }

      final data = await file.readAsBytes();
      print('✅ [OTA FIRMWARE] Retrieved cached firmware: ${data.length} bytes');
      return Right(data);
    } catch (e) {
      print('❌ [OTA FIRMWARE] Error getting cached firmware: $e');
      return const Left(BluetoothFailure.otaFileNotFound());
    }
  }

  @override
  Future<Either<BluetoothFailure, Unit>> cacheFirmware(
    OtaFirmwareModel firmware,
    Uint8List data,
  ) async {
    try {
      final cacheDir = await _getCacheDirectory();
      final file = File('${cacheDir.path}/${firmware.firmwareId}.zip');

      await file.writeAsBytes(data);
      print('✅ [OTA FIRMWARE] Cached firmware: ${firmware.firmwareId}');
      return const Right(unit);
    } catch (e) {
      print('❌ [OTA FIRMWARE] Error caching firmware: $e');
      return const Left(BluetoothFailure.otaUpdateFailed());
    }
  }

  @override
  Future<Either<BluetoothFailure, Unit>> clearCache({
    Duration? olderThan,
    String? deviceType,
  }) async {
    try {
      final cacheDir = await _getCacheDirectory();

      if (!await cacheDir.exists()) {
        return const Right(unit);
      }

      final files = await cacheDir.list().toList();
      final now = DateTime.now();

      for (final file in files) {
        if (file is File) {
          bool shouldDelete = true;

          if (olderThan != null) {
            final stat = await file.stat();
            final age = now.difference(stat.modified);
            shouldDelete = age > olderThan;
          }

          if (shouldDelete) {
            await file.delete();
            print('🗑️ [OTA FIRMWARE] Deleted cached file: ${file.path}');
          }
        }
      }

      return const Right(unit);
    } catch (e) {
      print('❌ [OTA FIRMWARE] Error clearing cache: $e');
      return const Left(BluetoothFailure.otaUpdateFailed());
    }
  }

  @override
  Future<Either<BluetoothFailure, bool>> verifyFirmwareIntegrity(
    OtaFirmwareModel firmware,
    Uint8List data,
  ) async {
    try {
      // Calculate SHA-256 checksum
      final digest = sha256.convert(data);
      final calculatedChecksum = 'sha256:${digest.toString()}';

      final isValid = calculatedChecksum == firmware.checksum;
      print(
          '🔍 [OTA FIRMWARE] Integrity check: $isValid (expected: ${firmware.checksum}, got: $calculatedChecksum)');

      return Right(isValid);
    } catch (e) {
      print('❌ [OTA FIRMWARE] Error verifying firmware integrity: $e');
      return const Left(BluetoothFailure.otaInvalidFirmware());
    }
  }

  @override
  Future<Either<BluetoothFailure, OtaFirmwareModel?>> checkForUpdates({
    required String deviceType,
    required DateTime currentFirmwareDate,
    required String hardwareVersion,
    String? bootloaderVersion,
  }) async {
    try {
      print('🔍 [OTA FIRMWARE] Checking for updates...');
      print('   Device Type: $deviceType');
      print('   Current Firmware Date: $currentFirmwareDate');
      print('   Hardware Version: $hardwareVersion');

      final latestResult = await getLatestFirmware(deviceType: deviceType);

      // Handle the result using mapBoth pattern
      BluetoothFailure? failure;
      OtaFirmwareModel? latestFirmware;

      latestResult.mapBoth(
        onLeft: (f) => failure = f,
        onRight: (firmware) => latestFirmware = firmware,
      );

      if (failure != null) {
        return Left(failure!);
      }

      if (latestFirmware == null) {
        print(
            'ℹ️ [OTA FIRMWARE] No firmware available for device type: $deviceType');
        return const Right(null);
      }

      // Check if latest firmware is newer than current
      if (latestFirmware!.createdAt.isAfter(currentFirmwareDate)) {
        // Check compatibility
        if (latestFirmware!.isCompatibleWith(
          deviceType: deviceType,
          hardwareVersion: hardwareVersion,
          bootloaderVersion: bootloaderVersion,
        )) {
          print(
              '✅ [OTA FIRMWARE] Update available: ${latestFirmware!.version}');
          return Right(latestFirmware!);
        } else {
          print(
              '⚠️ [OTA FIRMWARE] Latest firmware is not compatible with device');
          return const Right(null);
        }
      } else {
        print('ℹ️ [OTA FIRMWARE] Device firmware is up to date');
        return const Right(null);
      }
    } catch (e) {
      print('❌ [OTA FIRMWARE] Error checking for updates: $e');
      return const Left(BluetoothFailure.otaUpdateFailed());
    }
  }

  /// Get the cache directory for firmware files
  Future<Directory> _getCacheDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final cacheDir = Directory('${appDir.path}/$_cacheDirectoryName');

    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }

    return cacheDir;
  }
}
