import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:injectable/injectable.dart';

import '../domain/facade/ota_firmware_facade.dart';
import '../infrastructure/ota_firmware_facade_impl.dart';

/// Injectable module for OTA firmware dependencies
@module
abstract class OtaInjectableModule {
  /// Provide FirebaseStorage instance (FirebaseFirestore is already registered in main app)
  @lazySingleton
  FirebaseStorage get storage => FirebaseStorage.instance;

  /// Provide OTA firmware facade implementation
  /// Uses the already registered FirebaseFirestore instance from main app
  @LazySingleton(as: IOtaFirmwareFacade)
  OtaFirmwareFacadeImpl otaFirmwareFacade(
    FirebaseFirestore
        firestore, // This will use the already registered instance
    FirebaseStorage storage,
  ) =>
      OtaFirmwareFacadeImpl(
        firestore: firestore,
        storage: storage,
      );
}
