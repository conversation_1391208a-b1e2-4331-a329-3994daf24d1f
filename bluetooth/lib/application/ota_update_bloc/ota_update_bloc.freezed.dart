// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ota_update_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OtaUpdateEvent {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OtaUpdateEvent);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateEvent()';
  }
}

/// @nodoc
class $OtaUpdateEventCopyWith<$Res> {
  $OtaUpdateEventCopyWith(OtaUpdateEvent _, $Res Function(OtaUpdateEvent) __);
}

/// @nodoc

class CheckDeviceFirmware implements OtaUpdateEvent {
  const CheckDeviceFirmware(this.device);

  final BluetoothDevice device;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CheckDeviceFirmwareCopyWith<CheckDeviceFirmware> get copyWith =>
      _$CheckDeviceFirmwareCopyWithImpl<CheckDeviceFirmware>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CheckDeviceFirmware &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'OtaUpdateEvent.checkDeviceFirmware(device: $device)';
  }
}

/// @nodoc
abstract mixin class $CheckDeviceFirmwareCopyWith<$Res>
    implements $OtaUpdateEventCopyWith<$Res> {
  factory $CheckDeviceFirmwareCopyWith(
          CheckDeviceFirmware value, $Res Function(CheckDeviceFirmware) _then) =
      _$CheckDeviceFirmwareCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$CheckDeviceFirmwareCopyWithImpl<$Res>
    implements $CheckDeviceFirmwareCopyWith<$Res> {
  _$CheckDeviceFirmwareCopyWithImpl(this._self, this._then);

  final CheckDeviceFirmware _self;
  final $Res Function(CheckDeviceFirmware) _then;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(CheckDeviceFirmware(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class CheckForUpdates implements OtaUpdateEvent {
  const CheckForUpdates(this.device);

  final BluetoothDevice device;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CheckForUpdatesCopyWith<CheckForUpdates> get copyWith =>
      _$CheckForUpdatesCopyWithImpl<CheckForUpdates>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CheckForUpdates &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'OtaUpdateEvent.checkForUpdates(device: $device)';
  }
}

/// @nodoc
abstract mixin class $CheckForUpdatesCopyWith<$Res>
    implements $OtaUpdateEventCopyWith<$Res> {
  factory $CheckForUpdatesCopyWith(
          CheckForUpdates value, $Res Function(CheckForUpdates) _then) =
      _$CheckForUpdatesCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$CheckForUpdatesCopyWithImpl<$Res>
    implements $CheckForUpdatesCopyWith<$Res> {
  _$CheckForUpdatesCopyWithImpl(this._self, this._then);

  final CheckForUpdates _self;
  final $Res Function(CheckForUpdates) _then;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(CheckForUpdates(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class StartOtaUpdate implements OtaUpdateEvent {
  const StartOtaUpdate(this.device, this.firmwareData);

  final BluetoothDevice device;
  final Uint8List firmwareData;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $StartOtaUpdateCopyWith<StartOtaUpdate> get copyWith =>
      _$StartOtaUpdateCopyWithImpl<StartOtaUpdate>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is StartOtaUpdate &&
            (identical(other.device, device) || other.device == device) &&
            const DeepCollectionEquality()
                .equals(other.firmwareData, firmwareData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, device, const DeepCollectionEquality().hash(firmwareData));

  @override
  String toString() {
    return 'OtaUpdateEvent.startOtaUpdate(device: $device, firmwareData: $firmwareData)';
  }
}

/// @nodoc
abstract mixin class $StartOtaUpdateCopyWith<$Res>
    implements $OtaUpdateEventCopyWith<$Res> {
  factory $StartOtaUpdateCopyWith(
          StartOtaUpdate value, $Res Function(StartOtaUpdate) _then) =
      _$StartOtaUpdateCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device, Uint8List firmwareData});
}

/// @nodoc
class _$StartOtaUpdateCopyWithImpl<$Res>
    implements $StartOtaUpdateCopyWith<$Res> {
  _$StartOtaUpdateCopyWithImpl(this._self, this._then);

  final StartOtaUpdate _self;
  final $Res Function(StartOtaUpdate) _then;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
    Object? firmwareData = null,
  }) {
    return _then(StartOtaUpdate(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
      null == firmwareData
          ? _self.firmwareData
          : firmwareData // ignore: cast_nullable_to_non_nullable
              as Uint8List,
    ));
  }
}

/// @nodoc

class CancelOtaUpdate implements OtaUpdateEvent {
  const CancelOtaUpdate(this.device);

  final BluetoothDevice device;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CancelOtaUpdateCopyWith<CancelOtaUpdate> get copyWith =>
      _$CancelOtaUpdateCopyWithImpl<CancelOtaUpdate>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CancelOtaUpdate &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'OtaUpdateEvent.cancelOtaUpdate(device: $device)';
  }
}

/// @nodoc
abstract mixin class $CancelOtaUpdateCopyWith<$Res>
    implements $OtaUpdateEventCopyWith<$Res> {
  factory $CancelOtaUpdateCopyWith(
          CancelOtaUpdate value, $Res Function(CancelOtaUpdate) _then) =
      _$CancelOtaUpdateCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$CancelOtaUpdateCopyWithImpl<$Res>
    implements $CancelOtaUpdateCopyWith<$Res> {
  _$CancelOtaUpdateCopyWithImpl(this._self, this._then);

  final CancelOtaUpdate _self;
  final $Res Function(CancelOtaUpdate) _then;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(CancelOtaUpdate(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class LoadUpdateHistory implements OtaUpdateEvent {
  const LoadUpdateHistory();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is LoadUpdateHistory);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateEvent.loadUpdateHistory()';
  }
}

/// @nodoc

class VerifyFirmwareFile implements OtaUpdateEvent {
  const VerifyFirmwareFile(this.device, this.firmwareData);

  final BluetoothDevice device;
  final Uint8List firmwareData;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $VerifyFirmwareFileCopyWith<VerifyFirmwareFile> get copyWith =>
      _$VerifyFirmwareFileCopyWithImpl<VerifyFirmwareFile>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is VerifyFirmwareFile &&
            (identical(other.device, device) || other.device == device) &&
            const DeepCollectionEquality()
                .equals(other.firmwareData, firmwareData));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, device, const DeepCollectionEquality().hash(firmwareData));

  @override
  String toString() {
    return 'OtaUpdateEvent.verifyFirmwareFile(device: $device, firmwareData: $firmwareData)';
  }
}

/// @nodoc
abstract mixin class $VerifyFirmwareFileCopyWith<$Res>
    implements $OtaUpdateEventCopyWith<$Res> {
  factory $VerifyFirmwareFileCopyWith(
          VerifyFirmwareFile value, $Res Function(VerifyFirmwareFile) _then) =
      _$VerifyFirmwareFileCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device, Uint8List firmwareData});
}

/// @nodoc
class _$VerifyFirmwareFileCopyWithImpl<$Res>
    implements $VerifyFirmwareFileCopyWith<$Res> {
  _$VerifyFirmwareFileCopyWithImpl(this._self, this._then);

  final VerifyFirmwareFile _self;
  final $Res Function(VerifyFirmwareFile) _then;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
    Object? firmwareData = null,
  }) {
    return _then(VerifyFirmwareFile(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
      null == firmwareData
          ? _self.firmwareData
          : firmwareData // ignore: cast_nullable_to_non_nullable
              as Uint8List,
    ));
  }
}

/// @nodoc

class PrepareDeviceForOta implements OtaUpdateEvent {
  const PrepareDeviceForOta(this.device);

  final BluetoothDevice device;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PrepareDeviceForOtaCopyWith<PrepareDeviceForOta> get copyWith =>
      _$PrepareDeviceForOtaCopyWithImpl<PrepareDeviceForOta>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PrepareDeviceForOta &&
            (identical(other.device, device) || other.device == device));
  }

  @override
  int get hashCode => Object.hash(runtimeType, device);

  @override
  String toString() {
    return 'OtaUpdateEvent.prepareDeviceForOta(device: $device)';
  }
}

/// @nodoc
abstract mixin class $PrepareDeviceForOtaCopyWith<$Res>
    implements $OtaUpdateEventCopyWith<$Res> {
  factory $PrepareDeviceForOtaCopyWith(
          PrepareDeviceForOta value, $Res Function(PrepareDeviceForOta) _then) =
      _$PrepareDeviceForOtaCopyWithImpl;
  @useResult
  $Res call({BluetoothDevice device});
}

/// @nodoc
class _$PrepareDeviceForOtaCopyWithImpl<$Res>
    implements $PrepareDeviceForOtaCopyWith<$Res> {
  _$PrepareDeviceForOtaCopyWithImpl(this._self, this._then);

  final PrepareDeviceForOta _self;
  final $Res Function(PrepareDeviceForOta) _then;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? device = null,
  }) {
    return _then(PrepareDeviceForOta(
      null == device
          ? _self.device
          : device // ignore: cast_nullable_to_non_nullable
              as BluetoothDevice,
    ));
  }
}

/// @nodoc

class OtaProgressReceived implements OtaUpdateEvent {
  const OtaProgressReceived(this.progress);

  final OtaProgress progress;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OtaProgressReceivedCopyWith<OtaProgressReceived> get copyWith =>
      _$OtaProgressReceivedCopyWithImpl<OtaProgressReceived>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OtaProgressReceived &&
            (identical(other.progress, progress) ||
                other.progress == progress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, progress);

  @override
  String toString() {
    return 'OtaUpdateEvent.otaProgressReceived(progress: $progress)';
  }
}

/// @nodoc
abstract mixin class $OtaProgressReceivedCopyWith<$Res>
    implements $OtaUpdateEventCopyWith<$Res> {
  factory $OtaProgressReceivedCopyWith(
          OtaProgressReceived value, $Res Function(OtaProgressReceived) _then) =
      _$OtaProgressReceivedCopyWithImpl;
  @useResult
  $Res call({OtaProgress progress});
}

/// @nodoc
class _$OtaProgressReceivedCopyWithImpl<$Res>
    implements $OtaProgressReceivedCopyWith<$Res> {
  _$OtaProgressReceivedCopyWithImpl(this._self, this._then);

  final OtaProgressReceived _self;
  final $Res Function(OtaProgressReceived) _then;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? progress = null,
  }) {
    return _then(OtaProgressReceived(
      null == progress
          ? _self.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as OtaProgress,
    ));
  }
}

/// @nodoc

class OtaErrorReceived implements OtaUpdateEvent {
  const OtaErrorReceived(this.failure);

  final BluetoothFailure failure;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OtaErrorReceivedCopyWith<OtaErrorReceived> get copyWith =>
      _$OtaErrorReceivedCopyWithImpl<OtaErrorReceived>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OtaErrorReceived &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'OtaUpdateEvent.otaErrorReceived(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class $OtaErrorReceivedCopyWith<$Res>
    implements $OtaUpdateEventCopyWith<$Res> {
  factory $OtaErrorReceivedCopyWith(
          OtaErrorReceived value, $Res Function(OtaErrorReceived) _then) =
      _$OtaErrorReceivedCopyWithImpl;
  @useResult
  $Res call({BluetoothFailure failure});

  $BluetoothFailureCopyWith<$Res> get failure;
}

/// @nodoc
class _$OtaErrorReceivedCopyWithImpl<$Res>
    implements $OtaErrorReceivedCopyWith<$Res> {
  _$OtaErrorReceivedCopyWithImpl(this._self, this._then);

  final OtaErrorReceived _self;
  final $Res Function(OtaErrorReceived) _then;

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(OtaErrorReceived(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as BluetoothFailure,
    ));
  }

  /// Create a copy of OtaUpdateEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BluetoothFailureCopyWith<$Res> get failure {
    return $BluetoothFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

/// @nodoc
mixin _$OtaUpdateState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is OtaUpdateState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState()';
  }
}

/// @nodoc
class $OtaUpdateStateCopyWith<$Res> {
  $OtaUpdateStateCopyWith(OtaUpdateState _, $Res Function(OtaUpdateState) __);
}

/// @nodoc

class _Initial implements OtaUpdateState {
  const _Initial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _Initial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState.initial()';
  }
}

/// @nodoc

class _CheckingFirmware implements OtaUpdateState {
  const _CheckingFirmware();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _CheckingFirmware);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState.checkingFirmware()';
  }
}

/// @nodoc

class _FirmwareInfoLoaded implements OtaUpdateState {
  const _FirmwareInfoLoaded(this.firmwareInfo);

  final DeviceFirmwareInfo firmwareInfo;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FirmwareInfoLoadedCopyWith<_FirmwareInfoLoaded> get copyWith =>
      __$FirmwareInfoLoadedCopyWithImpl<_FirmwareInfoLoaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FirmwareInfoLoaded &&
            (identical(other.firmwareInfo, firmwareInfo) ||
                other.firmwareInfo == firmwareInfo));
  }

  @override
  int get hashCode => Object.hash(runtimeType, firmwareInfo);

  @override
  String toString() {
    return 'OtaUpdateState.firmwareInfoLoaded(firmwareInfo: $firmwareInfo)';
  }
}

/// @nodoc
abstract mixin class _$FirmwareInfoLoadedCopyWith<$Res>
    implements $OtaUpdateStateCopyWith<$Res> {
  factory _$FirmwareInfoLoadedCopyWith(
          _FirmwareInfoLoaded value, $Res Function(_FirmwareInfoLoaded) _then) =
      __$FirmwareInfoLoadedCopyWithImpl;
  @useResult
  $Res call({DeviceFirmwareInfo firmwareInfo});
}

/// @nodoc
class __$FirmwareInfoLoadedCopyWithImpl<$Res>
    implements _$FirmwareInfoLoadedCopyWith<$Res> {
  __$FirmwareInfoLoadedCopyWithImpl(this._self, this._then);

  final _FirmwareInfoLoaded _self;
  final $Res Function(_FirmwareInfoLoaded) _then;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? firmwareInfo = null,
  }) {
    return _then(_FirmwareInfoLoaded(
      null == firmwareInfo
          ? _self.firmwareInfo
          : firmwareInfo // ignore: cast_nullable_to_non_nullable
              as DeviceFirmwareInfo,
    ));
  }
}

/// @nodoc

class _CheckingForUpdates implements OtaUpdateState {
  const _CheckingForUpdates();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _CheckingForUpdates);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState.checkingForUpdates()';
  }
}

/// @nodoc

class _UpdateAvailable implements OtaUpdateState {
  const _UpdateAvailable(this.updateInfo);

  final FirmwareInfo updateInfo;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdateAvailableCopyWith<_UpdateAvailable> get copyWith =>
      __$UpdateAvailableCopyWithImpl<_UpdateAvailable>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateAvailable &&
            (identical(other.updateInfo, updateInfo) ||
                other.updateInfo == updateInfo));
  }

  @override
  int get hashCode => Object.hash(runtimeType, updateInfo);

  @override
  String toString() {
    return 'OtaUpdateState.updateAvailable(updateInfo: $updateInfo)';
  }
}

/// @nodoc
abstract mixin class _$UpdateAvailableCopyWith<$Res>
    implements $OtaUpdateStateCopyWith<$Res> {
  factory _$UpdateAvailableCopyWith(
          _UpdateAvailable value, $Res Function(_UpdateAvailable) _then) =
      __$UpdateAvailableCopyWithImpl;
  @useResult
  $Res call({FirmwareInfo updateInfo});
}

/// @nodoc
class __$UpdateAvailableCopyWithImpl<$Res>
    implements _$UpdateAvailableCopyWith<$Res> {
  __$UpdateAvailableCopyWithImpl(this._self, this._then);

  final _UpdateAvailable _self;
  final $Res Function(_UpdateAvailable) _then;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? updateInfo = null,
  }) {
    return _then(_UpdateAvailable(
      null == updateInfo
          ? _self.updateInfo
          : updateInfo // ignore: cast_nullable_to_non_nullable
              as FirmwareInfo,
    ));
  }
}

/// @nodoc

class _NoUpdatesAvailable implements OtaUpdateState {
  const _NoUpdatesAvailable();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _NoUpdatesAvailable);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState.noUpdatesAvailable()';
  }
}

/// @nodoc

class _VerifyingFirmware implements OtaUpdateState {
  const _VerifyingFirmware();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _VerifyingFirmware);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState.verifyingFirmware()';
  }
}

/// @nodoc

class _FirmwareVerified implements OtaUpdateState {
  const _FirmwareVerified();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _FirmwareVerified);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState.firmwareVerified()';
  }
}

/// @nodoc

class _PreparingDevice implements OtaUpdateState {
  const _PreparingDevice();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _PreparingDevice);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState.preparingDevice()';
  }
}

/// @nodoc

class _DevicePrepared implements OtaUpdateState {
  const _DevicePrepared();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _DevicePrepared);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState.devicePrepared()';
  }
}

/// @nodoc

class _PreparingUpdate implements OtaUpdateState {
  const _PreparingUpdate();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _PreparingUpdate);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState.preparingUpdate()';
  }
}

/// @nodoc

class _UpdateInProgress implements OtaUpdateState {
  const _UpdateInProgress(this.progress);

  final OtaProgress progress;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdateInProgressCopyWith<_UpdateInProgress> get copyWith =>
      __$UpdateInProgressCopyWithImpl<_UpdateInProgress>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateInProgress &&
            (identical(other.progress, progress) ||
                other.progress == progress));
  }

  @override
  int get hashCode => Object.hash(runtimeType, progress);

  @override
  String toString() {
    return 'OtaUpdateState.updateInProgress(progress: $progress)';
  }
}

/// @nodoc
abstract mixin class _$UpdateInProgressCopyWith<$Res>
    implements $OtaUpdateStateCopyWith<$Res> {
  factory _$UpdateInProgressCopyWith(
          _UpdateInProgress value, $Res Function(_UpdateInProgress) _then) =
      __$UpdateInProgressCopyWithImpl;
  @useResult
  $Res call({OtaProgress progress});
}

/// @nodoc
class __$UpdateInProgressCopyWithImpl<$Res>
    implements _$UpdateInProgressCopyWith<$Res> {
  __$UpdateInProgressCopyWithImpl(this._self, this._then);

  final _UpdateInProgress _self;
  final $Res Function(_UpdateInProgress) _then;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? progress = null,
  }) {
    return _then(_UpdateInProgress(
      null == progress
          ? _self.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as OtaProgress,
    ));
  }
}

/// @nodoc

class _UpdateCompleted implements OtaUpdateState {
  const _UpdateCompleted();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _UpdateCompleted);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState.updateCompleted()';
  }
}

/// @nodoc

class _CancellingUpdate implements OtaUpdateState {
  const _CancellingUpdate();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _CancellingUpdate);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState.cancellingUpdate()';
  }
}

/// @nodoc

class _UpdateCancelled implements OtaUpdateState {
  const _UpdateCancelled();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _UpdateCancelled);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState.updateCancelled()';
  }
}

/// @nodoc

class _LoadingHistory implements OtaUpdateState {
  const _LoadingHistory();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _LoadingHistory);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'OtaUpdateState.loadingHistory()';
  }
}

/// @nodoc

class _HistoryLoaded implements OtaUpdateState {
  const _HistoryLoaded(final List<OtaUpdateRecord> history)
      : _history = history;

  final List<OtaUpdateRecord> _history;
  List<OtaUpdateRecord> get history {
    if (_history is EqualUnmodifiableListView) return _history;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_history);
  }

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$HistoryLoadedCopyWith<_HistoryLoaded> get copyWith =>
      __$HistoryLoadedCopyWithImpl<_HistoryLoaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _HistoryLoaded &&
            const DeepCollectionEquality().equals(other._history, _history));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_history));

  @override
  String toString() {
    return 'OtaUpdateState.historyLoaded(history: $history)';
  }
}

/// @nodoc
abstract mixin class _$HistoryLoadedCopyWith<$Res>
    implements $OtaUpdateStateCopyWith<$Res> {
  factory _$HistoryLoadedCopyWith(
          _HistoryLoaded value, $Res Function(_HistoryLoaded) _then) =
      __$HistoryLoadedCopyWithImpl;
  @useResult
  $Res call({List<OtaUpdateRecord> history});
}

/// @nodoc
class __$HistoryLoadedCopyWithImpl<$Res>
    implements _$HistoryLoadedCopyWith<$Res> {
  __$HistoryLoadedCopyWithImpl(this._self, this._then);

  final _HistoryLoaded _self;
  final $Res Function(_HistoryLoaded) _then;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? history = null,
  }) {
    return _then(_HistoryLoaded(
      null == history
          ? _self._history
          : history // ignore: cast_nullable_to_non_nullable
              as List<OtaUpdateRecord>,
    ));
  }
}

/// @nodoc

class _Error implements OtaUpdateState {
  const _Error(this.failure);

  final BluetoothFailure failure;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ErrorCopyWith<_Error> get copyWith =>
      __$ErrorCopyWithImpl<_Error>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Error &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  @override
  String toString() {
    return 'OtaUpdateState.error(failure: $failure)';
  }
}

/// @nodoc
abstract mixin class _$ErrorCopyWith<$Res>
    implements $OtaUpdateStateCopyWith<$Res> {
  factory _$ErrorCopyWith(_Error value, $Res Function(_Error) _then) =
      __$ErrorCopyWithImpl;
  @useResult
  $Res call({BluetoothFailure failure});

  $BluetoothFailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$ErrorCopyWithImpl<$Res> implements _$ErrorCopyWith<$Res> {
  __$ErrorCopyWithImpl(this._self, this._then);

  final _Error _self;
  final $Res Function(_Error) _then;

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? failure = null,
  }) {
    return _then(_Error(
      null == failure
          ? _self.failure
          : failure // ignore: cast_nullable_to_non_nullable
              as BluetoothFailure,
    ));
  }

  /// Create a copy of OtaUpdateState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BluetoothFailureCopyWith<$Res> get failure {
    return $BluetoothFailureCopyWith<$Res>(_self.failure, (value) {
      return _then(_self.copyWith(failure: value));
    });
  }
}

// dart format on
