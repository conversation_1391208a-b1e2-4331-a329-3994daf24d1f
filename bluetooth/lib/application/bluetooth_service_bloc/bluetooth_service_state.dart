// bluetooth_service_state.dart
part of 'bluetooth_service_bloc.dart';

@freezed
class BluetoothServiceState with _$BluetoothServiceState {
  // Initial state
  const factory BluetoothServiceState.initial() = Initial;

  // Bluetooth is enabled
  const factory BluetoothServiceState.bluetoothOn() = BluetoothOn;

  // Bluetooth is disabled
  const factory BluetoothServiceState.bluetoothOff() = BluetoothOff;

  // Checking for recent device
  const factory BluetoothServiceState.checkingRecentDevice() =
      CheckingRecentDevice;

  // Fetching saved devices
  const factory BluetoothServiceState.fetchingSavedDevices() =
      FetchingSavedDevices;

  // Currently searching for devices
  const factory BluetoothServiceState.bluetoothSearching() = BluetoothSearching;

  // Attempting to connect to device
  const factory BluetoothServiceState.connecting() = Connecting;

  //reconnecting to device
  const factory BluetoothServiceState.reconnecting(BluetoothDevice device) =
      Reconnecting;

  // List of saved devices
  const factory BluetoothServiceState.savedDevices(
    List<DeviceModel> devices, {
    @Default(0) int version,
  }) = SavedDevices;

  // List of available devices found during scanning
  const factory BluetoothServiceState.bluetoothAvailableTypeDevices(
      List<BluetoothDevice?>? devices) = BluetoothAvailableTypeDevices;

  // Connected to a device
  const factory BluetoothServiceState.connected(BluetoothDevice device) =
      Connected;

  // Disconnected from a device
  const factory BluetoothServiceState.disconnected(BluetoothDevice device) =
      Disconnected;

  // Unpairing a device
  const factory BluetoothServiceState.unpairing(BluetoothDevice device) =
      Unpairing;

  // Device unpaired successfully
  const factory BluetoothServiceState.deviceUnpaired(BluetoothDevice device) =
      DeviceUnpaired;

  // Error occurred
  const factory BluetoothServiceState.bluetoothError(BluetoothFailure failure) =
      BluetoothError;

  // Connected for the first time (intro screen)
  const factory BluetoothServiceState.connectionIntro(BluetoothDevice device) =
      ConnectionIntro;

  //landing page state
  const factory BluetoothServiceState.landingPageState() = LandingPageState;
}
