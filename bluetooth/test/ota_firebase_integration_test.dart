// import 'dart:typed_data';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:mocktail/mocktail.dart';
// import 'package:fpdart/fpdart.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:firebase_storage/firebase_storage.dart';
//
// import '../lib/domain/facade/ota_firmware_facade.dart';
// import '../lib/domain/model/ota_firmware_model.dart';
// import '../lib/domain/failure/bluetooth_failure.dart';
// import '../lib/infrastructure/ota_firmware_facade_impl.dart';
//
// // Mock classes
// class MockFirebaseFirestore extends Mock implements FirebaseFirestore {}
//
// class MockFirebaseStorage extends Mock implements FirebaseStorage {}
//
// class MockCollectionReference extends Mock
//     implements CollectionReference<Map<String, dynamic>> {}
//
// class MockQuery extends Mock implements Query<Map<String, dynamic>> {}
//
// class MockQuerySnapshot extends Mock
//     implements QuerySnapshot<Map<String, dynamic>> {}
//
// class MockDocumentSnapshot extends Mock
//     implements QueryDocumentSnapshot<Map<String, dynamic>> {}
//
// class MockDocumentReference extends Mock
//     implements DocumentReference<Map<String, dynamic>> {}
//
// void main() {
//   group('OTA Firebase Integration Tests', () {
//     late IOtaFirmwareFacade otaFirmwareFacade;
//     late MockFirebaseFirestore mockFirestore;
//     late MockFirebaseStorage mockStorage;
//     late MockCollectionReference mockCollection;
//     late MockQuery mockQuery;
//     late MockQuerySnapshot mockQuerySnapshot;
//     late MockDocumentSnapshot mockDocumentSnapshot;
//     late MockDocumentReference mockDocumentReference;
//
//     setUp(() {
//       mockFirestore = MockFirebaseFirestore();
//       mockStorage = MockFirebaseStorage();
//       mockCollection = MockCollectionReference();
//       mockQuery = MockQuery();
//       mockQuerySnapshot = MockQuerySnapshot();
//       mockDocumentSnapshot = MockDocumentSnapshot();
//       mockDocumentReference = MockDocumentReference();
//
//       otaFirmwareFacade = OtaFirmwareFacadeImpl(
//         firestore: mockFirestore,
//         storage: mockStorage,
//       );
//
//       // Setup default mocks
//       when(() => mockFirestore.collection('ota_firmwares'))
//           .thenReturn(mockCollection);
//     });
//
//     group('getLatestFirmware', () {
//       test('should return latest firmware when available', () async {
//         // Arrange
//         final sampleFirmware = OtaFirmwareModel(
//           firmwareId: 'test_firmware_1',
//           deviceType: 'juno_v1',
//           downloadUrl: 'https://example.com/firmware.zip',
//           version: '2.1.0',
//           createdAt: DateTime(2024, 12, 1),
//           fileSize: 524288,
//           checksum: 'sha256:test_checksum',
//           buildDate: '2024-12-01',
//           hardwareVersion: '1.0',
//           isStable: true,
//         );
//
//         when(() => mockCollection.where('deviceType', isEqualTo: 'juno_v1'))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.where('isStable', isEqualTo: true))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.orderBy('createdAt', descending: true))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.limit(1)).thenReturn(mockQuery);
//         when(() => mockQuery.get()).thenAnswer((_) async => mockQuerySnapshot);
//         when(() => mockQuerySnapshot.docs).thenReturn([mockDocumentSnapshot]);
//         when(() => mockDocumentSnapshot.data())
//             .thenReturn(sampleFirmware.toJson());
//
//         // Act
//         final result = await otaFirmwareFacade.getLatestFirmware(
//           deviceType: 'juno_v1',
//         );
//
//         // Assert
//         OtaFirmwareModel? firmware;
//         result.mapBoth(
//           onLeft: (failure) =>
//               fail('Expected success but got failure: $failure'),
//           onRight: (data) => firmware = data,
//         );
//
//         expect(firmware, isNotNull);
//         expect(firmware!.firmwareId, 'test_firmware_1');
//         expect(firmware!.version, '2.1.0');
//         expect(firmware!.deviceType, 'juno_v1');
//       });
//
//       test('should return null when no firmware available', () async {
//         // Arrange
//         when(() => mockCollection.where('deviceType', isEqualTo: 'juno_v1'))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.where('isStable', isEqualTo: true))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.orderBy('createdAt', descending: true))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.limit(1)).thenReturn(mockQuery);
//         when(() => mockQuery.get()).thenAnswer((_) async => mockQuerySnapshot);
//         when(() => mockQuerySnapshot.docs).thenReturn([]);
//
//         // Act
//         final result = await otaFirmwareFacade.getLatestFirmware(
//           deviceType: 'juno_v1',
//         );
//
//         // Assert
//         expect(result.isRight(), true);
//         final firmware = result.getOrElse((l) => null);
//         expect(firmware, isNull);
//       });
//
//       test('should return failure when Firestore query fails', () async {
//         // Arrange
//         when(() => mockCollection.where('deviceType', isEqualTo: 'juno_v1'))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.where('isStable', isEqualTo: true))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.orderBy('createdAt', descending: true))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.limit(1)).thenReturn(mockQuery);
//         when(() => mockQuery.get()).thenThrow(Exception('Firestore error'));
//
//         // Act
//         final result = await otaFirmwareFacade.getLatestFirmware(
//           deviceType: 'juno_v1',
//         );
//
//         // Assert
//         expect(result.isLeft(), true);
//         expect(
//             result
//                 .getLeft()
//                 .getOrElse(() => const BluetoothFailure.otaUpdateFailed()),
//             isA<BluetoothFailure>());
//       });
//     });
//
//     group('checkForUpdates', () {
//       test('should return update when newer firmware is available', () async {
//         // Arrange
//         final currentDate = DateTime(2024, 11, 1);
//         final newerDate = DateTime(2024, 12, 1);
//
//         final newerFirmware = OtaFirmwareModel(
//           firmwareId: 'newer_firmware',
//           deviceType: 'juno_v1',
//           downloadUrl: 'https://example.com/newer_firmware.zip',
//           version: '2.1.0',
//           createdAt: newerDate,
//           fileSize: 524288,
//           checksum: 'sha256:newer_checksum',
//           buildDate: '2024-12-01',
//           hardwareVersion: '1.0',
//           isStable: true,
//         );
//
//         when(() => mockCollection.where('deviceType', isEqualTo: 'juno_v1'))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.where('isStable', isEqualTo: true))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.orderBy('createdAt', descending: true))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.limit(1)).thenReturn(mockQuery);
//         when(() => mockQuery.get()).thenAnswer((_) async => mockQuerySnapshot);
//         when(() => mockQuerySnapshot.docs).thenReturn([mockDocumentSnapshot]);
//         when(() => mockDocumentSnapshot.data())
//             .thenReturn(newerFirmware.toJson());
//
//         // Act
//         final result = await otaFirmwareFacade.checkForUpdates(
//           deviceType: 'juno_v1',
//           currentFirmwareDate: currentDate,
//           hardwareVersion: '1.0',
//         );
//
//         // Assert
//         expect(result.isRight(), true);
//         final update = result.getRight().getOrElse(() => null);
//         expect(update, isNotNull);
//         expect(update!.version, '2.1.0');
//         expect(update.createdAt.isAfter(currentDate), true);
//       });
//
//       test('should return null when no newer firmware is available', () async {
//         // Arrange
//         final currentDate = DateTime(2024, 12, 1);
//         final olderDate = DateTime(2024, 11, 1);
//
//         final olderFirmware = OtaFirmwareModel(
//           firmwareId: 'older_firmware',
//           deviceType: 'juno_v1',
//           downloadUrl: 'https://example.com/older_firmware.zip',
//           version: '2.0.0',
//           createdAt: olderDate,
//           fileSize: 524288,
//           checksum: 'sha256:older_checksum',
//           buildDate: '2024-11-01',
//           hardwareVersion: '1.0',
//           isStable: true,
//         );
//
//         when(() => mockCollection.where('deviceType', isEqualTo: 'juno_v1'))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.where('isStable', isEqualTo: true))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.orderBy('createdAt', descending: true))
//             .thenReturn(mockQuery);
//         when(() => mockQuery.limit(1)).thenReturn(mockQuery);
//         when(() => mockQuery.get()).thenAnswer((_) async => mockQuerySnapshot);
//         when(() => mockQuerySnapshot.docs).thenReturn([mockDocumentSnapshot]);
//         when(() => mockDocumentSnapshot.data())
//             .thenReturn(olderFirmware.toJson());
//
//         // Act
//         final result = await otaFirmwareFacade.checkForUpdates(
//           deviceType: 'juno_v1',
//           currentFirmwareDate: currentDate,
//           hardwareVersion: '1.0',
//         );
//
//         // Assert
//         expect(result.isRight(), true);
//         final update = result.getRight().getOrElse(() => null);
//         expect(update, isNull);
//       });
//     });
//
//     group('OtaFirmwareModel', () {
//       test('should serialize and deserialize correctly', () {
//         // Arrange
//         final firmware = OtaFirmwareModel(
//           firmwareId: 'test_firmware',
//           deviceType: 'juno_v1',
//           downloadUrl: 'https://example.com/firmware.zip',
//           version: '2.1.0',
//           createdAt: DateTime(2024, 12, 1, 10, 30),
//           fileSize: 524288,
//           checksum: 'sha256:test_checksum',
//           buildDate: '2024-12-01',
//           hardwareVersion: '1.0',
//           isStable: true,
//           releaseNotes: 'Test release',
//           minBootloaderVersion: '1.0.0',
//           metadata: {'buildNumber': '12345'},
//         );
//
//         // Act
//         final json = firmware.toJson();
//         final deserialized = OtaFirmwareModel.fromJson(json);
//
//         // Assert
//         expect(deserialized.firmwareId, firmware.firmwareId);
//         expect(deserialized.deviceType, firmware.deviceType);
//         expect(deserialized.version, firmware.version);
//         expect(deserialized.fileSize, firmware.fileSize);
//         expect(deserialized.isStable, firmware.isStable);
//         expect(deserialized.releaseNotes, firmware.releaseNotes);
//         expect(deserialized.metadata['buildNumber'], '12345');
//       });
//
//       test('should correctly compare firmware dates', () {
//         // Arrange
//         final olderFirmware = OtaFirmwareModel(
//           firmwareId: 'older',
//           deviceType: 'juno_v1',
//           downloadUrl: 'https://example.com/older.zip',
//           version: '2.0.0',
//           createdAt: DateTime(2024, 11, 1),
//           fileSize: 524288,
//           checksum: 'sha256:older',
//           buildDate: '2024-11-01',
//           hardwareVersion: '1.0',
//         );
//
//         final newerFirmware = OtaFirmwareModel(
//           firmwareId: 'newer',
//           deviceType: 'juno_v1',
//           downloadUrl: 'https://example.com/newer.zip',
//           version: '2.1.0',
//           createdAt: DateTime(2024, 12, 1),
//           fileSize: 524288,
//           checksum: 'sha256:newer',
//           buildDate: '2024-12-01',
//           hardwareVersion: '1.0',
//         );
//
//         // Act & Assert
//         expect(newerFirmware.isNewerThan(olderFirmware), true);
//         expect(olderFirmware.isNewerThan(newerFirmware), false);
//       });
//
//       test('should correctly check compatibility', () {
//         // Arrange
//         final firmware = OtaFirmwareModel(
//           firmwareId: 'test',
//           deviceType: 'juno_v1',
//           downloadUrl: 'https://example.com/test.zip',
//           version: '2.1.0',
//           createdAt: DateTime(2024, 12, 1),
//           fileSize: 524288,
//           checksum: 'sha256:test',
//           buildDate: '2024-12-01',
//           hardwareVersion: '1.0',
//           minBootloaderVersion: '1.0.0',
//         );
//
//         // Act & Assert
//         expect(
//             firmware.isCompatibleWith(
//               deviceType: 'juno_v1',
//               hardwareVersion: '1.0',
//               bootloaderVersion: '1.0.0',
//             ),
//             true);
//
//         expect(
//             firmware.isCompatibleWith(
//               deviceType: 'juno_v2',
//               hardwareVersion: '1.0',
//             ),
//             false);
//
//         expect(
//             firmware.isCompatibleWith(
//               deviceType: 'juno_v1',
//               hardwareVersion: '2.0',
//             ),
//             false);
//       });
//     });
//   });
// }
