# OTA Firmware Firestore Collection Structure

## Collection: `ota_firmwares`

This collection stores metadata for OTA firmware files that are hosted in Firebase Storage.

### Document Structure

Each document in the `ota_firmwares` collection represents a single firmware version:

```json
{
  "firmwareId": "juno_v1_2_1_0_20241201",
  "deviceType": "juno_v1",
  "downloadUrl": "https://firebasestorage.googleapis.com/v0/b/project-id/o/firmware%2Fjuno_v1_2_1_0.zip?alt=media&token=...",
  "version": "2.1.0",
  "createdAt": "2024-12-01T10:30:00Z",
  "fileSize": 524288,
  "checksum": "sha256:a1b2c3d4e5f6...",
  "buildDate": "2024-12-01",
  "hardwareVersion": "1.0",
  "isStable": true,
  "releaseNotes": "Bug fixes and performance improvements",
  "minBootloaderVersion": "1.0.0",
  "metadata": {
    "buildNumber": "12345",
    "gitCommit": "abc123def456",
    "buildEnvironment": "production"
  }
}
```

### Field Descriptions

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `firmwareId` | string | Yes | Unique identifier for the firmware (document ID) |
| `deviceType` | string | Yes | Device type this firmware is compatible with |
| `downloadUrl` | string | Yes | Firebase Storage download URL for the firmware file |
| `version` | string | Yes | Firmware version string (e.g., "2.1.0") |
| `createdAt` | timestamp | Yes | When this firmware was created/uploaded |
| `fileSize` | number | Yes | File size in bytes |
| `checksum` | string | Yes | SHA-256 checksum for integrity verification |
| `buildDate` | string | Yes | Build date of the firmware |
| `hardwareVersion` | string | Yes | Compatible hardware version |
| `isStable` | boolean | Yes | Whether this is a stable/production release |
| `releaseNotes` | string | No | Release notes or description |
| `minBootloaderVersion` | string | No | Minimum required bootloader version |
| `metadata` | map | No | Additional metadata (extensible) |

### Indexes

Create the following composite indexes for optimal query performance:

1. **Device Type + Stable + Created Date (Descending)**
   ```
   Collection: ota_firmwares
   Fields: deviceType (Ascending), isStable (Ascending), createdAt (Descending)
   ```

2. **Device Type + Created Date (Descending)**
   ```
   Collection: ota_firmwares
   Fields: deviceType (Ascending), createdAt (Descending)
   ```

### Security Rules

Add the following security rules to your `firestore.rules` file:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // OTA firmware collection - read-only for authenticated users
    match /ota_firmwares/{firmwareId} {
      // Allow read access for authenticated users
      allow read: if request.auth != null;
      
      // Only allow write access for admin users or cloud functions
      allow write: if request.auth != null && 
        (request.auth.token.admin == true || 
         request.auth.token.firebase.sign_in_provider == 'custom');
    }
  }
}
```

### Sample Queries

#### Get Latest Stable Firmware for Device Type
```dart
final query = FirebaseFirestore.instance
    .collection('ota_firmwares')
    .where('deviceType', isEqualTo: 'juno_v1')
    .where('isStable', isEqualTo: true)
    .orderBy('createdAt', descending: true)
    .limit(1);
```

#### Get All Firmware Versions for Device Type
```dart
final query = FirebaseFirestore.instance
    .collection('ota_firmwares')
    .where('deviceType', isEqualTo: 'juno_v1')
    .orderBy('createdAt', descending: true)
    .limit(10);
```

#### Get Specific Firmware by ID
```dart
final doc = await FirebaseFirestore.instance
    .collection('ota_firmwares')
    .doc('juno_v1_2_1_0_20241201')
    .get();
```

### Device Types

Currently supported device types:
- `juno_v1` - First generation Juno device

Future device types can be added as needed:
- `juno_v2` - Second generation Juno device
- `juno_pro` - Professional version
- etc.

### Firmware Upload Process

1. Upload firmware file to Firebase Storage under `/firmware/{deviceType}/` path
2. Generate SHA-256 checksum of the uploaded file
3. Create document in `ota_firmwares` collection with metadata
4. Set appropriate security permissions on the Storage file

### Best Practices

1. **Naming Convention**: Use consistent firmware ID format: `{deviceType}_{version}_{date}`
2. **Versioning**: Use semantic versioning (e.g., 2.1.0)
3. **Checksums**: Always verify file integrity using SHA-256
4. **Cleanup**: Implement cleanup process for old firmware versions
5. **Testing**: Test firmware thoroughly before marking as stable
6. **Rollback**: Keep previous stable versions available for rollback scenarios

### Storage Structure

Firebase Storage structure for firmware files:

```
/firmware/
  ├── juno_v1/
  │   ├── juno_v1_2_0_0.zip
  │   ├── juno_v1_2_1_0.zip
  │   └── juno_v1_2_2_0_beta.zip
  └── juno_v2/
      └── juno_v2_1_0_0.zip
```

### Error Handling

The system handles the following error scenarios:
- Network connectivity issues
- File corruption (checksum mismatch)
- Incompatible firmware versions
- Storage quota exceeded
- Authentication failures

### Monitoring

Consider implementing monitoring for:
- Download success/failure rates
- Update completion rates
- Device compatibility issues
- Storage usage and costs
