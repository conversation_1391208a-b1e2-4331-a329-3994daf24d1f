name: bluetooth
description: "bluetooth"
version: 0.0.1


environment:
  sdk: '>=3.4.0 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  remote:
    path: ../remote
  json_annotation:
  google_fonts: ^6.3.1
  flutter_screenutil:
  freezed_annotation:
  flutter_bloc:
  injector:
  injectable:
  fpdart: ^2.0.0-dev.3
  uuid:
  bloc_test: ^10.0.0
  mocktail:
  flutter_blue_plus: ^1.35.5
  permission_handler: ^12.0.1
  shared_preferences: ^2.2.3
  path_provider: ^2.1.1
  archive: ^3.4.10
  mcumgr_flutter: ^0.4.2
  fluttertoast: ^8.2.12
  cloud_firestore: ^6.0.1
  firebase_storage: ^13.0.1
  http: ^1.1.0
  crypto: ^3.0.3

dev_dependencies:
  melos: ^3.1.1
  build_runner:
  json_serializable:
  freezed:
  injectable_generator:

  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
