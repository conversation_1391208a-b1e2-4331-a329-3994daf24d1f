/// Test script to verify that onboarding creates both period dates and ovulation dates
/// This script simulates the onboarding process and provides expected data for verification
void main() async {
  print('🧪 Testing onboarding ovulation calculation...');

  // Test data
  final lastPeriodDate = DateTime(2025, 1, 15); // January 15, 2025
  const cycleLength = 28;
  const periodLength = 5;

  print('📅 Test data:');
  print('   Last period date: $lastPeriodDate');
  print('   Cycle length: $cycleLength days');
  print('   Period length: $periodLength days');

  // Expected period dates: January 15-19, 2025
  final expectedPeriodDates = <DateTime>{};
  for (int i = 0; i < periodLength; i++) {
    expectedPeriodDates.add(lastPeriodDate.add(Duration(days: i)));
  }

  print('📅 Expected period dates: $expectedPeriodDates');

  // Expected ovulation dates calculation:
  // Next period would be: January 15 + 28 = February 12, 2025
  // Ovulation peak: February 12 - 14 = January 29, 2025
  // Ovulation window: January 27-31, 2025 (±2 days from peak)
  final nextPeriodStart = lastPeriodDate.add(Duration(days: cycleLength));
  final ovulationPeak = nextPeriodStart.subtract(const Duration(days: 14));
  final expectedOvulationDates = <DateTime>{};

  for (int i = -2; i <= 2; i++) {
    final ovulationDate = ovulationPeak.add(Duration(days: i));
    // Only add if it's after the period ends
    final periodEndDate = lastPeriodDate.add(Duration(days: periodLength));
    if (ovulationDate.isAfter(periodEndDate)) {
      expectedOvulationDates.add(ovulationDate);
    }
  }

  print('🥚 Expected ovulation dates: $expectedOvulationDates');
  print('🥚 Ovulation peak date: $ovulationPeak');

  // Instructions for manual testing
  print('\n📋 Manual Testing Instructions:');
  print('1. Run the app and go through onboarding');
  print(
      '2. Set the last period date to: ${lastPeriodDate.toString().split(' ')[0]}');
  print('3. Set cycle length to: $cycleLength days');
  print('4. Set period length to: $periodLength days');
  print('5. Complete onboarding');
  print('6. Check Firestore for period_tracking collection');
  print('7. Verify both period dates AND ovulation dates are created');

  print('\n🔍 Expected Firestore Structure:');
  print('period_tracking/{user_id}/years/2025/months/2025_01/days/');
  print('  - "15": {isPeriodDate: true, isOvulationDate: false}');
  print('  - "16": {isPeriodDate: true, isOvulationDate: false}');
  print('  - "17": {isPeriodDate: true, isOvulationDate: false}');
  print('  - "18": {isPeriodDate: true, isOvulationDate: false}');
  print('  - "19": {isPeriodDate: true, isOvulationDate: false}');
  print('  - "27": {isPeriodDate: false, isOvulationDate: true}');
  print('  - "28": {isPeriodDate: false, isOvulationDate: true}');
  print('  - "29": {isPeriodDate: false, isOvulationDate: true}');
  print('  - "30": {isPeriodDate: false, isOvulationDate: true}');
  print('  - "31": {isPeriodDate: false, isOvulationDate: true}');

  print(
      '\n✅ If you see both period dates AND ovulation dates in Firestore, the fix is working!');
  print(
      '❌ If you only see period dates (no ovulation dates), the issue still exists.');
}
